package com.wolaidai.webot.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class SmsUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientUtil.class);

    public static boolean sendSms(String url, String mobile, String templateName, String userId, String secretkey, Map<String, String> params){
        long now = System.currentTimeMillis();
        JSONObject request = new JSONObject();
        request.put("mobile",mobile);
        request.put("timestamp",now);
        request.put("userId",userId);
        request.put("templateName",templateName);
        request.put("appTags","wecall");
        String encryptStr=mobile+secretkey+now;
        String sign= DigestUtils.md5Hex(encryptStr).toUpperCase();
        request.put("sign",sign);
        if(params!=null&&!params.isEmpty()){
            request.put("replaceField", JSON.toJSONString(params));
        }
        String response;
        try {
            response = HttpClientUtil.post(url, JSON.toJSONString(request), 10000);
        } catch (Exception e) {
            LOGGER.error("send sms error,mobile:{}",mobile,e);
            return false;
        }
        LOGGER.info("send sms finished,cost:{}ms", System.currentTimeMillis()-now);
        if (StringUtils.isNotBlank(response)) {
            JSONObject responseJson = JSONObject.parseObject(response);
            if(responseJson.containsKey("code")){
                if(responseJson.getInteger("code")==0){
                    return true;
                }
                LOGGER.error("send sms fail,code:{},errorMsg:{}",responseJson.getInteger("code"),responseJson.getString("message"));
            }
        }
        return false;
    }
}
