
package com.wolaidai.webot.common.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {
    
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    public static boolean isEmpty(String str) {
        if (str == null || str.trim().length() <= 0) {
            return true;
        }
        return false;
    }

    public static String unicodeToString(String str) {
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(str);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            str = str.replace(matcher.group(1), ch + "");
        }
        return str;
    }
    
    public static boolean isValidPhone(String str) {
        if (null != str) {
            return PHONE_PATTERN.matcher(str).find();
        }
        return false;
    }

    public static String valueOf(Object obj, String def) {
        if (obj == null) {
            return def;
        }
        return String.valueOf(obj.toString());
    }
}
