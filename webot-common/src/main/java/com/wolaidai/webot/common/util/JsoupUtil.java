package com.wolaidai.webot.common.util;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Whitelist;

public class JsoupUtil {
    public static String cleanHtml(String html) {
        if (StringUtils.isBlank(html)) return "";
        Document.OutputSettings outputSettings = new Document.OutputSettings();
        outputSettings.prettyPrint(false);
        return Jsoup.clean(html, "", new Whitelist().addTags("p", "a").addAttributes("a", "href", "target", "title"), outputSettings);
    }

    public static String text(String html) {
        if (StringUtils.isBlank(html)) return "";
        Document doc = Jsoup.parse(html);
        return doc.text();
    }
}
