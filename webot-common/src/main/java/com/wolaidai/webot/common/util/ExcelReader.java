package com.wolaidai.webot.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;

public class ExcelReader implements BaseReader {
    private static final Logger logger = LoggerFactory.getLogger(ExcelReader.class);
    private Workbook workbook;
    private Sheet currentSheet;
    private int currentSheetIndex;
    private Map<Integer, Iterator<Row>> rowIteratorMap = new HashMap<>();
    private Map<Integer, String[]> headersMap = new HashMap<>();
    private Iterator<Row> currentRowIterator;
    private int totalSheet;
    private int totalCount;
    private boolean hasHeaders = true;
    private DataFormatter objDefaultFormat = new DataFormatter(Locale.US);

    public int getTotalSheet() {
        return totalSheet;
    }

    public void reset() {
        rowIteratorMap.clear();
        headersMap.clear();
    }

    public void switchSheet(int sheetIndex) {
        if(sheetIndex >= getTotalSheet()){
           throw new IllegalArgumentException("sheet index error");
        }
        currentSheetIndex = sheetIndex;
        currentSheet = workbook.getSheetAt(sheetIndex);
        Iterator<Row> rowIterator = rowIteratorMap.get(sheetIndex);
        if (rowIterator != null) {
            currentRowIterator = rowIterator;
        } else {
            rowIterator = currentSheet.rowIterator();
            if (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                short lastCellNum = row.getLastCellNum();
                if (lastCellNum > 0) {
                    String[] values = new String[lastCellNum];
                    for (int i = 0; i < lastCellNum; i++) {
                        values[i] = objDefaultFormat.formatCellValue(row.getCell(i));
                    }
                    headersMap.put(sheetIndex, values);
                }
            }
            currentRowIterator = rowIterator;
            rowIteratorMap.put(sheetIndex, rowIterator);
        }
    }

    public ExcelReader(String fileName) throws IOException {
        logger.info("read excel file name:{}", fileName);
        if (StringUtils.endsWithIgnoreCase(fileName, ".xlsx")) {
            workbook = new XSSFWorkbook(fileName);
        } else if (StringUtils.endsWithIgnoreCase(fileName, ".xls")) {
            workbook = new HSSFWorkbook(new FileInputStream(fileName));
        } else {
            logger.info("file must be excel file,file name:{}", fileName);
            throw new RuntimeException("file must be excel file");
        }
        totalSheet = workbook.getNumberOfSheets();
        for (int i = 0; i < totalSheet; i++) {
            switchSheet(i);
            totalCount += currentSheet.getLastRowNum();
        }
        switchSheet(0);
    }

    @Override
    public int getHeaderCount() {
        return currentSheet.getRow(0).getLastCellNum();
    }

    @Override
    public boolean hasNext() {
        return currentRowIterator.hasNext();
    }

    @Override
    public boolean isHasHeaders() {
        return hasHeaders;
    }

    @Override
    public String[] getColumnValues() {
        Row row = currentRowIterator.next();
        short lastCellNum = row.getLastCellNum();
        boolean hasData = false;
        if (lastCellNum > 0) {
            String[] values = new String[lastCellNum];
            for (int i = 0; i < lastCellNum; i++) {
                String cellValue = objDefaultFormat.formatCellValue(row.getCell(i));
                values[i] = cellValue;
                if(!hasData&&StringUtils.isNotBlank(cellValue)){
                    hasData = true;
                }
            }
            return hasData?values:new String[]{};
        }
        return new String[]{};
    }

    @Override
    public String[] getHeaders() {
        return headersMap.get(currentSheetIndex);
    }

    @Override
    public void close() {
        try {
            workbook.close();
        } catch (IOException e) {

        }
    }

    public String currentSheetName() {
        return currentSheet.getSheetName();
    }

    public int getCurrentCount() {
        return currentSheet.getLastRowNum();
    }

    @Override
    public int getTotalCount() {
        return totalCount;
    }

    @Override
    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

}
