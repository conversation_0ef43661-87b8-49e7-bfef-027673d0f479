package com.wolaidai.webot.common.util;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;

public class BaseCsvReader extends CsvReader implements BaseReader {

    private boolean hasHeaders = true;
    private boolean initTotalCount = false;
    private int totalCount = 0;
    private String tmpfileName;

    public BaseCsvReader(String fileName, Charset charset) throws IOException {
        super(fileName, Letters.COMMA, charset);
        this.tmpfileName = fileName;
        initHeaders();
    }

    public BaseCsvReader(InputStream inputStream, Charset charset) throws IOException {
        super(inputStream, Letters.COMMA, charset);
        initHeaders();
    }

    @Override
    public boolean readHeaders() {
        throw new RuntimeException("Not supported. Use getHeaders() to retrive csv file header.");
    }

    private void initHeaders() throws IOException {
        boolean result = readRecord();

        headersHolder.Length = getColumnCount();

        headersHolder.Headers = new String[getColumnCount()];

        if (hasHeaders) {
            for (int i = 0; i < headersHolder.Length; i++) {
                String columnValue = get(i);

                headersHolder.Headers[i] = columnValue;

                // if there are duplicate header names, we will save the last
                // one
                headersHolder.IndexByName.put(columnValue, new Integer(i));
            }
        } else {
            if (result) {
                rollbackCurrentRecord();
            }
            resetDataBuffer();
        }

        resetColumnCount();
    }

    public boolean isHasHeaders() {
        return hasHeaders;
    }

    @Override
    public boolean hasNext() throws IOException {
        return readRecord();
    }

    @Override
    public String[] getColumnValues() throws IOException {
        return getValues();
    }

    @Override
    public int getTotalCount() {
        if (initTotalCount) {
            return totalCount;
        }
        BaseCsvReader riCsvReader = null;
        try {
            riCsvReader = new BaseCsvReader(tmpfileName, getCharset());
            while (riCsvReader.hasNext()) {
                totalCount++;
            }
        } catch (IOException e) {
            initTotalCount = true;
            totalCount = 0;
            return totalCount;
        } finally {
            if (riCsvReader != null) {
                riCsvReader.close();
            }
        }
        initTotalCount = true;
        return totalCount;
    }

	@Override
	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}

}
