package com.wolaidai.webot.common.util;

import org.apache.commons.lang3.time.DateUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {

    public static Date parseDate(String dateString, String format) {
        try {
            return DateUtils.parseDate(dateString, format);
        } catch (Exception e) {
            return null;
        }
    }
    
    public static String formatSecondsTime(Number _seconds, String def) {
        if (null == _seconds) {
            return def;
        }
        long seconds = _seconds.longValue();
        long h = seconds / 3600;
        long m = (long) ((seconds % 3600) / 60);
        long s = (long) ((seconds % 3600) % 60);
        StringBuilder sb = new StringBuilder();
        sb.append(h > 9 ? h : ("0" + h)).append(":").append(m > 9 ? m : ("0" + m)).append(":").append(s > 9 ? s : ("0" + s));
        return sb.toString();
    }
    
    public static String formatDateTime(Date date, String def) {
        if (null == date) {
            return def;
        }
        return new SimpleDateFormat("HH:mm:ss").format(date);
    }
    
    public static boolean isNowBetween(String startTime, String endTime) {
        if (startTime != null && endTime != null) {
            LocalDateTime now = LocalDateTime.now();
            String[] start = startTime.split(":");
            String[] end = endTime.split(":");
            try {
                return now.isAfter(LocalDateTime.of(now.toLocalDate(), LocalTime.of(Integer.valueOf(start[0]), Integer.valueOf(start[1])))) && now.isBefore(LocalDateTime.of(now.toLocalDate(), LocalTime.of(Integer.valueOf(end[0]), Integer.valueOf(end[1]))));
            } catch (Exception e) {
            }
        }
        return false;
    }
    
    public static ArrayList<Date[]> splitDateByDay(Date startTime, Date endTime) {
        ArrayList<Date[]> list = new ArrayList<>();
        Calendar c = Calendar.getInstance();
        c.setTime(startTime);
        int startDay = c.get(Calendar.DAY_OF_YEAR);
        c.setTime(endTime);
        int endDay = c.get(Calendar.DAY_OF_YEAR);
        if (endTime.after(DateUtils.truncate(endTime, Calendar.DATE))) {
            endDay += 1;
        }
        c.setTime(startTime);
        Date start = DateUtils.truncate(c.getTime(), Calendar.DATE);
        for (int i = startDay; i < endDay; i++) {
            c.add(Calendar.DAY_OF_YEAR, 1);
            Date end = DateUtils.truncate(c.getTime(), Calendar.DATE);
            list.add(new Date[] { start, end });
            start = end;

        }
        return list;
    }
}
