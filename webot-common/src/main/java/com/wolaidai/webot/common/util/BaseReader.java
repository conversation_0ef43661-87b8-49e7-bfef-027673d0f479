package com.wolaidai.webot.common.util;

import java.io.IOException;

public interface BaseReader {
    int getHeaderCount();

    boolean hasNext() throws IOException;

    String[] getColumnValues() throws IOException;

    boolean isHasHeaders();

    String[] getHeaders() throws IOException;

    void close() throws IOException;

    int getTotalCount();
    
    void setTotalCount(int totalCount);
}
