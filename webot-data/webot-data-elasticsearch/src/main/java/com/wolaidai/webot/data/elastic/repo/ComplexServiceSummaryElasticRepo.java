package com.wolaidai.webot.data.elastic.repo;

import java.util.Date;
import java.util.List;

import com.wolaidai.webot.data.elastic.entity.ServiceSummaryElasticEntity;
import com.wolaidai.webot.data.elastic.entity.ServiceSummaryReportEntity;

public interface ComplexServiceSummaryElasticRepo {
    List<ServiceSummaryReportEntity> findServiceSummaries(Integer orgId, Integer businessId, Integer clientTypeId, Date beginTime, Date endTime);

    ServiceSummaryElasticEntity findLatestServiceSummary(Integer sessionId);
}
