package com.wolaidai.webot.data.elastic.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties("elastic")
public class ElasticPropertyConfig {
    private String esClusterName;
    private String esSecurityUser;
    private String esXpackKeyPath;
    private String esXpackKeyPassword;
    private String esClusterNodes;

    public String getEsClusterName() {
        return esClusterName;
    }

    public void setEsClusterName(String esClusterName) {
        this.esClusterName = esClusterName;
    }

    public String getEsSecurityUser() {
        return esSecurityUser;
    }

    public void setEsSecurityUser(String esSecurityUser) {
        this.esSecurityUser = esSecurityUser;
    }

    public String getEsXpackKeyPath() {
        return esXpackKeyPath;
    }

    public void setEsXpackKeyPath(String esXpackKeyPath) {
        this.esXpackKeyPath = esXpackKeyPath;
    }

    public String getEsXpackKeyPassword() {
        return esXpackKeyPassword;
    }

    public void setEsXpackKeyPassword(String esXpackKeyPassword) {
        this.esXpackKeyPassword = esXpackKeyPassword;
    }

    public String getEsClusterNodes() {
        return esClusterNodes;
    }

    public void setEsClusterNodes(String esClusterNodes) {
        this.esClusterNodes = esClusterNodes;
    }
}

