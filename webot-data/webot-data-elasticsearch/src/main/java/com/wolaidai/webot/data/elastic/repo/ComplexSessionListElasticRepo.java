package com.wolaidai.webot.data.elastic.repo;

import com.wolaidai.webot.data.elastic.entity.SessionListElasticEntity;
import com.wolaidai.webot.data.elastic.model.LineElasticModel;

import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.SearchHits;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ComplexSessionListElasticRepo {

    SearchHits<SessionListElasticEntity> findRecordByParams(Integer orgId, String email, String customerName, Integer mark,
                                                      String content, Integer serviceSummaryStatus, Date startTime, Date endTime, List<Integer> businessIds,
                                                            List<Integer> clientTypeIds, List<Integer> appraiseLevels, String uuid, Integer userId, String mobile, Pageable pageable);

    SearchHits<SessionListElasticEntity> findRecordByParams(Integer orgId, List<String> emails, List<Integer> businessIds,
                                                            List<Integer> clientTypeIds, List<Integer> appraiseLevels, String content,
                                                            Integer hasPic, Set<String> respKeys, Date startTime, Date endTime,
                                                            String uuid, Integer userId, String mobile, String customerName,
                                                            List<Integer> includeSessionIds, List<Integer> excludeSessionIds, Pageable pageable);

    SearchHits<SessionListElasticEntity> findRecordByVisitor(Integer orgId, String clientId, String content, Date startTime, Date endTime, Pageable pageable);

    SearchHits<SessionListElasticEntity> findRecordByMobile(Integer orgId, String mobile, String content, Date startTime, Date endTime, Pageable pageable);

    long countSessionCnt(Integer orgId, String clientId, Date startTime, Date endTime);

    long countSessionByMobileCnt(Integer orgId, String mobile, Date startTime, Date endTime);

    long countSessionCnt(Integer orgId, String clientId, Date endTime);

    long countSessionByMobileCnt(Integer orgId, String mobile, Date endTime);

    LineElasticModel offlineSessionCountReport(Integer orgId, Collection<Integer> sessionIds, Date startTime, Date endTime, String interval);

    SearchHits<SessionListElasticEntity> findRecordByUserId(Integer userId);

    SearchHits<SessionListElasticEntity> findRecordByUserIdAndTime(Integer orgId, Integer userId, Date startTime, Date endTime);
}
