package com.wolaidai.webot.data.elastic.repo.impl;

import com.wolaidai.webot.data.elastic.entity.ChatHistoryElasticEntity;
import com.wolaidai.webot.data.elastic.entity.SessionListElasticEntity;
import com.wolaidai.webot.data.elastic.model.LineElasticModel;
import com.wolaidai.webot.data.elastic.model.LineElasticModel.LineChartSeries;
import com.wolaidai.webot.data.elastic.model.LineElasticModel.LineChatCount;
import com.wolaidai.webot.data.elastic.repo.ComplexSessionListElasticRepo;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.InternalDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.StringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.InternalValueCount;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

@Repository
public class ComplexSessionListElasticRepoImpl implements ComplexSessionListElasticRepo {

    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;

    @Override
    public SearchHits<SessionListElasticEntity> findRecordByParams(Integer orgId, String email, String customerName, Integer mark,
                                                                   String content, Integer serviceSummaryStatus, Date startTime, Date endTime, List<Integer> businessIds,
                                                                   List<Integer> clientTypeIds, List<Integer> appraiseLevels, String uuid, Integer userId, String mobile, Pageable pageable) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if (!CollectionUtils.isEmpty(businessIds)) {
            boolQueryBuilder.filter(new TermsQueryBuilder("businessId", businessIds));
        }
        if (!CollectionUtils.isEmpty(clientTypeIds)) {
            boolQueryBuilder.filter(new TermsQueryBuilder("clientTypeId", clientTypeIds));
        }
        if (!CollectionUtils.isEmpty(appraiseLevels)) {
            if (appraiseLevels.contains(0)) {
                BoolQueryBuilder orQueryBuilder = new BoolQueryBuilder();
                appraiseLevels.add(-1);
                orQueryBuilder.should(new TermsQueryBuilder("satisfactionLevel", appraiseLevels));
                orQueryBuilder.should(new BoolQueryBuilder().mustNot(new ExistsQueryBuilder("satisfactionLevel")));
                boolQueryBuilder.filter(orQueryBuilder);
            } else {
                boolQueryBuilder.filter(new TermsQueryBuilder("satisfactionLevel", appraiseLevels));
            }
        }
        if (StringUtils.hasText(email)) {
            BoolQueryBuilder keyBoolQueryBuilder = new BoolQueryBuilder();
            keyBoolQueryBuilder.should(new TermQueryBuilder("serviceUser.keyword", email));
            keyBoolQueryBuilder.should(new TermQueryBuilder("fromServiceUser.keyword", email));
            keyBoolQueryBuilder.should(new TermQueryBuilder("lastServiceUser.keyword", email));
            keyBoolQueryBuilder.should(new TermQueryBuilder("transferToUsers.keyword", email));
            boolQueryBuilder.filter(keyBoolQueryBuilder);
        }
        if (StringUtils.hasText(customerName)) {
            boolQueryBuilder.filter(new MatchPhraseQueryBuilder("customerName", customerName));
        }
        if (null != userId) {
            boolQueryBuilder.filter(new TermQueryBuilder("customerDetail.customers.userId", userId));
        }
        if (StringUtils.hasText(uuid)) {
            boolQueryBuilder.filter(new TermQueryBuilder("customerDetail.customers.uuid.keyword", uuid));
        }
        if (StringUtils.hasText(mobile)) {
            boolQueryBuilder.filter(new TermQueryBuilder("customerDetail.customers.mobile.keyword", mobile));
        }
        if (mark != null) {
            boolQueryBuilder.filter(new TermQueryBuilder("mark", mark));
        }
        if (serviceSummaryStatus != null) {
            boolQueryBuilder.filter(new TermQueryBuilder("serviceSummaryStatus", serviceSummaryStatus));
        }
        Set<String> gcIds = findGcIds(orgId, businessIds, null, clientTypeIds, content, null, startTime, endTime);
        return commonQuery(gcIds, orgId, null, content, startTime, endTime, true, pageable, boolQueryBuilder);
    }

    @Override
    public SearchHits<SessionListElasticEntity> findRecordByParams(Integer orgId, List<String> emails, List<Integer> businessIds,
                                                                   List<Integer> clientTypeIds, List<Integer> appraiseLevels, String content,
                                                                   Integer hasPic, Set<String> respKeys, Date startTime, Date endTime,
                                                                   String uuid,Integer userId,String mobile,String customerName,
                                                                   List<Integer> includeSessionIds, List<Integer> excludeSessionIds, Pageable pageable) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if (!CollectionUtils.isEmpty(businessIds)) {
            boolQueryBuilder.filter(new TermsQueryBuilder("businessId", businessIds));
        }
        if (!CollectionUtils.isEmpty(clientTypeIds)) {
            boolQueryBuilder.filter(new TermsQueryBuilder("clientTypeId", clientTypeIds));
        }
        if (!CollectionUtils.isEmpty(appraiseLevels)) {
            if (appraiseLevels.contains(0)) {
                BoolQueryBuilder orQueryBuilder = new BoolQueryBuilder();
                appraiseLevels.add(-1);
                orQueryBuilder.should(new TermsQueryBuilder("satisfactionLevel", appraiseLevels));
                orQueryBuilder.should(new BoolQueryBuilder().mustNot(new ExistsQueryBuilder("satisfactionLevel")));
                boolQueryBuilder.filter(orQueryBuilder);
            } else {
                boolQueryBuilder.filter(new TermsQueryBuilder("satisfactionLevel", appraiseLevels));
            }
        }
        if (!CollectionUtils.isEmpty(emails)) {
            BoolQueryBuilder keyBoolQueryBuilder = new BoolQueryBuilder();
            keyBoolQueryBuilder.should(new TermsQueryBuilder("serviceUser.keyword", emails));
            keyBoolQueryBuilder.should(new TermsQueryBuilder("fromServiceUser.keyword", emails));
            keyBoolQueryBuilder.should(new TermsQueryBuilder("lastServiceUser.keyword", emails));
            keyBoolQueryBuilder.should(new TermsQueryBuilder("transferToUsers.keyword", emails));
            boolQueryBuilder.filter(keyBoolQueryBuilder);
        }
        if (!CollectionUtils.isEmpty(respKeys)) {
            for (String key : respKeys) {
                if ("firstRespTimeout".equals(key) || "avgRespTimeout".equals(key) || "respTimeout".equals(key)) {
                    boolQueryBuilder.must(new TermQueryBuilder(key, 1));
                }
            }
        }
        if (StringUtils.hasText(customerName)) {
            boolQueryBuilder.filter(new MatchPhraseQueryBuilder("customerName", customerName));
        }
        if (null != userId) {
            boolQueryBuilder.filter(new TermQueryBuilder("customerDetail.customers.userId", userId));
        }
        if (StringUtils.hasText(uuid)) {
            boolQueryBuilder.filter(new TermQueryBuilder("customerDetail.customers.uuid.keyword", uuid));
        }
        if (StringUtils.hasText(mobile)) {
            boolQueryBuilder.filter(new TermQueryBuilder("customerDetail.customers.mobile.keyword", mobile));
        }
        if (!CollectionUtils.isEmpty(includeSessionIds)) {
            boolQueryBuilder.filter(new TermsQueryBuilder("_id", includeSessionIds));
        }
        if (!CollectionUtils.isEmpty(excludeSessionIds)) {
            boolQueryBuilder.mustNot(new TermsQueryBuilder("_id", excludeSessionIds));
        }

        Set<String> gcIds = findGcIds(orgId, businessIds, null, clientTypeIds, content, hasPic, startTime, endTime);
        return commonQuery(gcIds, orgId, hasPic, content, startTime, endTime, true, pageable, boolQueryBuilder);
    }

    @Override
    public SearchHits<SessionListElasticEntity> findRecordByVisitor(Integer orgId, String clientId, String content,
                                                                   Date startTime, Date endTime, Pageable pageable) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("clientId", clientId));
        boolQueryBuilder.filter(new BoolQueryBuilder().mustNot(new ExistsQueryBuilder("customerDetail.customers.mobile")));
        Set<String> gcIds = findGcIds(orgId, null, clientId, null, content, null, startTime, endTime);
        return commonQuery(gcIds, orgId, null, content, startTime, endTime, false, pageable, boolQueryBuilder);
    }

    @Override
    public SearchHits<SessionListElasticEntity> findRecordByMobile(Integer orgId, String mobile, String content,
                                                                   Date startTime, Date endTime, Pageable pageable) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        boolQueryBuilder.filter(new TermQueryBuilder("customerDetail.customers.mobile.keyword", mobile));
        Set<String> gcIds = findGcIds(orgId, null, null, null, content, null, startTime, endTime);
        return commonQuery(gcIds, orgId, null, content, startTime, endTime, false, pageable, boolQueryBuilder);
    }

    @Override
    public long countSessionCnt(Integer orgId, String clientId, Date startTime, Date endTime) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        boolQueryBuilder.filter(new TermQueryBuilder("clientId", clientId));
        boolQueryBuilder.filter(new BoolQueryBuilder().mustNot(new ExistsQueryBuilder("customerDetail.customers.mobile")));
        if (startTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("queueTime");
            rangeQueryBuilder.gte(startTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        if (endTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("queueTime");
            rangeQueryBuilder.lte(endTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        return elasticsearchTemplate.count(new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build(), SessionListElasticEntity.class);
    }

    @Override
    public long countSessionByMobileCnt(Integer orgId, String mobile, Date startTime, Date endTime) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        boolQueryBuilder.filter(new TermQueryBuilder("customerDetail.customers.mobile.keyword", mobile));
        if (startTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("queueTime");
            rangeQueryBuilder.gte(startTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        if (endTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("queueTime");
            rangeQueryBuilder.lte(endTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        return elasticsearchTemplate.count(new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build(), SessionListElasticEntity.class);

    }

    @Override
    public long countSessionCnt(Integer orgId, String clientId, Date endTime) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        boolQueryBuilder.filter(new TermQueryBuilder("clientId", clientId));
        boolQueryBuilder.filter(new BoolQueryBuilder().mustNot(new ExistsQueryBuilder("customerDetail.customers.mobile")));
        if (endTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            rangeQueryBuilder.lt(endTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        return elasticsearchTemplate.count(new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build(), SessionListElasticEntity.class);
    }

    @Override
    public long countSessionByMobileCnt(Integer orgId, String mobile, Date endTime) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        boolQueryBuilder.filter(new TermQueryBuilder("customerDetail.customers.mobile.keyword", mobile));
        if (endTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            rangeQueryBuilder.lt(endTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        return elasticsearchTemplate.count(new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build(), SessionListElasticEntity.class);

    }

    private Set<String> findGcIds(Integer orgId, List<Integer> businessIds, String clientId, List<Integer> clientTypeIds, String content, Integer hasPic, Date startTime, Date endTime){
        Set<String> globalCids = new HashSet<>();
        if (StringUtils.hasText(content)) {
            String trimContent = content.trim();
            String targetContent;
            if (trimContent.contains(" ")) {
                StringBuilder targetSb = new StringBuilder();
                for (String s : trimContent.split(" ")) {
                    targetSb.append("(*".concat(QueryParser.escape(s)).concat("*) OR "));
                }
                targetContent = targetSb.substring(0, targetSb.length() - 4);
            } else {
                targetContent = "*".concat(QueryParser.escape(trimContent)).concat("*");
            }
            BoolQueryBuilder historyQueryBuilder = getHistoryQueryBuilder(orgId, businessIds, clientId, clientTypeIds, startTime, endTime);
            historyQueryBuilder.filter(new QueryStringQueryBuilder("content.keyword:".concat(targetContent)));
            historyQueryBuilder.filter(new TermsQueryBuilder("type","text","menu"));
            globalCids = buildGlobalCidSet(historyQueryBuilder);
            if(globalCids.isEmpty()){
                return globalCids;
            }
        }
        if (hasPic != null) {
            BoolQueryBuilder historyQueryBuilder = getHistoryQueryBuilder(orgId, businessIds, clientId, clientTypeIds, startTime, endTime);
            if(!globalCids.isEmpty()){
                historyQueryBuilder.filter(new TermsQueryBuilder("gcid.keyword",globalCids));
            }
            historyQueryBuilder.filter(new TermQueryBuilder("type", ChatHistoryElasticEntity.TYPE_IMAGE));
            Set<String> globalCidSet = buildGlobalCidSet(historyQueryBuilder);
            //如果不包含图片，并且有文本搜索，则反向移除有图片记录
            if (hasPic==0&&!globalCids.isEmpty()) {
                globalCids.removeAll(globalCidSet);
            } else {
                globalCids = globalCidSet;
            }
        }
        return globalCids;
    }

    private Set<String> buildGlobalCidSet(BoolQueryBuilder historyQueryBuilder) {
        Set<String> globalCidSet = new HashSet<>();
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("globalCid").field("gcid.keyword").size(10000).order(BucketOrder.aggregation("topDate",false))
                .subAggregation(AggregationBuilders.max("topDate").field("date"));
        NativeSearchQuery historySearchQuery = new NativeSearchQueryBuilder().withQuery(historyQueryBuilder).withMaxResults(0).addAggregation(aggregationBuilder).build();
        SearchHits<ChatHistoryElasticEntity> searchHits = elasticsearchTemplate.search(historySearchQuery, ChatHistoryElasticEntity.class);
        Aggregations aggregations = (Aggregations) searchHits.getAggregations().aggregations();
        StringTerms globalCidTerms = aggregations.get("globalCid");
        if (globalCidTerms != null) {
            List<StringTerms.Bucket> gcidBuckets = globalCidTerms.getBuckets();
            for (StringTerms.Bucket termBucket : gcidBuckets) {
                globalCidSet.add(termBucket.getKeyAsString());
            }
        }
        return globalCidSet;
    }

    private BoolQueryBuilder getHistoryQueryBuilder(Integer orgId, List<Integer> businessIds, String clientId, List<Integer> clientTypeIds, Date startTime, Date endTime) {
        BoolQueryBuilder historyQueryBuilder = new BoolQueryBuilder();
        historyQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        if(StringUtils.hasText(clientId)){
            historyQueryBuilder.filter(new TermQueryBuilder("clientId", clientId));
        }
        if (!CollectionUtils.isEmpty(clientTypeIds)) {
            historyQueryBuilder.filter(new TermsQueryBuilder("clientType", clientTypeIds));
        }
        if(startTime !=null|| endTime !=null){
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("date");
            if (startTime != null) {
                rangeQueryBuilder.gte(startTime.getTime());
            }
            if (endTime != null) {
                rangeQueryBuilder.lte(endTime.getTime());
            }
            historyQueryBuilder.filter(rangeQueryBuilder);
        }
        if (!CollectionUtils.isEmpty(businessIds)) {
            historyQueryBuilder.filter(new TermsQueryBuilder("skillGroupId", businessIds));
        }
        return historyQueryBuilder;
    }

    /**
     *
     * @param globalCids
     * @param orgId
     * @param hasPic
     * @param startTime
     * @param endTime
     * @param defaultTimeRange
     * @param pageable
     * @param boolQueryBuilder
     * @return
     */
    private SearchHits<SessionListElasticEntity> commonQuery(Set<String> globalCids, Integer orgId, Integer hasPic, String content, Date startTime, Date endTime,
                                                             boolean defaultTimeRange, Pageable pageable, BoolQueryBuilder boolQueryBuilder) {
        if(!globalCids.isEmpty()){
            if(!StringUtils.hasText(content)&&Objects.equals(0,hasPic)){
                boolQueryBuilder.mustNot(new TermsQueryBuilder("gcid.keyword", globalCids));
            }else{
                boolQueryBuilder.filter(new TermsQueryBuilder("gcid.keyword", globalCids));
            }
        }else{
            if(StringUtils.hasText(content)||Objects.equals(1,hasPic)){
                return null;
            }
        }
        boolQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        //会话已完成状态
        boolQueryBuilder.filter(new TermQueryBuilder("status", SessionListElasticEntity.STATUS_OFFLINE));
        if (startTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            rangeQueryBuilder.gte(startTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        if (endTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            rangeQueryBuilder.lte(endTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        //时间都不传就默认最近一个月
        if (defaultTimeRange && startTime == null && endTime == null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            rangeQueryBuilder.gt("now-1M");
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        NativeSearchQuery query = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).withPageable(pageable).build();
        query.setTrackTotalHits(true);
        return elasticsearchTemplate.search(query, SessionListElasticEntity.class);
    }
    
    private DateHistogramAggregationBuilder extractDateHistogram(String interval, String dateParam) {
        DateHistogramAggregationBuilder dateInterval = AggregationBuilders.dateHistogram("dateInterval");
        dateInterval.fixedInterval(new DateHistogramInterval(interval));
        dateInterval.field(dateParam);
        dateInterval.format("yyyy-MM-dd");
        dateInterval.timeZone(ZoneId.of("+08:00"));
        return dateInterval;
    }
    
    @Override
    public LineElasticModel offlineSessionCountReport(Integer orgId, Collection<Integer> sessionIds, Date startTime, Date endTime, String interval) {
        LineElasticModel result = new LineElasticModel();
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        queryBuilder.filter(new TermQueryBuilder("status", 2));
        queryBuilder.must(new TermsQueryBuilder("_id", sessionIds));
        if (null != startTime || null != endTime) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            if (startTime != null) {
                rangeQueryBuilder.gte(startTime.getTime());
            }
            if (endTime != null) {
                rangeQueryBuilder.lt(endTime.getTime());
            }
            queryBuilder.filter(rangeQueryBuilder);
        }
        DateHistogramAggregationBuilder dateInterval = extractDateHistogram(interval, "createTime");
        result.setInterval(interval);
        dateInterval.subAggregation(AggregationBuilders.count("count").field("sessionKey"));
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(queryBuilder).withMaxResults(0).withAggregations(dateInterval).build();
        SearchHits<SessionListElasticEntity> searchHits = elasticsearchTemplate.search(searchQuery, SessionListElasticEntity.class);
        Aggregations aggregations = (Aggregations) searchHits.getAggregations().aggregations();
        List<LineChartSeries> lineChatData = new ArrayList<>();
        InternalDateHistogram aggData = aggregations.get("dateInterval");
        List<InternalDateHistogram.Bucket> buckets = aggData.getBuckets();
        long allTotalCount = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy/MM/dd");
        for (InternalDateHistogram.Bucket bucket : buckets) {
            ZonedDateTime dateTime = (ZonedDateTime) bucket.getKey();
            LineChartSeries lineChartSeries = new LineChartSeries(sdf.format(dateTime.plusHours(1).toEpochSecond() * 1000L));
            if (dateTime.getHour() == 23) {
                lineChartSeries.setGroupKey(sdf2.format(dateTime.toEpochSecond() * 1000L) + " 24:00");
            }
            InternalValueCount ivc = bucket.getAggregations().get("count");
            long count = ivc.getValue();
            lineChartSeries.getGroupValue().add(new LineChatCount("", count));
            lineChartSeries.setTotalCount(count);
            lineChatData.add(lineChartSeries);
            allTotalCount += count;
        }
        result.setTotalCount(allTotalCount);
        result.setSeriesData(lineChatData);
        return result;
    }

    @Override
    public SearchHits<SessionListElasticEntity> findRecordByUserId(Integer userId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
            .must(QueryBuilders.matchQuery("customerDetail.customers.userId", userId))
            .must(QueryBuilders.matchQuery("status", SessionListElasticEntity.STATUS_OFFLINE));
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
            .withQuery(boolQueryBuilder)
            .withSorts(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
            .build();
        return elasticsearchTemplate.search(searchQuery, SessionListElasticEntity.class);
    }

    @Override
    public SearchHits<SessionListElasticEntity> findRecordByUserIdAndTime(Integer orgId, Integer userId, Date startTime, Date endTime) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("orgId", orgId))
                .must(QueryBuilders.matchQuery("customerDetail.customers.userId", userId));
        if (startTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            rangeQueryBuilder.gte(startTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        if (endTime != null) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            rangeQueryBuilder.lte(endTime.getTime());
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withSorts(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .build();
        return elasticsearchTemplate.search(searchQuery, SessionListElasticEntity.class);
    }
}
