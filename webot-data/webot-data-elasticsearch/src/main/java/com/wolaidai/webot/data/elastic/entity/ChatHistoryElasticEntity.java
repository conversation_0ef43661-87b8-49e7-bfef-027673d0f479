package com.wolaidai.webot.data.elastic.entity;

import com.alibaba.fastjson.JSONObject;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;
import java.util.Map;

@Document(indexName = "chat_histories")
public class ChatHistoryElasticEntity {

    public final static String SENDER_TYPE_USER = "user";
    public final static String SENDER_TYPE_MANUAL = "manual";
    public final static String SENDER_TYPE_BOT = "bot";
    public final static String SENDER_TYPE_SYSTEM = "system";

    public final static String TYPE_TEXT = "text";
    public final static String TYPE_VIDEO = "video";
    public final static String TYPE_IMAGE = "image";
    public final static String TYPE_VOICE = "voice";
    public final static String TYPE_EVENT = "event";
    public final static String TYPE_FILE = "file";

    public final static String SCENE_TYPE_BOT = "bot";
    public final static String SCENE_TYPE_CS = "cs";


    @Id
    private String id;
    private Integer orgId;
    private Integer clientType;
    private String clientId;
    private String cid;
    private String gcid;
    private JSONObject manual;
    private Integer botId;
    private Integer skillGroupId;
    private String sender;
    private String type;
    private String content;
    private String msgId;
    private String scene;
    @Field(type= FieldType.Date,format = DateFormat.date_optional_time)
    private Date date;
    private JSONObject media;
    private JSONObject extend;
    private JSONObject extra;
    private boolean recall;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getGcid() {
        return gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public JSONObject getManual() {
        return manual;
    }

    public void setManual(JSONObject manual) {
        this.manual = manual;
    }

    public Integer getBotId() {
        return botId;
    }

    public void setBotId(Integer botId) {
        this.botId = botId;
    }

    public Integer getSkillGroupId() {
        return skillGroupId;
    }

    public void setSkillGroupId(Integer skillGroupId) {
        this.skillGroupId = skillGroupId;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public JSONObject getMedia() {
        return media;
    }

    public void setMedia(JSONObject media) {
        this.media = media;
    }

    public JSONObject getExtend() {
        return extend;
    }

    public void setExtend(JSONObject extend) {
        this.extend = extend;
    }

    public JSONObject getExtra() {
        return extra;
    }

    public void setExtra(JSONObject extra) {
        this.extra = extra;
    }

    public boolean isRecall() {
        return recall;
    }

    public void setRecall(boolean recall) {
        this.recall = recall;
    }
}
