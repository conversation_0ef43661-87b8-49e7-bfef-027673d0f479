package com.wolaidai.webot.data.elastic.repo.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.LongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;

import com.wolaidai.webot.data.elastic.entity.ServiceSummaryElasticEntity;
import com.wolaidai.webot.data.elastic.entity.ServiceSummaryReportEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexServiceSummaryElasticRepo;

@SuppressWarnings("deprecation")
@Repository
public class ComplexServiceSummaryElasticRepoImpl implements ComplexServiceSummaryElasticRepo {

    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;

    @Override
    public List<ServiceSummaryReportEntity> findServiceSummaries(Integer orgId, Integer businessId, Integer clientTypeId, Date beginTime, Date endTime) {
        List<ServiceSummaryReportEntity> result = new ArrayList<>();
        BoolQueryBuilder bqb = new BoolQueryBuilder();
        bqb.filter(new TermQueryBuilder("orgId", orgId));
        bqb.must(QueryBuilders.existsQuery("unitId"));
        if (null != businessId) {
            bqb.filter(new TermQueryBuilder("businessId", businessId));
        }
        if (null != clientTypeId) {
            bqb.filter(new TermQueryBuilder("clientTypeId", clientTypeId));
        }
        if (null != beginTime || null != endTime) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            if (null != beginTime) {
                rangeQueryBuilder.gte(beginTime.getTime());
            }
            if (null != endTime) {
                rangeQueryBuilder.lte(endTime.getTime());
            }
            bqb.filter(rangeQueryBuilder);
        }
        TermsAggregationBuilder typeAgg = AggregationBuilders.terms("unitAgg").field("unitId").size(10000);
        typeAgg.subAggregation(AggregationBuilders.terms("typeAgg").field("typeId").size(10000));
        NativeSearchQuery query = new NativeSearchQueryBuilder().withQuery(bqb).withAggregations(typeAgg).withTrackTotalHits(true).withPageable(PageRequest.of(0, 1)).build();
        SearchHits<ServiceSummaryElasticEntity> hits = elasticsearchTemplate.search(query, ServiceSummaryElasticEntity.class);
        int totalCount = (int) hits.getTotalHits();
        Aggregations aggregations = (Aggregations) hits.getAggregations().aggregations();
        LongTerms unitTerms = aggregations.get("unitAgg");
        for (LongTerms.Bucket unitTerm : unitTerms.getBuckets()) {
            Integer unitKey = unitTerm.getKeyAsNumber().intValue();
            int unitCount = (int) unitTerm.getDocCount();
            LongTerms typeTerms = unitTerm.getAggregations().get("typeAgg");
            List<LongTerms.Bucket> list = typeTerms.getBuckets();
            int extra = unitCount;
            if (null != list && list.size() > 0) {
                int count = 0;
                for (LongTerms.Bucket typeTerm : list) {
                    Integer typeKey = typeTerm.getKeyAsNumber().intValue();
                    int typeCount = (int) typeTerm.getDocCount();
                    count += typeCount;
                    result.add(new ServiceSummaryReportEntity(unitKey, typeKey, typeCount, totalCount));
                }
                extra = unitCount - count;
            }
            if (extra > 0) {
                result.add(new ServiceSummaryReportEntity(unitKey, extra, totalCount));
            }
        }
        return result;
    }

    @Override
    public ServiceSummaryElasticEntity findLatestServiceSummary(Integer sessionId) {
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
            .withQuery(QueryBuilders.matchQuery("sessionId", sessionId))
            .withSorts(SortBuilders.fieldSort("updateTime").order(SortOrder.DESC))
            .withPageable(PageRequest.of(0,1))
            .build();
        SearchHits<ServiceSummaryElasticEntity> searchHits = elasticsearchTemplate.search(searchQuery, ServiceSummaryElasticEntity.class);
        if(searchHits.isEmpty()) {
            return null;
        } else {
            return searchHits.getSearchHit(0).getContent();
        }
    }

}
