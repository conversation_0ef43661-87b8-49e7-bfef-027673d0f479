package com.wolaidai.webot.data.elastic.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

@Document(indexName = "session_list")
public class SessionListElasticEntity {

    //离线状态
    public final static Integer STATUS_OFFLINE = 2;

    @Id
    private Integer id;
    //客户id
    private String clientId;
    //客户类型
    private Integer clientTypeId;
    //客户渠道来源
    private String origin;
    //客服email
    private String serviceUser;
    //来源客服email，首次为空
    private String fromServiceUser;
    @Field(type= FieldType.Date, format = DateFormat.date_time_no_millis)
    private Date gcTime;
    //全局会话Id
    private String gcid;
    //最后接待客服email
    private String lastServiceUser;
    private Integer satisfactionLevel;
    private Integer fromQueueId;
    //会话唯一标识
    private String sessionKey;
    private Integer status;
    //业务ID
    private Integer businessId;
    //业务名
    private String businessName;
    //客户姓名
    private String customerName;
    //客户类型，0:普通用户；1：VIP用户
    private Integer customerType;
    private JSONObject customerDetail;
    private Integer orgId;
    //是否被标星，0：未标记，1：被标记
    private Integer mark = 0;
    //客服未读消息数
    private Integer unreadMsgCount = 0;
    @Field(type= FieldType.Date, format = {DateFormat.date_time,DateFormat.date_time_no_millis})
    private Date createTime;
    //最后一条消息
    private String lastMsg;
    //最后消息时间
    @Field(type= FieldType.Date, format = DateFormat.date_time_no_millis)
    private Date lastMsgTime;
    //客户最早回复消息时间，客服回复后清空
    @Field(type= FieldType.Date,format = DateFormat.date_time_no_millis)
    private Date customerFirstReplyTime;
    //客户最新回复消息时间
    @Field(type= FieldType.Date,format = DateFormat.date_time_no_millis)
    private Date customerLastReplyTime;
    //开始排队时间
    @Field(type= FieldType.Date,format = {DateFormat.date_time,DateFormat.date_time_no_millis})
    private Date queueTime;
    //离线时间
    @Field(type= FieldType.Date,format = DateFormat.date_time_no_millis)
    private Date offlineTime;
    //会话等待时间
    private Long waitSecond;
    //会话持续时间
    private Long durationSecond;
    //人工接通方式:自动分配/客服邀请
    private Integer assignType;
    //结束方式:客户超时下线/客服主动结束会话
    private Integer closeType;
    //转接记录
    private JSONArray transferToUsers = new JSONArray();
    //服务总结状态:1-已总结, 0-未总结
    private Integer serviceSummaryStatus;

    //首次响应超时,0:未超时；1:超时
    private Integer firstRespTimeout = 0;
    //平均响应超时,0:未超时；1:超时
    private Integer avgRespTimeout = 0;
    //会话响应超时,0:未超时；1:超时
    private Integer respTimeout = 0;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getServiceUser() {
        return serviceUser;
    }

    public void setServiceUser(String serviceUser) {
        this.serviceUser = serviceUser;
    }

    public String getFromServiceUser() {
        return fromServiceUser;
    }

    public void setFromServiceUser(String fromServiceUser) {
        this.fromServiceUser = fromServiceUser;
    }

    public Date getGcTime() {
        return gcTime;
    }

    public void setGcTime(Date gcTime) {
        this.gcTime = gcTime;
    }

    public String getGcid() {
        return gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public String getLastServiceUser() {
        return lastServiceUser;
    }

    public void setLastServiceUser(String lastServiceUser) {
        this.lastServiceUser = lastServiceUser;
    }

    public Integer getSatisfactionLevel() {
        return satisfactionLevel;
    }

    public void setSatisfactionLevel(Integer satisfactionLevel) {
        this.satisfactionLevel = satisfactionLevel;
    }

    public Integer getFromQueueId() {
        return fromQueueId;
    }

    public void setFromQueueId(Integer fromQueueId) {
        this.fromQueueId = fromQueueId;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public JSONObject getCustomerDetail() {
        return customerDetail;
    }

    public void setCustomerDetail(JSONObject customerDetail) {
        this.customerDetail = customerDetail;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getMark() {
        return mark;
    }

    public void setMark(Integer mark) {
        this.mark = mark;
    }

    public Integer getUnreadMsgCount() {
        return unreadMsgCount;
    }

    public void setUnreadMsgCount(Integer unreadMsgCount) {
        this.unreadMsgCount = unreadMsgCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLastMsg() {
        return lastMsg;
    }

    public void setLastMsg(String lastMsg) {
        this.lastMsg = lastMsg;
    }

    public Date getLastMsgTime() {
        return lastMsgTime;
    }

    public void setLastMsgTime(Date lastMsgTime) {
        this.lastMsgTime = lastMsgTime;
    }

    public Date getCustomerFirstReplyTime() {
        return customerFirstReplyTime;
    }

    public void setCustomerFirstReplyTime(Date customerFirstReplyTime) {
        this.customerFirstReplyTime = customerFirstReplyTime;
    }

    public Date getCustomerLastReplyTime() {
        return customerLastReplyTime;
    }

    public void setCustomerLastReplyTime(Date customerLastReplyTime) {
        this.customerLastReplyTime = customerLastReplyTime;
    }

    public Date getQueueTime() {
        return queueTime;
    }

    public void setQueueTime(Date queueTime) {
        this.queueTime = queueTime;
    }

    public Date getOfflineTime() {
        return offlineTime;
    }

    public void setOfflineTime(Date offlineTime) {
        this.offlineTime = offlineTime;
    }

    public Long getWaitSecond() {
        return waitSecond;
    }

    public void setWaitSecond(Long waitSecond) {
        this.waitSecond = waitSecond;
    }

    public Long getDurationSecond() {
        return durationSecond;
    }

    public void setDurationSecond(Long durationSecond) {
        this.durationSecond = durationSecond;
    }

    public Integer getAssignType() {
        return assignType;
    }

    public void setAssignType(Integer assignType) {
        this.assignType = assignType;
    }

    public Integer getCloseType() {
        return closeType;
    }

    public void setCloseType(Integer closeType) {
        this.closeType = closeType;
    }

    public JSONArray getTransferToUsers() {
        return transferToUsers;
    }

    public void setTransferToUsers(JSONArray transferToUsers) {
        this.transferToUsers = transferToUsers;
    }

    public Integer getServiceSummaryStatus() {
        return serviceSummaryStatus;
    }

    public void setServiceSummaryStatus(Integer serviceSummaryStatus) {
        this.serviceSummaryStatus = serviceSummaryStatus;
    }

    public Integer getFirstRespTimeout() {
        return firstRespTimeout;
    }

    public void setFirstRespTimeout(Integer firstRespTimeout) {
        this.firstRespTimeout = firstRespTimeout;
    }

    public Integer getAvgRespTimeout() {
        return avgRespTimeout;
    }

    public void setAvgRespTimeout(Integer avgRespTimeout) {
        this.avgRespTimeout = avgRespTimeout;
    }

    public Integer getRespTimeout() {
        return respTimeout;
    }

    public void setRespTimeout(Integer respTimeout) {
        this.respTimeout = respTimeout;
    }
}
