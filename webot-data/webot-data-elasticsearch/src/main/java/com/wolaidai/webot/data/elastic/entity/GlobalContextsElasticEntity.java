package com.wolaidai.webot.data.elastic.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;
import java.util.List;

@Document(indexName = "global_contexts")
public class GlobalContextsElasticEntity {

    @Id
    private String id;

    private String clientId;
    private Integer clientType;
    @Field(type= FieldType.Date,format = DateFormat.date_optional_time)
    private Date gcTime;
    private String gcid;
    private Integer orgId;
    private Integer skillGroupId;
    private List<Integer> skillGroupIds;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public Date getGcTime() {
        return gcTime;
    }

    public void setGcTime(Date gcTime) {
        this.gcTime = gcTime;
    }

    public String getGcid() {
        return gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getSkillGroupId() {
        return skillGroupId;
    }

    public void setSkillGroupId(Integer skillGroupId) {
        this.skillGroupId = skillGroupId;
    }

    public List<Integer> getSkillGroupIds() {
        return skillGroupIds;
    }

    public void setSkillGroupIds(List<Integer> skillGroupIds) {
        this.skillGroupIds = skillGroupIds;
    }
}
