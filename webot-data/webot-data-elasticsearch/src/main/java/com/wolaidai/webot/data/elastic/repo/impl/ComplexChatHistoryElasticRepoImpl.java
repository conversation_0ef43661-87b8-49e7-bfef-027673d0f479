package com.wolaidai.webot.data.elastic.repo.impl;

import com.wolaidai.webot.data.elastic.entity.ChatHistoryElasticEntity;
import com.wolaidai.webot.data.elastic.entity.HistoryManualEntity;
import com.wolaidai.webot.data.elastic.entity.SessionListElasticEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexChatHistoryElasticRepo;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.filter.Filter;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.StringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Avg;
import org.elasticsearch.search.aggregations.metrics.TopHits;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class ComplexChatHistoryElasticRepoImpl implements ComplexChatHistoryElasticRepo {
    
    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;

    @Override
    public Map<String, HistoryManualEntity> findManualHistories(Integer orgId, boolean groupBySession, Date startTime, Date endTime) {
        return findManualHistories(orgId, groupBySession, null, startTime, endTime);
    }

    @Override
    public Map<String, HistoryManualEntity> findManualHistories(Integer orgId, boolean groupBySession, String email, Date startTime, Date endTime) {
        BoolQueryBuilder historyQueryBuilder = new BoolQueryBuilder();
        historyQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        if (null != email && email.trim().length() > 0) {
            historyQueryBuilder.filter(new TermQueryBuilder("manual.email.keyword", email));
        }
        if (null != startTime || null != endTime) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("date");
            if (startTime != null) {
                rangeQueryBuilder.gte(startTime.getTime());
            }
            if (endTime != null) {
                rangeQueryBuilder.lt(endTime.getTime());
            }
            historyQueryBuilder.filter(rangeQueryBuilder);
        }
        TermsAggregationBuilder sessionKeyAggregation = AggregationBuilders.terms("sessionKey").field("manual.sessionKey.keyword").size(100000);
        FilterAggregationBuilder fa = AggregationBuilders.filter("responseTime", QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("manual.responseTime")));
        fa.subAggregation(AggregationBuilders.topHits("first").size(1).sort("_id", SortOrder.ASC));
        fa.subAggregation(AggregationBuilders.avg("avg").field("manual.responseTime"));
        sessionKeyAggregation.subAggregation(fa);

        FilterAggregationBuilder fa2 = AggregationBuilders.filter("manual", QueryBuilders.boolQuery().must(new ExistsQueryBuilder("manual.email")));
        fa2.subAggregation(AggregationBuilders.topHits("first").size(1).sort("_id", SortOrder.ASC));

        NativeSearchQuery historySearchQuery = null;
        if (groupBySession) {
            fa2.subAggregation(AggregationBuilders.topHits("last").size(1).sort("_id", SortOrder.DESC));
            sessionKeyAggregation.subAggregation(fa2);
            historySearchQuery = new NativeSearchQueryBuilder().withQuery(historyQueryBuilder).withAggregations(sessionKeyAggregation).withTrackTotalHits(true).withPageable(PageRequest.of(0, 1)).build();
        } else {
            sessionKeyAggregation.subAggregation(fa2);
            TermsAggregationBuilder emailAggregation = AggregationBuilders.terms("email").field("manual.email.keyword").size(1000).subAggregation(sessionKeyAggregation);
            historySearchQuery = new NativeSearchQueryBuilder().withQuery(historyQueryBuilder).withAggregations(emailAggregation).withTrackTotalHits(true).withPageable(PageRequest.of(0, 1)).build();
        }
        SearchHits<ChatHistoryElasticEntity> searchHits = elasticsearchTemplate.search(historySearchQuery, ChatHistoryElasticEntity.class);
        LinkedHashMap<String, HistoryManualEntity> map = new LinkedHashMap<>();
        Aggregations aggregations = (Aggregations) searchHits.getAggregations().aggregations();
        if (groupBySession) {
            eachSession(groupBySession, map, aggregations.get("sessionKey"));
        } else {
            for (StringTerms.Bucket item : ((StringTerms) aggregations.get("email")).getBuckets()) {
                eachSession(groupBySession, map, item.getAggregations().get("sessionKey"));
            }
        }
        return map;
    }

    @Override
    public SearchHits<ChatHistoryElasticEntity> findByGcids(Collection<String> gcIds) {
        return elasticsearchTemplate.search(new NativeSearchQueryBuilder().withQuery(new TermsQueryBuilder("gcid.keyword", gcIds)).withMaxResults(150000).build(), ChatHistoryElasticEntity.class);
    }

    @Override
    public SearchHits<ChatHistoryElasticEntity> findByGcidOrderByDateAsc(String gcid) {
        return elasticsearchTemplate.search(new NativeSearchQueryBuilder()
                .withQuery(new TermQueryBuilder("gcid.keyword", gcid))
                .withSorts(Collections.singletonList(SortBuilders.fieldSort("date").order(SortOrder.ASC))).build(), ChatHistoryElasticEntity.class);
    }

    @Override
    public SearchHits<ChatHistoryElasticEntity> findByGcidOrSessionKeyOrderByDateAsc(String gcid, String sessionKey) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
            .should(new TermQueryBuilder("gcid.keyword", gcid))
            .should(new TermQueryBuilder("manual.sessionKey.keyword", sessionKey));
        
        return elasticsearchTemplate.search(new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withSorts(Collections.singletonList(SortBuilders.fieldSort("date").order(SortOrder.ASC))).build(), ChatHistoryElasticEntity.class);
    }

    @Override
    public long countByGcidAndSenderAndTypeNotAndRecallNot(String gcid, String sender, String type) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("gcid.keyword", gcid));
        boolQueryBuilder.filter(new TermQueryBuilder("sender", sender));
        boolQueryBuilder.filter(new BoolQueryBuilder().mustNot(new TermQueryBuilder("type", type)));
        boolQueryBuilder.filter(new BoolQueryBuilder().mustNot(new ExistsQueryBuilder("recall")));
        return elasticsearchTemplate.count(new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build(),ChatHistoryElasticEntity.class);
    }

    @Override
    public long countByGcidAndType(String gcid, String type) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("gcid.keyword", gcid));
        boolQueryBuilder.filter(new TermQueryBuilder(type, type));
        return elasticsearchTemplate.count(new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build(),ChatHistoryElasticEntity.class);
    }

    @Override
    public long countByGcidAndRecall(String gcid, boolean recall) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("gcid.keyword", gcid));
        boolQueryBuilder.filter(new TermQueryBuilder("recall", recall));
        return elasticsearchTemplate.count(new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build(),ChatHistoryElasticEntity.class);
    }

    @Override
    public long countByGcidAndSenderAndSceneAndTypeNot(String gcid, String sender, String scene, String type) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(new TermQueryBuilder("gcid.keyword", gcid));
        boolQueryBuilder.filter(new TermQueryBuilder("sender", sender));
        //机器人会话包含:scene=bot或scene不存在(微信端咨询机器人)
        if (ChatHistoryElasticEntity.SCENE_TYPE_CS.equals(scene)) {
            boolQueryBuilder.filter(new TermQueryBuilder("scene", scene));
        } else {
            boolQueryBuilder.filter(new BoolQueryBuilder().mustNot(new TermQueryBuilder("scene", ChatHistoryElasticEntity.SCENE_TYPE_CS)));
        }
        boolQueryBuilder.filter(new BoolQueryBuilder().mustNot(new TermQueryBuilder("type", type)));
        return elasticsearchTemplate.count(new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build(),ChatHistoryElasticEntity.class);
    }

    @Override
    public SearchHits<ChatHistoryElasticEntity> findByParams(Integer orgId, String customerName, String mediaType, Date startTime,
                                                             Date endTime, List<String> emails, Pageable pageable) {
        BoolQueryBuilder chatQueryBuilder = new BoolQueryBuilder();
        chatQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
        if (StringUtils.hasText(mediaType)) {
            chatQueryBuilder.filter(new TermQueryBuilder("type", mediaType));
        } else {
            chatQueryBuilder.filter(new TermsQueryBuilder("type",
                    Arrays.asList(ChatHistoryElasticEntity.TYPE_IMAGE, ChatHistoryElasticEntity.TYPE_VIDEO)));
        }
        RangeQueryBuilder chatRange = new RangeQueryBuilder("date");
        chatRange.gte(startTime.getTime());
        chatRange.lte(endTime.getTime());
        chatQueryBuilder.filter(chatRange);
        Set<String> globalCidSet = new HashSet<>();
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("globalCid").field("gcid.keyword").size(10000).order(BucketOrder.aggregation("topDate",false))
                .subAggregation(AggregationBuilders.max("topDate").field("date"));
        NativeSearchQuery chatSearchQuery = new NativeSearchQueryBuilder().withQuery(chatQueryBuilder).withMaxResults(0).addAggregation(aggregationBuilder).build();
        SearchHits<ChatHistoryElasticEntity> chatHistoryElasticEntitySearchHits = elasticsearchTemplate.search(chatSearchQuery, ChatHistoryElasticEntity.class);
        Aggregations aggregations = (Aggregations) chatHistoryElasticEntitySearchHits.getAggregations().aggregations();
        StringTerms globalCidTerms = aggregations.get("globalCid");
        if (globalCidTerms != null) {
            List<StringTerms.Bucket> gcidBuckets = globalCidTerms.getBuckets();
            for (StringTerms.Bucket termBucket : gcidBuckets) {
                globalCidSet.add(termBucket.getKeyAsString());
            }
        }
        if (!CollectionUtils.isEmpty(globalCidSet)) {
            BoolQueryBuilder sessionQueryBuilder = new BoolQueryBuilder();
            sessionQueryBuilder.filter(new TermQueryBuilder("orgId", orgId));
            //会话已完成状态
            sessionQueryBuilder.filter(new TermQueryBuilder("status", SessionListElasticEntity.STATUS_OFFLINE));
            if (StringUtils.hasText(customerName)) {
                sessionQueryBuilder.filter(new MatchPhraseQueryBuilder("customerName", customerName));
            }
            if (!CollectionUtils.isEmpty(emails)) {
                BoolQueryBuilder keyBoolQueryBuilder = new BoolQueryBuilder();
                keyBoolQueryBuilder.should(new TermsQueryBuilder("serviceUser.keyword", emails));
                keyBoolQueryBuilder.should(new TermsQueryBuilder("fromServiceUser.keyword", emails));
                keyBoolQueryBuilder.should(new TermsQueryBuilder("lastServiceUser.keyword", emails));
                keyBoolQueryBuilder.should(new TermsQueryBuilder("transferToUsers.keyword", emails));
                sessionQueryBuilder.filter(keyBoolQueryBuilder);
            }
            RangeQueryBuilder sessionRange = new RangeQueryBuilder("createTime");
            sessionRange.gte(startTime.getTime());
            sessionRange.lte(endTime.getTime());
            sessionQueryBuilder.filter(sessionRange);
            sessionQueryBuilder.filter(new TermsQueryBuilder("gcid.keyword", globalCidSet));
            NativeSearchQuery historySearchQuery = new NativeSearchQueryBuilder()
                    .withQuery(sessionQueryBuilder).withSourceFilter(new FetchSourceFilter(new String[]{"gcid"}, null) {}).build();
            SearchHits<SessionListElasticEntity> searchHits = elasticsearchTemplate.search(historySearchQuery, SessionListElasticEntity.class);
            if (!CollectionUtils.isEmpty(searchHits.getSearchHits())) {
                globalCidSet.clear();
                globalCidSet.addAll(searchHits.stream().map(org.springframework.data.elasticsearch.core.SearchHit::getContent)
                        .collect(Collectors.toList()).stream().map(SessionListElasticEntity::getGcid).collect(Collectors.toSet()));
                BoolQueryBuilder chatQuery = new BoolQueryBuilder();
                chatQuery.filter(new TermQueryBuilder("orgId", orgId));
                chatQuery.filter(new TermsQueryBuilder("gcid.keyword", globalCidSet));
                if (StringUtils.hasText(mediaType)) {
                    chatQuery.filter(new TermQueryBuilder("type", mediaType));
                } else {
                    chatQuery.filter(new TermsQueryBuilder("type",
                            Arrays.asList(ChatHistoryElasticEntity.TYPE_IMAGE, ChatHistoryElasticEntity.TYPE_VIDEO)));
                }
                NativeSearchQuery chatSearch = new NativeSearchQueryBuilder().withQuery(chatQuery).withPageable(pageable).build();
                chatSearch.setTrackTotalHits(true);
                return elasticsearchTemplate.search(chatSearch, ChatHistoryElasticEntity.class);
            }
        }
        return null;
    }

    @Override
    public SearchHits<ChatHistoryElasticEntity> findUserAttachmentHistories(Set<String> gcidSet) {
        List<String> typeList = Arrays.asList(ChatHistoryElasticEntity.TYPE_IMAGE, ChatHistoryElasticEntity.TYPE_VIDEO, ChatHistoryElasticEntity.TYPE_VOICE,
                ChatHistoryElasticEntity.TYPE_FILE);
        BoolQueryBuilder chatQueryBuilder = new BoolQueryBuilder();
        chatQueryBuilder.filter(new TermsQueryBuilder("gcid.keyword", gcidSet));
        chatQueryBuilder.filter(new TermsQueryBuilder("type", typeList));
        chatQueryBuilder.filter(new TermQueryBuilder("sender", ChatHistoryElasticEntity.SENDER_TYPE_USER));
        NativeSearchQuery chatSearchQuery = new NativeSearchQueryBuilder()
                .withQuery(chatQueryBuilder)
                .withSorts(Collections.singletonList(SortBuilders.fieldSort("date").order(SortOrder.DESC)))
                .build();
        return elasticsearchTemplate.search(chatSearchQuery, ChatHistoryElasticEntity.class);
    }

    @SuppressWarnings("unchecked")
    private void eachSession(boolean groupBySession, LinkedHashMap<String, HistoryManualEntity> map, StringTerms sessionKeyTerms) {
        DatatypeFactory parser = null;
        try {
            parser = DatatypeFactory.newInstance();
        } catch (DatatypeConfigurationException e1) {
            e1.printStackTrace();
        }
        for (StringTerms.Bucket item2 : sessionKeyTerms.getBuckets()) {
            Aggregations agg = item2.getAggregations();
            HistoryManualEntity hm = new HistoryManualEntity();
            Aggregations manualAgg = ((Filter) agg.get("manual")).getAggregations();
            if (((TopHits) manualAgg.get("first")).getHits().getHits().length == 0) {
                continue;
            }
            Map<String, Object> top = ((TopHits) manualAgg.get("first")).getHits().getHits()[0].getSourceAsMap();
            Map<String, Object> manual = (Map<String, Object>) top.get("manual");
            if (null != manual) {
                hm.setEmail((String) manual.get("email"));
            }
            Aggregations responseTime = ((Filter) agg.get("responseTime")).getAggregations();
            SearchHit[] hits = ((TopHits) responseTime.get("first")).getHits().getHits();
            if (null != hits && hits.length > 0) {
                top = hits[0].getSourceAsMap();
                manual = (Map<String, Object>) top.get("manual");
                if (null != manual) {
                    hm.setFirstResponseTime(((Number) manual.get("responseTime")).intValue());
                }
                if (null != parser) {
                    hm.setFirstSendTime(parser.newXMLGregorianCalendar((String) top.get("date")).toGregorianCalendar().getTime());
                }
            }
            hm.setSessionKey(item2.getKeyAsString());
            hm.setCount((int) item2.getDocCount());
            float avgResponseTime = (float) ((Avg) responseTime.get("avg")).getValue();
            if (avgResponseTime > 0) {
                hm.setAvgResponseTime(avgResponseTime);
            }
            if (groupBySession) {
                Map<String, Object> bottom = ((TopHits) manualAgg.get("last")).getHits().getHits()[0].getSourceAsMap();
                manual = (Map<String, Object>) bottom.get("manual");
                if (null != manual) {
                    hm.setLastEmail((String) manual.get("email"));
                }
                map.put(hm.getSessionKey(), hm);
            } else {
                map.put(hm.getEmail() + hm.getSessionKey(), hm);
            }
        }
    }
}
