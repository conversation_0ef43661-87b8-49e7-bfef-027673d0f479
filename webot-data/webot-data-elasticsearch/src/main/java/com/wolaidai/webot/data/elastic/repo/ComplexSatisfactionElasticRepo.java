package com.wolaidai.webot.data.elastic.repo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.wolaidai.webot.data.elastic.entity.SatisfactionDataElasticEntity;
import com.wolaidai.webot.data.elastic.entity.SatisfactionLabelReportEntity;

public interface ComplexSatisfactionElasticRepo {
    List<SatisfactionLabelReportEntity> findSatisfactionLabels(Integer orgId, Collection<String> emails, Date beginTime, Date endTime);
}
