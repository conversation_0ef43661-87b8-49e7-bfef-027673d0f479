package com.wolaidai.webot.data.elastic.entity;

import java.util.Date;

public class HistoryManualEntity {
    private String email;
    private String sessionKey;
    private Integer firstResponseTime;
    private Integer count;
    private Float avgResponseTime;
    private Date firstSendTime;
    private String lastEmail;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public Integer getFirstResponseTime() {
        return firstResponseTime;
    }

    public void setFirstResponseTime(Integer firstResponseTime) {
        this.firstResponseTime = firstResponseTime;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Float getAvgResponseTime() {
        return avgResponseTime;
    }

    public void setAvgResponseTime(Float avgResponseTime) {
        this.avgResponseTime = avgResponseTime;
    }

    public Date getFirstSendTime() {
        return firstSendTime;
    }

    public void setFirstSendTime(Date firstSendTime) {
        this.firstSendTime = firstSendTime;
    }

    public String getLastEmail() {
        return lastEmail;
    }

    public void setLastEmail(String lastEmail) {
        this.lastEmail = lastEmail;
    }
}
