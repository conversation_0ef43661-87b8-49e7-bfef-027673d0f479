package com.wolaidai.webot.data.elastic.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class LineElasticModel extends BaseElasticModel{
    private String interval;
    private long totalCount;
    private long subTotalCount;
    private List<LineChartSeries> seriesData = new ArrayList<>();

    public String getInterval() {
        return interval;
    }

    public void setInterval(String interval) {
        this.interval = interval;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public long getSubTotalCount() {
        return subTotalCount;
    }

    public void setSubTotalCount(long subTotalCount) {
        this.subTotalCount = subTotalCount;
    }

    public List<LineChartSeries> getSeriesData() {
        return seriesData;
    }

    public void setSeriesData(List<LineChartSeries> seriesData) {
        this.seriesData = seriesData;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class LineChartSeries{
        private Object groupKey;
        private long totalCount;
        private long subTotalCount;
        private List<LineChatCount> groupValue = new ArrayList<>();

        public LineChartSeries() {
        }

        public LineChartSeries(Object groupKey) {
            this.groupKey = groupKey;
        }

        public Object getGroupKey() {
            return groupKey;
        }

        public void setGroupKey(Object groupKey) {
            this.groupKey = groupKey;
        }

        public long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(long totalCount) {
            this.totalCount = totalCount;
        }

        public long getSubTotalCount() {
            return subTotalCount;
        }

        public void setSubTotalCount(long subTotalCount) {
            this.subTotalCount = subTotalCount;
        }

        public List<LineChatCount> getGroupValue() {
            return groupValue;
        }

        public void setGroupValue(List<LineChatCount> groupValue) {
            this.groupValue = groupValue;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class LineChatCount{
        private Object dataKey;
        private long dataCount;
        private long subDataCount;

        public LineChatCount() {
        }

        public LineChatCount(Object dataKey, long dataCount) {
            this.dataKey = dataKey;
            this.dataCount = dataCount;
        }

        public LineChatCount(Object dataKey, long dataCount, long subDataCount) {
            this.dataKey = dataKey;
            this.dataCount = dataCount;
            this.subDataCount = subDataCount;
        }

        public Object getDataKey() {
            return dataKey;
        }

        public void setDataKey(Object dataKey) {
            this.dataKey = dataKey;
        }

        public long getDataCount() {
            return dataCount;
        }

        public void setDataCount(long dataCount) {
            this.dataCount = dataCount;
        }

        public long getSubDataCount() {
            return subDataCount;
        }

        public void setSubDataCount(long subDataCount) {
            this.subDataCount = subDataCount;
        }
    }

}
