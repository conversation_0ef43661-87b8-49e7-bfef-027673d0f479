package com.wolaidai.webot.data.elastic.repo;

import com.wolaidai.webot.data.elastic.entity.ChatHistoryElasticEntity;
import com.wolaidai.webot.data.elastic.entity.HistoryManualEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.SearchHits;

import java.util.*;

public interface ComplexChatHistoryElasticRepo {

    Map<String, HistoryManualEntity> findManualHistories(Integer orgId, boolean groupBySession, Date startTime, Date endTime);
    Map<String, HistoryManualEntity> findManualHistories(Integer orgId, boolean groupBySession, String email, Date startTime, Date endTime);
    SearchHits<ChatHistoryElasticEntity> findByGcids(Collection<String> gcIds);

    SearchHits<ChatHistoryElasticEntity> findByGcidOrderByDateAsc(String gcid);

    SearchHits<ChatHistoryElasticEntity> findByGcidOrSessionKeyOrderByDateAsc(String gcid, String sessionKey);

    long countByGcidAndSenderAndTypeNotAndRecallNot(String gcid, String sender, String type);

    long countByGcidAndType(String gcid, String type);

    long countByGcidAndRecall(String gcid, boolean recall);

    long countByGcidAndSenderAndSceneAndTypeNot(String gcid, String sender, String scene, String type);

    SearchHits<ChatHistoryElasticEntity> findByParams(Integer orgId, String customerName, String mediaType,
                                                      Date startTime, Date endTime, List<String> emails, Pageable pageable);

    SearchHits<ChatHistoryElasticEntity> findUserAttachmentHistories(Set<String> gcidSet);
}
