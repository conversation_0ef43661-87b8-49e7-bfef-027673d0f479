package com.wolaidai.webot.data.elastic.entity;

import java.util.LinkedHashMap;
import java.util.Map;

public class SatisfactionLabelReportEntity {

    private String label;
    private Integer labelCount;
    private Map<String, Integer> data = new LinkedHashMap<>();

    public SatisfactionLabelReportEntity(String label, Integer labelCount) {
        this.label = label;
        this.labelCount = labelCount;
    }

    public void addData(String email, Integer count) {
        data.put(email, count);
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getLabelCount() {
        return labelCount;
    }

    public void setLabelCount(Integer labelCount) {
        this.labelCount = labelCount;
    }

    public Map<String, Integer> getData() {
        return data;
    }

    public void setData(Map<String, Integer> data) {
        this.data = data;
    }

}
