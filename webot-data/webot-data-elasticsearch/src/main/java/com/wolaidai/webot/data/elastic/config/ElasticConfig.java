package com.wolaidai.webot.data.elastic.config;

import org.elasticsearch.client.Client;
import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.cluster.node.DiscoveryNode;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.transport.TransportAddress;
import org.elasticsearch.xpack.client.PreBuiltXPackTransportClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;

@Configuration
@EnableElasticsearchRepositories(basePackages = "com.wolaidai.webot.data.elastic.repo")
@ComponentScan(basePackages = "com.wolaidai.webot.data.elastic.repo")
@EnableConfigurationProperties(ElasticPropertyConfig.class)
public class ElasticConfig {
    @Autowired
    private ElasticPropertyConfig elasticPropertyConfig;

    @Bean
    public Client esClient() {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        // 设置集群名字
        Settings.Builder settingsBuilder = Settings.builder();
        settingsBuilder.put("cluster.name", elasticPropertyConfig.getEsClusterName());
        settingsBuilder.put("client.transport.sniff", true);
        settingsBuilder.put("client.transport.ping_timeout", "30s");
        if (!StringUtils.isEmpty(elasticPropertyConfig.getEsSecurityUser())) {
            settingsBuilder.put("xpack.security.user", elasticPropertyConfig.getEsSecurityUser());
            settingsBuilder.put("xpack.security.transport.ssl.enabled", "true");
            settingsBuilder.put("xpack.security.transport.ssl.verification_mode", "certificate");
            settingsBuilder.put("xpack.ssl.keystore.path", elasticPropertyConfig.getEsXpackKeyPath());
            settingsBuilder.put("xpack.ssl.keystore.password", elasticPropertyConfig.getEsXpackKeyPassword());
            settingsBuilder.put("xpack.ssl.truststore.path", elasticPropertyConfig.getEsXpackKeyPath());
            settingsBuilder.put("xpack.ssl.truststore.password", elasticPropertyConfig.getEsXpackKeyPassword());
        }

        TransportClient client = new PreBuiltXPackTransportClient(settingsBuilder.build());

        try {
            // 读取的ip列表是以逗号分隔的
            for (String clusterNode : elasticPropertyConfig.getEsClusterNodes().split(",")) {
                String ip = clusterNode.split(":")[0];
                String port = clusterNode.split(":")[1];
                client.addTransportAddress(new TransportAddress(InetAddress.getByName(ip), Integer.parseInt(port)));
            }
            List<DiscoveryNode> discoveryNodes = client.connectedNodes();
            if (discoveryNodes.isEmpty()) {
                throw new RuntimeException("connect es fail...");
            }
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return client;
    }

    @Bean
    public ElasticsearchTemplate elasticsearchTemplate(){
       return new ElasticsearchTemplate(esClient());
    }
}
