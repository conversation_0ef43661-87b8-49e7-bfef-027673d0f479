package com.wolaidai.webot.data.elastic.repo.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.StringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;

import com.wolaidai.webot.data.elastic.entity.SatisfactionDataElasticEntity;
import com.wolaidai.webot.data.elastic.entity.SatisfactionLabelReportEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexSatisfactionElasticRepo;

@SuppressWarnings("deprecation")
@Repository
public class ComplexSatisfactionElasticRepoImpl implements ComplexSatisfactionElasticRepo {

    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;

    @Override
    public List<SatisfactionLabelReportEntity> findSatisfactionLabels(Integer orgId, Collection<String> emails, Date beginTime, Date endTime) {
        BoolQueryBuilder bqb = new BoolQueryBuilder();
        bqb.filter(new TermQueryBuilder("orgId", orgId));
        if (null != emails && emails.size() > 0) {
            bqb.filter(new TermsQueryBuilder("serviceUser.keyword", emails));
        }
        if (null != beginTime || null != endTime) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime");
            if (null != beginTime) {
                rangeQueryBuilder.gte(beginTime.getTime());
            }
            if (null != endTime) {
                rangeQueryBuilder.lte(endTime.getTime());
            }
            bqb.filter(rangeQueryBuilder);
        }
        TermsAggregationBuilder labelAgg = AggregationBuilders.terms("labelAgg").field("labels.keyword").size(10000);
        labelAgg.subAggregation(AggregationBuilders.terms("emailAgg").field("serviceUser.keyword").size(10000));
        NativeSearchQuery query = new NativeSearchQueryBuilder().withQuery(bqb).withAggregations(labelAgg).withTrackTotalHits(true).withPageable(PageRequest.of(0, 1)).build();
        SearchHits<SatisfactionDataElasticEntity> hits = elasticsearchTemplate.search(query, SatisfactionDataElasticEntity.class);
        Aggregations aggregations = (Aggregations) hits.getAggregations().aggregations();
        StringTerms labelTerms = aggregations.get("labelAgg");
        List<SatisfactionLabelReportEntity> list = new ArrayList<>();
        for (StringTerms.Bucket labelTerm : labelTerms.getBuckets()) {
            SatisfactionLabelReportEntity sl = new SatisfactionLabelReportEntity(labelTerm.getKeyAsString(), (int) labelTerm.getDocCount());
            StringTerms emailTerms = labelTerm.getAggregations().get("emailAgg");
            for (StringTerms.Bucket emailTerm : emailTerms.getBuckets()) {
                sl.addData(emailTerm.getKeyAsString(), (int) emailTerm.getDocCount());
            }
            list.add(sl);
        }
        return list;
    }

}
