package com.wolaidai.webot.data.elastic.entity;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class ServiceSummaryReportEntity {

    private Integer unitId;
    private Integer typeId;
    private Integer itemCount;
    private Integer totalCount;
    private Float percent;

    public ServiceSummaryReportEntity(Integer unitId, Integer itemCount, Integer totalCount) {
        this.unitId = unitId;
        this.itemCount = itemCount;
        this.totalCount = totalCount;
        this.percent = BigDecimal.valueOf(itemCount).divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).floatValue();
    }

    public ServiceSummaryReportEntity(Integer unitId, Integer typeId, Integer itemCount, Integer totalCount) {
        this.unitId = unitId;
        this.typeId = typeId;
        this.itemCount = itemCount;
        this.totalCount = totalCount;
        this.percent = BigDecimal.valueOf(itemCount).divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).floatValue();
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Float getPercent() {
        return percent;
    }

    public void setPercent(Float percent) {
        this.percent = percent;
    }

}
