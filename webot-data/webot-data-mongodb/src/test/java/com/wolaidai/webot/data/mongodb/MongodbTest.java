package com.wolaidai.webot.data.mongodb;

import com.wolaidai.webot.data.mongodb.config.MongodbConfig;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MongodbConfig.class)
@EnableAutoConfiguration
public class MongodbTest {
    @Autowired
    private MongoTemplate mongoTemplate;
}
