package com.wolaidai.webot.data.mysql;

import com.wolaidai.webot.data.mysql.config.MysqlConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@EnableAutoConfiguration
@SpringBootTest(classes = MysqlConfig.class)
public class WebotDataMysqlApplicationTests {

    @Test
    public void test1(){
    }

}

