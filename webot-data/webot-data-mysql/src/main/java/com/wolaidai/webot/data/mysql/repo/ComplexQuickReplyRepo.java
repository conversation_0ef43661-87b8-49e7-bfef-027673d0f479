package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.QuickReplyEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ComplexQuickReplyRepo  {

    Page<QuickReplyEntity> findQuickReplies(Integer orgId, String email, Integer type, Integer categoryId, String key, Integer global, Pageable pageable);

    List<QuickReplyEntity> findQuickReplies(Integer orgId, Integer categoryId, String creator, Integer global);
}
