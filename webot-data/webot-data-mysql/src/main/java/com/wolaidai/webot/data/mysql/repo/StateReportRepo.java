package com.wolaidai.webot.data.mysql.repo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.wolaidai.webot.data.mysql.entity.report.StateReportEntity;

public interface StateReportRepo extends BaseRepo<StateReportEntity, Integer> {
    
    StateReportEntity findByOrgIdAndEmailAndDataTimeEquals(Integer orgId, String email, Date dataTime);

    List<StateReportEntity> findByOrgIdAndEmailInAndDataTimeEquals(Integer orgId, Collection<String> emails, Date dataTime);

    List<StateReportEntity> findByOrgIdAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAscNickNameAsc(Integer orgId, Date beginTime, Date endTime);

    List<StateReportEntity> findByOrgIdAndEmailInAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAscNickNameAsc(Integer orgId, Collection<String> emails, Date beginTime, Date endTime);

    @Modifying
    @Transactional
    @Query(value = "delete from state_report where data_time>=?1 and data_time<?2", nativeQuery = true)
    Integer deleteByDataTime(Date begin, Date end);

    @Modifying
    @Transactional
    @Query(value = "delete from state_report where org_id =?1 and data_time>=?2 and data_time<?3", nativeQuery = true)
    Integer deleteByOrgIdAndDataTime(Integer orgId, Date begin, Date end);

    @Modifying
    @Transactional
    @Query(value = "delete from state_report where org_id=?1 and email in ?2 and data_time>=?3 and data_time<?4", nativeQuery = true)
    Integer deleteByOrgIdAndEmailsAndDataTime(Integer orgId, Collection<String> emails, Date begin, Date end);

}
