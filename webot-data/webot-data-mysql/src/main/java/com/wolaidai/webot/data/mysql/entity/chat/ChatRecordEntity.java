package com.wolaidai.webot.data.mysql.entity.chat;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "chat_record")
public class ChatRecordEntity extends BaseEntity {

    private Integer orgId;
    private String fromEmail;
    private String toEmail;
    @ManyToOne(fetch = FetchType.LAZY)
    private ChatSessionEntity session;
    @ManyToOne(fetch = FetchType.LAZY)
    private ChatMembersSnapshotEntity snapshot;
    private String msgId;
    private String type;
    private String content;
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JSONObject media = new JSONObject();
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JSONObject manual = new JSONObject();
    private Date createTime;

    public String getFromEmail() {
        return fromEmail;
    }

    public void setFromEmail(String fromEmail) {
        this.fromEmail = fromEmail;
    }

    public String getToEmail() {
        return toEmail;
    }

    public void setToEmail(String toEmail) {
        this.toEmail = toEmail;
    }

    public ChatSessionEntity getSession() {
        return session;
    }

    public void setSession(ChatSessionEntity session) {
        this.session = session;
    }

    public ChatMembersSnapshotEntity getSnapshot() {
        return snapshot;
    }

    public void setSnapshot(ChatMembersSnapshotEntity snapshot) {
        this.snapshot = snapshot;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public JSONObject getMedia() {
        return media;
    }

    public void setMedia(JSONObject media) {
        this.media = media;
    }

    public JSONObject getManual() {
        return manual;
    }

    public void setManual(JSONObject manual) {
        this.manual = manual;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
