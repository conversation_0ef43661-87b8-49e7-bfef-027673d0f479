package com.wolaidai.webot.data.mysql.entity.report;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "session_report", uniqueConstraints = {@UniqueConstraint(columnNames = {"date", "businessId"})})
public class SessionReportEntity extends BaseEntity {
    //日期
    private Date date;
    //产品ID
    private Integer businessId;
    //产品
    private String businessName;
    //进入人工请求数
    private Long manualCount;
    //人工应答数
    private Long answerCount;
    //放弃数
    private Long quitCount;
    //等待数
    private Long waitCount;
    //接通率
    @Column(precision = 5, scale = 2)
    private BigDecimal connectRate;
    //会话总时长
    private Long sessionSecond;
    //会话数
    private Long sessionCount;
    //会话均长
    private Long avgSessionSecond;
    //等待总时长
    private Long waitSecond;
    //等待均长
    private Long avgWaitSecond;
    //组织ID
    private Integer orgId;
    //记录生成时间
    private Date creatTime;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Long getManualCount() {
        return manualCount;
    }

    public void setManualCount(Long manualCount) {
        this.manualCount = manualCount;
    }

    public Long getAnswerCount() {
        return answerCount;
    }

    public void setAnswerCount(Long answerCount) {
        this.answerCount = answerCount;
    }

    public Long getQuitCount() {
        return quitCount;
    }

    public void setQuitCount(Long quitCount) {
        this.quitCount = quitCount;
    }

    public Long getWaitCount() {
        return waitCount;
    }

    public void setWaitCount(Long waitCount) {
        this.waitCount = waitCount;
    }

    public BigDecimal getConnectRate() {
        return connectRate;
    }

    public void setConnectRate(BigDecimal connectRate) {
        this.connectRate = connectRate;
    }

    public Long getSessionSecond() {
        return sessionSecond;
    }

    public void setSessionSecond(Long sessionSecond) {
        this.sessionSecond = sessionSecond;
    }

    public Long getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Long sessionCount) {
        this.sessionCount = sessionCount;
    }

    public Long getAvgSessionSecond() {
        return avgSessionSecond;
    }

    public void setAvgSessionSecond(Long avgSessionSecond) {
        this.avgSessionSecond = avgSessionSecond;
    }

    public Long getWaitSecond() {
        return waitSecond;
    }

    public void setWaitSecond(Long waitSecond) {
        this.waitSecond = waitSecond;
    }

    public Long getAvgWaitSecond() {
        return avgWaitSecond;
    }

    public void setAvgWaitSecond(Long avgWaitSecond) {
        this.avgWaitSecond = avgWaitSecond;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Date getCreatTime() {
        return creatTime;
    }

    public void setCreatTime(Date creatTime) {
        this.creatTime = creatTime;
    }
}
