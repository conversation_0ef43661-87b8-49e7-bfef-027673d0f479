package com.wolaidai.webot.data.mysql.repo.chat;

import com.wolaidai.webot.data.mysql.entity.chat.ChatRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;

public interface ComplexChatRecordRepo {

    Page<ChatRecordEntity> findByOrgIdAndSessionId(Integer orgId, String email, Integer sessionId, Date date, Pageable pageable);

}
