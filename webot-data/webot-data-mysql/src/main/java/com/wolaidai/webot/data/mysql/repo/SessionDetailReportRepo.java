package com.wolaidai.webot.data.mysql.repo;

import java.util.Collection;
import java.util.Date;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.wolaidai.webot.data.mysql.entity.report.SessionDetailReportEntity;

public interface SessionDetailReportRepo extends BaseRepo<SessionDetailReportEntity, Integer> {

    SessionDetailReportEntity findByOrgIdAndEmailAndSessionIdAndDataTimeEquals(Integer orgId, String email, Integer sessionId, Date dataTime);

    Page<SessionDetailReportEntity> findByOrgIdAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByQueueTimeDesc(Integer orgId, Date beginTime, Date endTime, Pageable page);

    Page<SessionDetailReportEntity> findByOrgIdAndEmailInAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByQueueTimeDesc(Integer orgId, Collection<String> emails, Date beginTime, Date endTime, Pageable page);

    @Modifying
    @Transactional
    @Query(value = "delete from session_detail_report where data_time>=?1 and data_time<?2", nativeQuery = true)
    Integer deleteByDataTime(Date begin, Date end);

    @Modifying
    @Transactional
    @Query(value = "delete from session_detail_report where org_id =?1 and data_time>=?2 and data_time<?3", nativeQuery = true)
    Integer deleteByOrgIdAndDataTime(Integer orgId, Date begin, Date end);

    @Modifying
    @Transactional
    @Query(value = "delete from session_detail_report where org_id=?1 and email in ?2 and data_time>=?3 and data_time<?4", nativeQuery = true)
    Integer deleteByOrgIdAndEmailsAndDataTime(Integer orgId, Collection<String> emails, Date begin, Date end);

}
