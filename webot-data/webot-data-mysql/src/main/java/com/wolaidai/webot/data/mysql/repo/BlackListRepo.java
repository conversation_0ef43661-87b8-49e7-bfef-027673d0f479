package com.wolaidai.webot.data.mysql.repo;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.wolaidai.webot.data.mysql.entity.blacklist.BlackListEntity;

public interface BlackListRepo extends BaseRepo<BlackListEntity, Integer> {

    BlackListEntity findByOrgIdAndId(Integer orgId, Integer id);

    List<BlackListEntity> findByOrgIdAndIdIn(Integer orgId, Collection<Integer> ids);

    List<BlackListEntity> findByOrgIdAndIdInAndStatus(Integer orgId, Collection<Integer> ids, Integer status);

    @Query(value = "select id from black_list where org_id=?1 and (phone=?2 or client_id=?3) and (expire_time is null or expire_time>now()) and (status=0 or status=1) limit 1", nativeQuery = true)
    Integer findByOrgIdAndPhoneAndClientId(Integer orgId, String phone, String clientId);
}
