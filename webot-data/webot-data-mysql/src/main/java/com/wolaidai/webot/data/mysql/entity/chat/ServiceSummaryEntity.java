package com.wolaidai.webot.data.mysql.entity.chat;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "service_summary")
//服务总结表
public class ServiceSummaryEntity extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    private SessionListEntity session;
    private Integer clientTypeId;
    private Integer businessId;
    private Integer orgId;
    private String remark;
    @ManyToOne(fetch = FetchType.LAZY)
    private BusinessUnitEntity unit;
    @ManyToOne(fetch = FetchType.LAZY)
    private BusinessTypeEntity type;
    
    private Date createTime;
    private Date updateTime;

    private String aiRemark;

    public Integer lookSessionId() {
        if (session != null) {
            return session.getId();
        }
        return null;
    }

    public SessionListEntity getSession() {
        return session;
    }

    public void setSession(SessionListEntity session) {
        this.session = session;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BusinessUnitEntity getUnit() {
        return unit;
    }

    public void setUnit(BusinessUnitEntity unit) {
        this.unit = unit;
    }

    public BusinessTypeEntity getType() {
        return type;
    }

    public void setType(BusinessTypeEntity type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAiRemark() {
        return aiRemark;
    }

    public void setAiRemark(String aiRemark) {
        this.aiRemark = aiRemark;
    }
}
