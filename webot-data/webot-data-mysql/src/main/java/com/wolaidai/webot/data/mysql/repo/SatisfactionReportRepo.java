package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.report.SatisfactionReportEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface SatisfactionReportRepo extends BaseRepo<SatisfactionReportEntity, Integer> {

    List<SatisfactionReportEntity> findByOrgIdAndDateGreaterThanEqualAndDateLessThanEqual(Integer orgId, Date startTime, Date endTime);

    @Modifying
    @Transactional
    void deleteAllByOrgIdAndDateGreaterThanEqualAndDateLessThanEqual(Integer orgId, Date startTime, Date endTime);

}
