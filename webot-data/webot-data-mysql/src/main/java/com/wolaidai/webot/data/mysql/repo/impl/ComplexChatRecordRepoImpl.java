package com.wolaidai.webot.data.mysql.repo.impl;

import com.wolaidai.webot.data.mysql.entity.chat.ChatRecordEntity;
import com.wolaidai.webot.data.mysql.repo.chat.ChatRecordRepo;
import com.wolaidai.webot.data.mysql.repo.chat.ComplexChatRecordRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class ComplexChatRecordRepoImpl implements ComplexChatRecordRepo {

    @Autowired
    private ChatRecordRepo chatRecordRepo;

    @Override
    public Page<ChatRecordEntity> findByOrgIdAndSessionId(Integer orgId, String email, Integer sessionId, Date date, Pageable pageable) {
        return chatRecordRepo.findAll((Root<ChatRecordEntity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder)->{
            List<Predicate> andPredicates = new ArrayList<>();
            andPredicates.add(criteriaBuilder.equal(root.get("orgId"), orgId));
            andPredicates.add(criteriaBuilder.equal(root.get("session").get("id"), sessionId));
            if (StringUtils.hasText(email)) {
                Expression<String> keySearch = criteriaBuilder.function("json_contains", String.class,
                        criteriaBuilder.function("json_extract", List.class, root.get("snapshot").get("users"), criteriaBuilder.literal("$")), criteriaBuilder.function("json_array", List.class, criteriaBuilder.literal(email)));
                andPredicates.add(criteriaBuilder.equal(keySearch, 1));
            }
            andPredicates.add(criteriaBuilder.lessThan(root.get("createTime"), date));
            criteriaQuery.orderBy(criteriaBuilder.desc(root.get("createTime")));
            return criteriaBuilder.and(andPredicates.toArray(new Predicate[]{}));
        }, pageable);
    }
}
