package com.wolaidai.webot.data.mysql.entity.chat;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "user_state")
//客服实时状态表
public class UserStateEntity extends BaseEntity {
    
    public final static Integer TYPE_MANAGER = 0;
    public final static Integer TYPE_NORMAL = 1;
    
    public final static Integer STATE_LOGIN = 0;
    public final static Integer STATE_ONLINE = 1;
    public final static Integer STATE_BUSY = 2;
    public final static Integer STATE_RESTING = 3;
    public final static Integer STATE_LEAVE = 4;
    public final static Integer STATE_STUDYING = 5;
    public final static Integer STATE_EATING = 6;
    public final static Integer STATE_OFFLINE = 7;
    
    private String email;
    private Integer orgId;
    //客服类型，0：管理员；1：普通用户
    private Integer userType;
    private String name;
    private String nickName;
    private String workNumber;
    private Integer state;
    private Date createTime;
    private Date updateTime;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserStateEntity that = (UserStateEntity) o;
        return Objects.equals(getId(), that.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }
}
