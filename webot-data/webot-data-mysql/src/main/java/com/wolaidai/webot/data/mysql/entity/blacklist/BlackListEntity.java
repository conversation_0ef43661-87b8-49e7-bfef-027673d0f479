package com.wolaidai.webot.data.mysql.entity.blacklist;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

@Entity
@Table(name = "black_list")
public class BlackListEntity extends BaseEntity {

    public final static Integer STATUS_UNCHECK = 0;
    public final static Integer STATUS_PASS = 1;
    public final static Integer STATUS_REJECT = 2;
    public final static Integer STATUS_CANCEL = 3;

    private Integer orgId;
    private String name;
    private String phone;
    private String clientId;
    private Integer clientType;
    private String remark;
    private String creator;
    private Integer status = STATUS_UNCHECK;
    private Integer validDays = -1;
    private String reviewRemark;
    private String reviewCreator;
    private String cancelCreator;
    private Date createTime;
    private Date updateTime;
    private Date expireTime;
    private Date cancelTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getValidDays() {
        return validDays;
    }

    public void setValidDays(Integer validDays) {
        this.validDays = validDays;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }

    public String getReviewCreator() {
        return reviewCreator;
    }

    public void setReviewCreator(String reviewCreator) {
        this.reviewCreator = reviewCreator;
    }

    public String getCancelCreator() {
        return cancelCreator;
    }

    public void setCancelCreator(String cancelCreator) {
        this.cancelCreator = cancelCreator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

}
