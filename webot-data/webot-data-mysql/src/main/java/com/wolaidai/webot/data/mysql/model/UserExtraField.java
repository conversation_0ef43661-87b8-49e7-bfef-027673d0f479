package com.wolaidai.webot.data.mysql.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public class UserExtraField {

    private String nickName;
    private String workNumber;

    public UserExtraField(String s) {
        JSONObject json = JSON.parseObject(s);
        this.nickName = json.getString("nickName");
        this.workNumber = json.getString("workNumber");
    }

    public UserExtraField(JSONObject json) {
        this.nickName = json.getString("nickName");
        this.workNumber = json.getString("workNumber");
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

}
