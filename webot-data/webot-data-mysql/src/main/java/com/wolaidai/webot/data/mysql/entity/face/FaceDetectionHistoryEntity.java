package com.wolaidai.webot.data.mysql.entity.face;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

@Entity
@Table(name = "face_detection_history")
public class FaceDetectionHistoryEntity extends BaseEntity {

    private Integer orgId;
    @ManyToOne(fetch = FetchType.LAZY)
    private FaceDetectionEntity faceDetection;
    private String token;
    private String compareFileId;
    private Integer code;
    private String msg;
    private Date createTime;
    private String vendor;
    private String orderNo;
    private String vendorCode;

    public Integer getOrgId() { 
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public FaceDetectionEntity getFaceDetection() {
        return faceDetection;
    }

    public void setFaceDetection(FaceDetectionEntity faceDetection) {
        this.faceDetection = faceDetection;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getCompareFileId() {
        return compareFileId;
    }

    public void setCompareFileId(String compareFileId) {
        this.compareFileId = compareFileId;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    

}
