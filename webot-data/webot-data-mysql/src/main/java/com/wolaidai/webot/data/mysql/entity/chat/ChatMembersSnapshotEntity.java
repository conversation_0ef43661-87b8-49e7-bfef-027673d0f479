package com.wolaidai.webot.data.mysql.entity.chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "chat_members_snapshot")
public class ChatMembersSnapshotEntity extends BaseEntity {

    private Integer sessionId;
    @Type(type = "json")
    @Column(columnDefinition = "json", name = "user_id" )
    private JSONArray users = new JSONArray();
    private Date createTime;

    public Integer getSessionId() {
        return sessionId;
    }

    public void setSessionId(Integer sessionId) {
        this.sessionId = sessionId;
    }

    public JSONArray getUsers() {
        return users;
    }

    public void setUsers(JSONArray users) {
        this.users = users;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    public ChatMembersSnapshotEntity() {
    }

    public ChatMembersSnapshotEntity(Integer sessionId, List<String> users) {
        this.sessionId = sessionId;
        this.users = JSONArray.parseArray(JSON.toJSONString(users));;
        this.createTime = new Date();
    }
}
