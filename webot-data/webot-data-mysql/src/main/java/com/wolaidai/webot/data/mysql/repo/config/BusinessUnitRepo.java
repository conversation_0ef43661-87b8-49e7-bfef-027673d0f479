package com.wolaidai.webot.data.mysql.repo.config;

import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;
import org.springframework.data.repository.CrudRepository;

import java.util.Collection;
import java.util.List;

public interface BusinessUnitRepo extends CrudRepository<BusinessUnitEntity, Integer> {

    List<BusinessUnitEntity> findByOrgIdOrderByPositionAsc(Integer orgId);

    BusinessUnitEntity findByIdAndOrgId(Integer id, Integer orgId);

    List<BusinessUnitEntity> findByOrgId(Integer orgId);

    BusinessUnitEntity findByOrgIdAndName(Integer orgId, String name);

    List<BusinessUnitEntity> findByOrgIdAndIdIn(Integer orgId, Collection<Integer> ids);

}
