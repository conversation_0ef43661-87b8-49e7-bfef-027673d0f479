package com.wolaidai.webot.data.mysql.repo.impl;

import com.wolaidai.webot.data.mysql.entity.chat.QuickReplyEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ReplyCategoryEntity;
import com.wolaidai.webot.data.mysql.repo.ComplexQuickReplyRepo;
import com.wolaidai.webot.data.mysql.repo.QuickReplyRepo;
import com.wolaidai.webot.data.mysql.repo.ReplyCategoryRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class ComplexQuickReplyRepoImpl implements ComplexQuickReplyRepo {

    @Autowired
    private QuickReplyRepo quickReplyRepo;

    @Autowired
    private ReplyCategoryRepo replyCategoryRepo;

    public Page<QuickReplyEntity> findQuickReplies(Integer orgId, String email, Integer type, Integer categoryId, String key, Integer global, Pageable pageable) {
        return quickReplyRepo.findAll((Root<QuickReplyEntity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder)->{
            List<Predicate> andPredicates = new ArrayList<>();
            andPredicates.add(criteriaBuilder.equal(root.get("orgId"), orgId));
            if (StringUtils.hasText(email)) {
                andPredicates.add(criteriaBuilder.equal(root.get("creator"), email));
            }
            andPredicates.add(criteriaBuilder.equal(root.get("answerType"), type));
            andPredicates.add(criteriaBuilder.equal(root.get("global"), global));
            if (categoryId != -1) {
                Set<Integer> categoryIds = new HashSet<>();
                categoryIds.add(categoryId);
                ReplyCategoryEntity categoryEntity = replyCategoryRepo.findByIdAndOrgIdAndGlobal(categoryId, orgId, global);
                if (categoryEntity != null && !CollectionUtils.isEmpty(categoryEntity.getChildCategories())) {
                    categoryIds.addAll(categoryEntity.getChildCategories().stream().map(ReplyCategoryEntity :: getId).collect(Collectors.toList()));
                }
                andPredicates.add(criteriaBuilder.in(root.get("category").get("id")).value(categoryIds));
            }
            if (StringUtils.hasText(key)) {
                String criteriaString = "%" + key.replace("%", "/%").replace("_", "/_") + "%";
                if (QuickReplyEntity.ANSWER_TYPE_TEXT == type) {
                    andPredicates.add(criteriaBuilder.like(root.get("content"), criteriaString, '/'));
                } else {
                    andPredicates.add(criteriaBuilder.or(criteriaBuilder.like(root.get("remark"), criteriaString, '/'), criteriaBuilder.like(root.get("fileName"), criteriaString, '/')));
                }
            }
            criteriaQuery.orderBy(criteriaBuilder.desc(root.get("updateTime")));
            return criteriaBuilder.and(andPredicates.toArray(new Predicate[]{}));
        }, pageable);
    }

    @Override
    public List<QuickReplyEntity> findQuickReplies(Integer orgId, Integer categoryId, String creator, Integer global) {
        return quickReplyRepo.findAll((Root<QuickReplyEntity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder)->{
            List<Predicate> andPredicates = new ArrayList<>();
            andPredicates.add(criteriaBuilder.equal(root.get("orgId"), orgId));
            andPredicates.add(criteriaBuilder.equal(root.get("category").get("id"), categoryId));
            //查询个人快捷回复时带作者
            if (QuickReplyEntity.GLOBAL_N == global && StringUtils.hasText(creator)) {
                andPredicates.add(criteriaBuilder.equal(root.get("creator"), creator));
            }
            andPredicates.add(criteriaBuilder.equal(root.get("global"), global));
            return criteriaBuilder.and(andPredicates.toArray(new Predicate[]{}));
        });
    }
}
