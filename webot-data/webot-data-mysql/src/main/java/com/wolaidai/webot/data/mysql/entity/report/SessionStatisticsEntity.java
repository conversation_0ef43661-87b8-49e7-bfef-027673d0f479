package com.wolaidai.webot.data.mysql.entity.report;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

//会话统计
@Entity
@Table(name = "session_statistics")
public class SessionStatisticsEntity extends BaseEntity {

    private Integer orgId;

    private Integer sessionId;
    //新老客户 0-新客户,1-老客户
    private Integer newOldCustomer;
    //咨询机器人消息数
    private Integer seekBotCnt;
    //机器人回复数
    private Integer botAnswerCnt;
    //咨询人工消息数
    private Integer seekManualCnt;
    //人工回复数
    private Integer manualAnswerCnt;
    //撤回消息数
    private Integer recallCnt;
    //转接次数
    private Integer transferCnt;
    //24小时首解 0-否,1-是
    private Integer twentyFourFirst;
    private Date createTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getSessionId() {
        return sessionId;
    }

    public void setSessionId(Integer sessionId) {
        this.sessionId = sessionId;
    }

    public Integer getNewOldCustomer() {
        return newOldCustomer;
    }

    public void setNewOldCustomer(Integer newOldCustomer) {
        this.newOldCustomer = newOldCustomer;
    }

    public Integer getSeekBotCnt() {
        return seekBotCnt;
    }

    public void setSeekBotCnt(Integer seekBotCnt) {
        this.seekBotCnt = seekBotCnt;
    }

    public Integer getBotAnswerCnt() {
        return botAnswerCnt;
    }

    public void setBotAnswerCnt(Integer botAnswerCnt) {
        this.botAnswerCnt = botAnswerCnt;
    }

    public Integer getSeekManualCnt() {
        return seekManualCnt;
    }

    public void setSeekManualCnt(Integer seekManualCnt) {
        this.seekManualCnt = seekManualCnt;
    }

    public Integer getManualAnswerCnt() {
        return manualAnswerCnt;
    }

    public void setManualAnswerCnt(Integer manualAnswerCnt) {
        this.manualAnswerCnt = manualAnswerCnt;
    }

    public Integer getRecallCnt() {
        return recallCnt;
    }

    public void setRecallCnt(Integer recallCnt) {
        this.recallCnt = recallCnt;
    }

    public Integer getTransferCnt() {
        return transferCnt;
    }

    public void setTransferCnt(Integer transferCnt) {
        this.transferCnt = transferCnt;
    }

    public Integer getTwentyFourFirst() {
        return twentyFourFirst;
    }

    public void setTwentyFourFirst(Integer twentyFourFirst) {
        this.twentyFourFirst = twentyFourFirst;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
