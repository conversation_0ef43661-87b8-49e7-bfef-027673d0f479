package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.BusinessTypeCollectEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface BusinessTypeCollectRepo extends JpaRepository<BusinessTypeCollectEntity, Integer> {

    BusinessTypeCollectEntity findByOrgIdAndEmailAndBusinessTypeId(Integer orgId, String email, Integer businessTypeId);

    BusinessTypeCollectEntity findByOrgIdAndEmailAndBusinessUnitId(Integer orgId, String email, Integer businessUnitId);

    @Modifying
    @Transactional
    @Query(value = "update business_type_collect set position = ?4, update_time = now() where org_id = ?1 and email = ?2 and business_unit_id = ?3", nativeQuery = true)
    void updatePositionByUnitId(Integer orgId, String email, Integer unitId, Integer position);

    @Modifying
    @Transactional
    @Query(value = "update business_type_collect set position = ?4, update_time = now() where org_id = ?1 and email = ?2 and business_type_id = ?3", nativeQuery = true)
    void updatePositionByTypeId(Integer orgId, String email, Integer typeId, Integer position);


    @Query(value = "select * from business_type_collect where org_id = ?1 and email = ?2 order by position,id desc", nativeQuery = true)
    List<BusinessTypeCollectEntity> findByOrgIdAndEmailOrderByPositionAndId(Integer orgId, String email);
}
