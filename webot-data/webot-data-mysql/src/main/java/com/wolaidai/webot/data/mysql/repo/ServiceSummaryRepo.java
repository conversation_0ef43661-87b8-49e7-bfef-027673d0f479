package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.ServiceSummaryEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface ServiceSummaryRepo extends BaseRepo<ServiceSummaryEntity, Integer> {

    ServiceSummaryEntity findByOrgIdAndSession(Integer orgId, SessionListEntity session);

    List<ServiceSummaryEntity> findByOrgIdAndSessionIdIn(Integer orgId, Set<Integer> sessionIds);

    ServiceSummaryEntity findFirstByOrgIdAndSessionId(Integer orgId, Integer sessionId);

    ServiceSummaryEntity findBySessionId(Integer sessionId);

    long countByOrgIdAndUnitId(Integer orgId, Integer unitId);

    long countByOrgIdAndUnitIdAndTypeIdIn(Integer orgId, Integer unitId, Collection<Integer> typeIds);
    
    long countBySessionIdIn(Collection<Integer> sessionIds);
}
