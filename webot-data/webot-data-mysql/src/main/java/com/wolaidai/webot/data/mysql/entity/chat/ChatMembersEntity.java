package com.wolaidai.webot.data.mysql.entity.chat;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "chat_members")
public class ChatMembersEntity extends BaseEntity {

    public final static Integer STATUS_ACTIVE = 1;   //直接展示在个人会话列表
    public final static Integer STATUS_INACTIVE = 0;

    @ManyToOne(fetch = FetchType.LAZY)
    private ChatSessionEntity session;
    @ManyToOne(fetch = FetchType.LAZY)
    private UserStateEntity user;
    private Integer unreadMsgCount;
    private Integer status;
    private Date createTime;
    private Date updateTime;

    public ChatSessionEntity getSession() {
        return session;
    }

    public void setSession(ChatSessionEntity session) {
        this.session = session;
    }

    public UserStateEntity getUser() {
        return user;
    }

    public void setUser(UserStateEntity user) {
        this.user = user;
    }

    public Integer getUnreadMsgCount() {
        return unreadMsgCount;
    }

    public void setUnreadMsgCount(Integer unreadMsgCount) {
        this.unreadMsgCount = unreadMsgCount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public ChatMembersEntity() {
    }

    public ChatMembersEntity(Integer status, ChatSessionEntity session, UserStateEntity stateEntity) {
        this.status = status;
        this.session = session;
        this.user = stateEntity;
        this.unreadMsgCount = 0;
        this.createTime = new Date();
        this.updateTime = createTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChatMembersEntity that = (ChatMembersEntity) o;
        return Objects.equals(session.getId(), that.session.getId()) && Objects.equals(user.getId(), that.user.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(session.getId(), user.getId());
    }
}
