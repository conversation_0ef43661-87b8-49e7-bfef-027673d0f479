package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.report.SessionReportEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface SessionReportRepo extends BaseRepo<SessionReportEntity, Integer> {

    List<SessionReportEntity> findByOrgIdAndBusinessIdNotAndDateGreaterThanEqualAndDateLessThanEqual(Integer orgId, Integer businessId, Date startTime, Date endTime);

    List<SessionReportEntity> findByOrgIdAndBusinessIdAndDateGreaterThanEqualAndDateLessThanEqual(Integer orgId, Integer businessId, Date startTime, Date endTime);

    @Modifying
    @Transactional
    void deleteAllByOrgIdAndDateGreaterThanEqualAndDateLessThanEqual(Integer orgId, Date startTime, Date endTime);

}
