package com.wolaidai.webot.data.mysql.entity.audit;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import com.wolaidai.webot.data.mysql.enums.AuditAction;
import com.wolaidai.webot.data.mysql.enums.AuditModule;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name="audit_log")
public class AuditLogEntity extends BaseEntity {
    private Integer orgId;
    private Integer productId;
    private String username;
    private String supportUsername;
    @Enumerated(EnumType.STRING)
    private AuditAction action;
    @Enumerated(EnumType.STRING)
    private AuditModule module;
    private String sourceId;
    private String path;
    private String remark;
    private String ip;
    private Date createTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSupportUsername() {
        return supportUsername;
    }

    public void setSupportUsername(String supportUsername) {
        this.supportUsername = supportUsername;
    }

    public AuditAction getAction() {
        return action;
    }

    public void setAction(AuditAction action) {
        this.action = action;
    }

    public AuditModule getModule() {
        return module;
    }

    public void setModule(AuditModule module) {
        this.module = module;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
