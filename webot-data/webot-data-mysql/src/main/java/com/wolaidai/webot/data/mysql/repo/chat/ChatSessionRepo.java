package com.wolaidai.webot.data.mysql.repo.chat;

import com.wolaidai.webot.data.mysql.entity.chat.ChatSessionEntity;
import com.wolaidai.webot.data.mysql.repo.BaseRepo;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ChatSessionRepo extends BaseRepo<ChatSessionEntity, Integer> {

    @Query(value = "select * from chat_session cs inner join chat_members cm on cs.id = cm.session_id" +
            " where cs.org_id = ?1" +
            " and cm.user_id = ?2" +
            " order by if (cm.update_time > cs.last_msg_time, cm.update_time, cs.last_msg_time) desc", nativeQuery = true)
    List<ChatSessionEntity> findByOrgIdAndUserId(Integer orgId, Integer userId);
}
