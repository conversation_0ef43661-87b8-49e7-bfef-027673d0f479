package com.wolaidai.webot.data.mysql.entity.config;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "business_unit")
//业务单元表
public class BusinessUnitEntity extends BaseEntity {

    public final static Integer ACTIVE_STATUS = 1;
    public final static Integer INACTIVE_STATUS = 0;

    private Integer orgId;
    private String name;
    private Integer position = 0;
    private Integer status = INACTIVE_STATUS;
    private Date createTime;
    private Date updateTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
