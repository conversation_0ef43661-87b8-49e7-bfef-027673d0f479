package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsAnalyzeEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeAnalyzeEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface SessionListRepo extends BaseRepo<SessionListEntity, Integer> {

    @Query(value = "select client_id from session_list s where s.org_id=?1 and s.client_id=?2 and s.create_time > ?3 and s.status in (0,1) order by create_time desc limit 1",nativeQuery = true)
    String findClientIdByOnlineStatusAndCreateTime(Integer orgId, String clientId, Date createTime);

    SessionListEntity findByOrgIdAndId(Integer orgId, Integer id);

    SessionListEntity findBySessionKey(String sessionKey);

    List<SessionListEntity> findByGcidIn(Collection<String> gcids);

    SessionListEntity findByOrgIdAndSessionKey(Integer orgId, String sessionKey);
    @Query(value = "select s from SessionListEntity s where s.orgId = ?1 " +
            "and (s.serviceUser = ?2 or s.fromServiceUser = ?2 or s.lastServiceUser = ?2) " +
            "and s.customerName like ?3 order by s.createTime desc")
    List<SessionListEntity> findRecordByParams(Integer orgId, String email, String customerName);

    @Query(value = "select last_service_user from session_list s where s.org_id=?1 and s.client_id=?2 order by create_time desc limit 1",nativeQuery = true)
    String findLastServiceEmailByOrgIdAndClientId(Integer orgId,String clientId);
    
    @Query(value = "select last_service_user from session_list s where s.org_id=?1 and s.client_id=?2 and s.create_time>?3 order by create_time desc limit 1",nativeQuery = true)
    String findLastServiceEmailByOrgIdAndClientIdAndCreateTime(Integer orgId,String clientId,Date createTime);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsAnalyzeEntity(t.businessId, count(t), sum(t.durationSecond)) "
            + "from SessionListEntity t where t.orgId = :orgId and t.queueTime >= :startTime and t.queueTime <= :endTime and t.status = 2 group by t.businessId")
    List<StatisticsAnalyzeEntity> getBusinessStatisticsByTime(@Param("orgId") Integer orgId,
                                                              @Param("startTime") Date startTime,
                                                              @Param("endTime") Date endTime);

    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeAnalyzeEntity(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(t.queueTime) / 1800) * 1800) AS timeslice, count(t), sum(t.durationSecond)) "
            + "from SessionListEntity t where t.orgId = :orgId and t.queueTime >= :startTime and t.queueTime <= :endTime and t.status = 2 group by timeslice")
    List<StatisticsTimeAnalyzeEntity> getTimeStatisticsByTime(@Param("orgId") Integer orgId,
                                                              @Param("startTime") Date startTime,
                                                              @Param("endTime") Date endTime);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity(DATE(t.createTime) AS createDate, count(t)) from SessionListEntity t "
            + "where t.orgId = :orgId and t.createTime >= :startTime and t.createTime <= :endTime and t.status = 2 group by createDate")
    List<StatisticsTimeCountEntity> getOfflineCountByDate(@Param("orgId") Integer orgId,
                                                          @Param("startTime") Date startTime,
                                                          @Param("endTime") Date endTime);

    List<SessionListEntity> findByStatusAndCreateTimeGreaterThanEqualAndCreateTimeLessThanOrderByCreateTimeAsc(Integer status, Date beginTime, Date endTime);
    List<SessionListEntity> findByOrgIdAndStatusAndCreateTimeGreaterThanEqualAndCreateTimeLessThanOrderByCreateTimeAsc(Integer orgId, Integer status, Date beginTime, Date endTime);
    List<SessionListEntity> findByOrgIdAndStatusAndLastServiceUserInAndCreateTimeGreaterThanEqualAndCreateTimeLessThanOrderByCreateTimeAsc(Integer orgId, Integer status, Collection<String> emails, Date beginTime, Date endTime);
    @Query(value = "select * from session_list s where s.org_id=?1 and s.status=?2 and s.create_time>=?4 and s.create_time<?5 and (s.from_service_user in ?3 or s.last_service_user in ?3 or s.service_user in ?3 or s.id in (select t.session_id from session_transfer_list t where t.org_id=?1 and t.status=1 and t.update_time>=?4 and t.update_time<?5 and (t.from_service_user in ?3 or t.to_service_user in ?3))) order by s.create_time asc", nativeQuery = true)
    List<SessionListEntity> findByOrgIdAndStatusAndServiceUserInAndCreateTime(Integer orgId, Integer status, Collection<String> emails, Date beginTime, Date endTime);

    @Query(value = "select * from session_list s where s.org_id=?1 and s.status=?2 and s.create_time>=?3 and s.create_time<?4", nativeQuery = true)
    List<SessionListEntity> findByOrgIdAndStatusAndCreateTime(Integer orgId, Integer status, Date beginTime, Date endTime);

    Integer countByOrgIdAndCreateTimeGreaterThanEqual(Integer orgId, Date beginTime);

    @Query(value = "select count(*) from session_list s where s.org_id=?1 and status=?3 and s.create_time>=?4 and s.create_time<?5 and (s.from_service_user = ?2 or s.last_service_user = ?2 or s.service_user = ?2 or s.id in (select t.session_id from session_transfer_list t where t.org_id=?1 and t.status=1 and t.update_time>=?4 and t.update_time<?5 and (t.from_service_user = ?2 or t.to_service_user = ?2)))", nativeQuery = true)
    Integer countByOrgIdAndServiceUserAndStatusAndCreateTime(Integer orgId, String email,Integer status, Date beginTime, Date endTime);

    Integer countByOrgIdAndFirstRespTimeoutAndCreateTimeGreaterThanEqual(Integer orgId, Integer firstRespTimeout, Date beginTime);
    Integer countByOrgIdAndAvgRespTimeoutAndCreateTimeGreaterThanEqual(Integer orgId, Integer avgRespTimeout, Date beginTime);
    Integer countByOrgIdAndRespTimeoutAndCreateTimeGreaterThanEqual(Integer orgId, Integer respTimeout, Date beginTime);

    @Query(value = "select count(DISTINCT(customer_name)) from session_list where org_id=?1 and client_id=?2 and create_time>=?3 and create_time<=?4 and customer_name not like '访客%'", nativeQuery = true)
    Integer countRegCustomerByOrgIdAndClientIdAndTime(Integer orgId, String clientId, Date beginTime, Date endTime);
}
