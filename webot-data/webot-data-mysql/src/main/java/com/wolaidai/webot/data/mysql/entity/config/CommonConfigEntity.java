package com.wolaidai.webot.data.mysql.entity.config;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

@Entity
@Table(name = "common_config")
public class CommonConfigEntity extends BaseEntity {
    public final static String TYPE_WORKTIME_CONFIG = "worktime";
    public final static String TYPE_WORKBENCH_CONFIG = "workbench";
    public final static String TYPE_WORKBENCH_CONFIG_STATUS = "workbench_status";
    public final static String TYPE_SERVICEUSER_CONFIG = "serviceuser";
    public final static String TYPE_AUTOREPLY_CONFIG = "autoreply";
    public final static String TYPE_SATISFACTION_CONFIG = "satisfaction";
    public final static String TYPE_RESP_TIMEOUT_CONFIG = "respTimeout";
    public final static String TYPE_MAX_RECEPTION = "maxReception";
    public final static String TYPE_DEFAULT_RECEPTION = "defaultReception";
    public final static String TYPE_SUMMARY_SENSITIVE = "summarySensitive";

    private Integer orgId = -1;
    private String type;
    private String subType1;
    private String subType2;
    private String content;
    private Date createTime;
    private Date updateTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubType1() {
        return subType1;
    }

    public void setSubType1(String subType1) {
        this.subType1 = subType1;
    }

    public String getSubType2() {
        return subType2;
    }

    public void setSubType2(String subType2) {
        this.subType2 = subType2;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
