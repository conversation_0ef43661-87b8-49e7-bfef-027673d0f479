package com.wolaidai.webot.data.mysql.entity.account;

import com.wolaidai.webot.data.mysql.constant.Database;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name="user", catalog = Database.AUTH)
public class UserEntity extends BaseEntity {
    public static final int INACTIVE_STATUS = 0;
    public static final int ACTIVE_STATUS = 1;
    private String email;
    private String mobile;
    private String password;
    private String fullName;
    private Integer status;
    private Integer failLoginNum;
    private Date lastFailTime;
    private Date createTime;
    private Date updateTime;
    @Transient
    private String supportUser;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFailLoginNum() {
        return failLoginNum;
    }

    public void setFailLoginNum(Integer failLoginNum) {
        this.failLoginNum = failLoginNum;
    }

    public Date getLastFailTime() {
        return lastFailTime;
    }

    public void setLastFailTime(Date lastFailTime) {
        this.lastFailTime = lastFailTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSupportUser() {
        return supportUser;
    }

    public void setSupportUser(String supportUser) {
        this.supportUser = supportUser;
    }
}
