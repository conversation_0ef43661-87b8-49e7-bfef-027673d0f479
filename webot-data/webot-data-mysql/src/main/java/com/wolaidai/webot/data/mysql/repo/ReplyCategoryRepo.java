package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.ReplyCategoryEntity;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface ReplyCategoryRepo extends CrudRepository<ReplyCategoryEntity, Integer> {

    ReplyCategoryEntity findByOrgIdAndNameAndCreatorAndTypeAndGlobal(Integer orgId, String name, String creator, Integer type, Integer global);

    ReplyCategoryEntity findByIdAndOrgIdAndCreatorAndGlobal(Integer id, Integer orgId, String creator, Integer global);

    ReplyCategoryEntity findByOrgIdAndNameAndTypeAndGlobal(Integer orgId, String name, Integer type, Integer global);

    ReplyCategoryEntity findByIdAndOrgIdAndGlobal(Integer id, Integer orgId, Integer global);

    List<ReplyCategoryEntity> findByOrgIdAndTypeAndGlobalAndParentCategoryIsNull(Integer orgId, Integer type, Integer global);

    List<ReplyCategoryEntity> findByOrgIdAndCreatorAndTypeAndGlobalAndParentCategoryIsNull(Integer orgId, String email, Integer type, Integer global);

    List<ReplyCategoryEntity> findByOrgIdAndCreatorAndTypeAndGlobalAndParentCategoryIsNullOrderByPositionAsc(Integer orgId, String creator, Integer type, Integer global);

    List<ReplyCategoryEntity> findByOrgIdAndTypeAndGlobalAndParentCategoryIsNullOrderByPositionAsc(Integer orgId, Integer type, Integer global);

    ReplyCategoryEntity findByOrgIdAndNameAndCreatorAndTypeAndGlobalAndParentCategory(Integer orgId, String name, String creator, Integer type, Integer global, ReplyCategoryEntity parentCategoryId);

    ReplyCategoryEntity findByOrgIdAndNameAndTypeAndGlobalAndParentCategory(Integer orgId, String name, Integer type, Integer global, ReplyCategoryEntity parentCategoryId);


    

}
