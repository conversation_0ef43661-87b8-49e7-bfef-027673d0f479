package com.wolaidai.webot.data.mysql.resultEntity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

public class StatisticSatisfactionEntity {

    private Integer sessionId;
    private Integer type;
    private Integer level;
    private JSONArray labels;
    private String content;

    public StatisticSatisfactionEntity() {
    }

    public StatisticSatisfactionEntity(Integer sessionId, Integer type, Integer level, Object labels, String content) {
        this.sessionId = sessionId;
        this.type = type;
        this.level = level;
        if (null != labels) {
            this.labels = JSON.parseArray(labels.toString());
        }
        this.content = content;
    }

    public Integer getSessionId() {
        return sessionId;
    }

    public void setSessionId(Integer sessionId) {
        this.sessionId = sessionId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public JSONArray getLabels() {
        return labels;
    }

    public void setLabels(JSONArray labels) {
        this.labels = labels;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
