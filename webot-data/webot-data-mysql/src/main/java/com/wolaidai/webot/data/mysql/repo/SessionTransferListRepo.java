package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.SessionTransferListEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface SessionTransferListRepo extends BaseRepo<SessionTransferListEntity, Integer> {

    @Modifying
    @Transactional
    @Query(value = "update session_transfer_list s set s.status = ?3,s.update_time=?4 where s.id=?1 and s.org_id=?2 and s.status=0",nativeQuery = true)
    void updateStatusById(Integer id,Integer orgId,Integer status,Date updateTime);
    
    @Query(value = "select * from session_transfer_list where session_id in ?1", nativeQuery = true)
    List<SessionTransferListEntity> findBySessionListIds(Collection<Integer> ids);

    @Query(value = "select * from session_transfer_list where to_service_user = ?1 and status = ?2", nativeQuery = true)
    List<SessionTransferListEntity> findByToServiceUser(String toServiceUser, Integer status);

    long countByOrgIdAndSessionIdAndStatus(Integer orgId, Integer sessionId, Integer status);
    
}
