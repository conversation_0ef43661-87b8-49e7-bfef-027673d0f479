package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;

public interface ComplexQueueListRepo {

    Page<QueueListEntity> findByOrgIdAndStatusAndTime(Integer orgId, Integer status, Date startTime, Date endTime, String uuid, Integer userId, Pageable pageable);

}
