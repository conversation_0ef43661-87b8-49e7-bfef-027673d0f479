package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.report.SessionStatisticsEntity;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface SessionStatisticsRepo extends BaseRepo<SessionStatisticsEntity, Integer> {

    List<SessionStatisticsEntity> findBySessionIdIn(Set<Integer> sessionIds);

    SessionStatisticsEntity findBySessionId(Integer sessionId);

    @Query("select s.sessionId from SessionStatisticsEntity s where " + 
           "date(s.createTime)>=date(:startTime) and date(s.createTime)<=date(:endTime) " + 
           "and (:userMsgCountMin is null or (s.seekBotCnt+s.seekManualCnt) >= :userMsgCountMin)" +
           "and (:userMsgCountMax is null or (s.seekBotCnt+s.seekManualCnt) <= :userMsgCountMax)" +
           "and (:serviceMsgCountMin is null or s.manualAnswerCnt >= :serviceMsgCountMin)" +
           "and (:serviceMsgCountMax is null or s.manualAnswerCnt <= :serviceMsgCountMax)")
    List<Integer> findIdsByMsgCount(@Param("startTime") Date startTime, 
                                    @Param("endTime") Date endTime, 
                                    @Param("userMsgCountMin") Integer userMsgCountMin, 
                                    @Param("userMsgCountMax") Integer userMsgCountMax, 
                                    @Param("serviceMsgCountMin") Integer serviceMsgCountMin, 
                                    @Param("serviceMsgCountMax") Integer serviceMsgCountMax);

}
