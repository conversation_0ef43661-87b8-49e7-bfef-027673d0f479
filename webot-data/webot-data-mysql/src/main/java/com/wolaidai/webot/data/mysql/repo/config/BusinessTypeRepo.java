package com.wolaidai.webot.data.mysql.repo.config;

import com.wolaidai.webot.data.mysql.entity.config.BsTypeSampleEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

public interface BusinessTypeRepo extends CrudRepository<BusinessTypeEntity, Integer> {

    List<BusinessTypeEntity> findByOrgIdAndUnitIdAndParentTypeIsNull(Integer orgId, Integer unitId, Sort sort);
    
    BusinessTypeEntity findByIdAndOrgId(Integer id, Integer orgId);

    BusinessTypeEntity findByIdAndOrgIdAndUnitId(Integer id, Integer orgId, Integer unitId);

    BusinessTypeEntity findByOrgIdAndParentTypeIdAndTitle(Integer orgId, Integer parentId, String title);

    BusinessTypeEntity findByOrgIdAndUnitIdAndTitleAndParentTypeIsNull(Integer orgId, Integer unitId, String title);

    @Modifying
    @Transactional
    @Query(value = "delete from business_type where org_id = ?1 and unit_id = ?2", nativeQuery = true)
    void deleteByOrgIdAndUnit(Integer orgId, Integer unitId);


    @Query("select new com.wolaidai.webot.data.mysql.entity.config.BsTypeSampleEntity(t.id, t.parentType.id AS parentId, t.title)"
            + " from BusinessTypeEntity t where t.orgId = :orgId")
    List<BsTypeSampleEntity> findByOrgId(@Param("orgId") Integer orgId);

    List<BusinessTypeEntity> findByOrgIdAndIdIn(Integer orgId, Collection<Integer> ids);
}
