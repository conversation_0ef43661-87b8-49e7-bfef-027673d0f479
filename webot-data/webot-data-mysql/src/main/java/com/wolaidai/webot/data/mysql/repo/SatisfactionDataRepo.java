package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticSatisfactionEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeAnalyzeEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface SatisfactionDataRepo extends BaseRepo<SatisfactionDataEntity, Integer> {

    List<SatisfactionDataEntity> findByOrgIdAndSessionIdInAndCreateTimeGreaterThanEqualAndCreateTimeLessThan(Integer orgId, Collection<Integer> sessionIds, Date start, Date end);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity(DATE(t.session.createTime) AS createDate, count(t)) from SatisfactionDataEntity t "
            + "where t.orgId = :orgId and t.session.createTime >= :startTime and t.session.createTime <= :endTime and t.session.status = 2 and t.status=1 group by createDate")
    List<StatisticsTimeCountEntity> getEvaluatedCountByDate(@Param("orgId") Integer orgId,
                                                           @Param("startTime") Date startTime,
                                                           @Param("endTime") Date endTime);

    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity(DATE(t.session.createTime) AS createDate, count(t)) from SatisfactionDataEntity t "
            + "where t.orgId = :orgId and t.session.createTime >= :startTime and t.session.createTime <= :endTime and t.session.status = 2 and t.type = 2 group by createDate")
    List<StatisticsTimeCountEntity> getInviteCountByDate(@Param("orgId") Integer orgId,
                                                         @Param("startTime") Date startTime,
                                                         @Param("endTime") Date endTime);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeAnalyzeEntity(DATE(t.session.createTime) AS createDate, t.level, count(t)) from SatisfactionDataEntity t "
            + "where t.orgId = :orgId and t.session.createTime >= :startTime and t.session.createTime <= :endTime and t.session.status = 2 and t.status = 1 group by createDate, t.level")
    List<StatisticsTimeAnalyzeEntity> getLevelCountByDate(@Param("orgId") Integer orgId,
                                                          @Param("startTime") Date startTime,
                                                          @Param("endTime") Date endTime);
                                                          
    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticSatisfactionEntity(t.session.id AS sessionId, t.type, t.level, t.labels, t.content)"
            + " from SatisfactionDataEntity t where t.orgId = :orgId and t.status = :status and t.session.id in :sessionIds")
    List<StatisticSatisfactionEntity> findByOrgIdAndStatusAndSessionIdIn(@Param("orgId") Integer orgId,
                                                                         @Param("status") Integer status,
                                                                         @Param("sessionIds") Set<Integer> sessionIds);
}
