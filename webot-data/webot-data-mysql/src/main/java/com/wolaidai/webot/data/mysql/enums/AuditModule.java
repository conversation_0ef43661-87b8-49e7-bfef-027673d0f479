package com.wolaidai.webot.data.mysql.enums;

public enum AuditModule {
    WORKTIME_CONFIG("上下班时间"),
    WORKBENCH_CONFIG("工作台提醒"),
    SERVICEUSER_CONFIG("在线客服分配方式设置"),
    AUTOREPLY_CONFIG("会话自动应答设置"),
    SATISFACTION_CONFIG("人工满意度设置"),
    EXPORT_REPORT("导出报表"),
    SERVICESUMMARY_CONFIG("服务小结设置"),
    RESP_TIMEOUT_CONFIG("响应超时设置");
    private String remark;
    AuditModule(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }
}
