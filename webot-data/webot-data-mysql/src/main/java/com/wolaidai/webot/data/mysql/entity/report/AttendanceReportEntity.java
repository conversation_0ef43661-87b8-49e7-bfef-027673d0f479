package com.wolaidai.webot.data.mysql.entity.report;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

@Entity
@Table(name = "attendance_report")
//客服考勤统计数据表
public class AttendanceReportEntity extends BaseEntity {

    private String email;
    private Integer orgId;
    private Date dataTime;
    private String workNumber;
    private String nickName;
    private Date firstLogin;
    private Date lastLogout;
    private Integer totalLogin = 0;
    private Integer totalOnline = 0;
    private Integer totalBusy = 0;
    private Integer totalRest = 0;
    private Integer totalLeave = 0;
    private Integer totalEat = 0;
    private Integer totalStudy = 0;
    private Integer sessionCount = 0;
    private Long sessionDuration = 0l;
    private Long sessionCostAvg = 0l;
    private Long totalFirstRsp = 0l;
    private Integer firstAnswerAvg = 0;
    private Float totalAvgRsp = 0f;
    private Integer answerAvg = 0;
    private Integer evaluationCount = 0;
    private Float evaluationPercent = 0f;
    private Integer noEvaluationCount = 0;
    private Float noEvaluationPercent = 0f;
    private Integer starOne = 0;
    private Integer starTwo = 0;
    private Integer starThree = 0;
    private Integer starFour = 0;
    private Integer starFive = 0;
    private Float satisfactionPercent = 0f;
    private Date createTime;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Date getFirstLogin() {
        return firstLogin;
    }

    public void setFirstLogin(Date firstLogin) {
        this.firstLogin = firstLogin;
    }

    public Date getLastLogout() {
        return lastLogout;
    }

    public void setLastLogout(Date lastLogout) {
        this.lastLogout = lastLogout;
    }

    public Integer getTotalLogin() {
        return totalLogin;
    }

    public void setTotalLogin(Integer totalLogin) {
        this.totalLogin = totalLogin;
    }

    public Integer getTotalOnline() {
        return totalOnline;
    }

    public void setTotalOnline(Integer totalOnline) {
        this.totalOnline = totalOnline;
    }

    public Integer getTotalBusy() {
        return totalBusy;
    }

    public void setTotalBusy(Integer totalBusy) {
        this.totalBusy = totalBusy;
    }

    public Integer getTotalRest() {
        return totalRest;
    }

    public void setTotalRest(Integer totalRest) {
        this.totalRest = totalRest;
    }

    public Integer getTotalLeave() {
        return totalLeave;
    }

    public void setTotalLeave(Integer totalLeave) {
        this.totalLeave = totalLeave;
    }

    public Integer getTotalEat() {
        return totalEat;
    }

    public void setTotalEat(Integer totalEat) {
        this.totalEat = totalEat;
    }

    public Integer getTotalStudy() {
        return totalStudy;
    }

    public void setTotalStudy(Integer totalStudy) {
        this.totalStudy = totalStudy;
    }

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public Long getSessionDuration() {
        return sessionDuration;
    }

    public void setSessionDuration(Long sessionDuration) {
        this.sessionDuration = sessionDuration;
    }

    public Long getSessionCostAvg() {
        return sessionCostAvg;
    }

    public void setSessionCostAvg(Long sessionCostAvg) {
        this.sessionCostAvg = sessionCostAvg;
    }

    public Long getTotalFirstRsp() {
        return totalFirstRsp;
    }

    public void setTotalFirstRsp(Long totalFirstRsp) {
        this.totalFirstRsp = totalFirstRsp;
    }

    public Integer getFirstAnswerAvg() {
        return firstAnswerAvg;
    }

    public void setFirstAnswerAvg(Integer firstAnswerAvg) {
        this.firstAnswerAvg = firstAnswerAvg;
    }

    public Float getTotalAvgRsp() {
        return totalAvgRsp;
    }

    public void setTotalAvgRsp(Float totalAvgRsp) {
        this.totalAvgRsp = totalAvgRsp;
    }

    public Integer getAnswerAvg() {
        return answerAvg;
    }

    public void setAnswerAvg(Integer answerAvg) {
        this.answerAvg = answerAvg;
    }

    public Integer getEvaluationCount() {
        return evaluationCount;
    }

    public void setEvaluationCount(Integer evaluationCount) {
        this.evaluationCount = evaluationCount;
    }

    public Float getEvaluationPercent() {
        return evaluationPercent;
    }

    public void setEvaluationPercent(Float evaluationPercent) {
        this.evaluationPercent = evaluationPercent;
    }

    public Integer getNoEvaluationCount() {
        return noEvaluationCount;
    }

    public void setNoEvaluationCount(Integer noEvaluationCount) {
        this.noEvaluationCount = noEvaluationCount;
    }

    public Float getNoEvaluationPercent() {
        return noEvaluationPercent;
    }

    public void setNoEvaluationPercent(Float noEvaluationPercent) {
        this.noEvaluationPercent = noEvaluationPercent;
    }

    public Integer getStarOne() {
        return starOne;
    }

    public void setStarOne(Integer starOne) {
        this.starOne = starOne;
    }

    public Integer getStarTwo() {
        return starTwo;
    }

    public void setStarTwo(Integer starTwo) {
        this.starTwo = starTwo;
    }

    public Integer getStarThree() {
        return starThree;
    }

    public void setStarThree(Integer starThree) {
        this.starThree = starThree;
    }

    public Integer getStarFour() {
        return starFour;
    }

    public void setStarFour(Integer starFour) {
        this.starFour = starFour;
    }

    public Integer getStarFive() {
        return starFive;
    }

    public void setStarFive(Integer starFive) {
        this.starFive = starFive;
    }

    public Float getSatisfactionPercent() {
        return satisfactionPercent;
    }

    public void setSatisfactionPercent(Float satisfactionPercent) {
        this.satisfactionPercent = satisfactionPercent;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
