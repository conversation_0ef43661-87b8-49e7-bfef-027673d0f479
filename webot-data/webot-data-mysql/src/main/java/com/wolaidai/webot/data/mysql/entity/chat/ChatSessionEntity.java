package com.wolaidai.webot.data.mysql.entity.chat;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.*;
import java.util.*;

@Entity
@Table(name = "chat_session")
public class ChatSessionEntity extends BaseEntity {

    private Integer orgId;
    @ManyToOne(fetch = FetchType.LAZY)
    private ChatRoomEntity room;
    private String lastMsg;
    private String lastMsgSender;
    private Date lastMsgTime;
    private String creator;
    private Date createTime;
    private Date updateTime;
    @OneToMany(mappedBy = "session", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("createTime asc")
    private List<ChatMembersEntity> members = new ArrayList<>();

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public ChatRoomEntity getRoom() {
        return room;
    }

    public void setRoom(ChatRoomEntity room) {
        this.room = room;
    }

    public String getLastMsg() {
        return lastMsg;
    }

    public void setLastMsg(String lastMsg) {
        this.lastMsg = lastMsg;
    }

    public String getLastMsgSender() {
        return lastMsgSender;
    }

    public void setLastMsgSender(String lastMsgSender) {
        this.lastMsgSender = lastMsgSender;
    }

    public Date getLastMsgTime() {
        return lastMsgTime;
    }

    public void setLastMsgTime(Date lastMsgTime) {
        this.lastMsgTime = lastMsgTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public List<ChatMembersEntity> getMembers() {
        return members;
    }

    public void setMembers(List<ChatMembersEntity> members) {
        this.members = members;
    }
}
