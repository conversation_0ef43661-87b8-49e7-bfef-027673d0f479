package com.wolaidai.webot.data.mysql.entity.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "task")
public class TaskEntity extends BaseEntity {

    public static final Integer PORT_EXPORT = 1;
    public static final Integer PORT_IMPORT = 2;

    public static final Integer NOT_START_STATUS = 0;
    public static final Integer EXECUTING_STATUS = 1;
    public static final Integer SUCCESS_STATUS = 2;
    public static final Integer FAIL_STATUS = 3;

    public static final String ATTENDANCE_REPORT = "AttendanceReport";
    public static final String STATE_REPORT = "StateReport";
    public static final String SESSION_DETAIL_REPORT = "SessionDetailReport";
    public static final String SERVICE_DATA_REPORT = "ServiceDataReport";
    public static final String SESSION_REPORT = "SessionReport";
    public static final String SATISFACTION_REPORT = "SatisfactionReport";
    public static final String SATISFACTION_LABEL_REPORT = "SatisfactionLabelReport";
    public static final String SESSION_RECORD = "SessionRecord";
    public static final String CHAT_DETAIL = "ChatDetail";
    public static final String QUICK_REPLY = "QuickReply";
    public static final String BUSINESS_TYPE = "BusinessType";
    public static final String SERVICE_SUMMARY_REPORT = "ServiceSummaryReport";

    private Integer orgId;
    private String name;
    private Integer port;
    private String type;
    private Integer status = NOT_START_STATUS;
    private Integer progress;
    private String fileId;
    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONObject extraParams = new JSONObject();
    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONObject importInfo;
    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONArray errorLog;
    private String creator;
    private Date createTime;
    private Date updateTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public JSONObject getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(JSONObject extraParams) {
        this.extraParams = extraParams;
    }

    public JSONObject getImportInfo() {
        return importInfo;
    }

    public void setImportInfo(JSONObject importInfo) {
        this.importInfo = importInfo;
    }

    public JSONArray getErrorLog() {
        return errorLog;
    }

    public void setErrorLog(JSONArray errorLog) {
        this.errorLog = errorLog;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
