package com.wolaidai.webot.data.mysql.repo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.wolaidai.webot.data.mysql.entity.chat.UserStateHistoryEntity;
import com.wolaidai.webot.data.mysql.model.UserStateDuration;

public interface UserStateHistoryRepo extends BaseRepo<UserStateHistoryEntity, Integer> {

    @Query(value = "select A.org_id orgId,A.email,A.create_time createTime,B.create_time endTime,A.state,TIMESTAMPDIFF(SECOND,A.create_time,B.create_time) seconds from(select a.*,(@i \\:= @i + 1) as ord_num from user_state_history a,(select @i \\:= 1) d where a.user_type=1 and a.create_time>=?1 and a.create_time<?2 order by email, org_id, create_time, state) as A LEFT JOIN (select a.*,(@j \\:= @j + 1) as ord_num from user_state_history a,(select @j \\:= 0) c where a.user_type=1 and a.create_time>=?1 and a.create_time<?2 order by email, org_id, create_time, state) as B on A.ord_num=B.ord_num and A.org_id=B.org_id and A.email=B.email order by A.email,A.org_id,A.create_time,A.state", nativeQuery = true)
    List<UserStateDuration> find(Date startTime, Date endTime);

    @Query(value = "select A.org_id orgId,A.email,A.create_time createTime,B.create_time endTime,A.state,TIMESTAMPDIFF(SECOND,A.create_time,B.create_time) seconds from(select a.*,(@i \\:= @i + 1) as ord_num from user_state_history a,(select @i \\:= 1) d where a.user_type=1 and a.org_id=?1 and a.create_time>=?2 and a.create_time<?3 order by email, org_id, create_time, state) as A LEFT JOIN (select a.*,(@j \\:= @j + 1) as ord_num from user_state_history a,(select @j \\:= 0) c where a.user_type=1 and a.org_id=?1 and a.create_time>=?2 and a.create_time<?3 order by email, org_id, create_time, state) as B on A.ord_num=B.ord_num and A.org_id=B.org_id and A.email=B.email order by A.email,A.org_id,A.create_time,A.state", nativeQuery = true)
    List<UserStateDuration> find(Integer orgId, Date startTime, Date endTime);

    @Query(value = "select A.org_id orgId,A.email,A.create_time createTime,B.create_time endTime,A.state,TIMESTAMPDIFF(SECOND,A.create_time,B.create_time) seconds from(select a.*,(@i \\:= @i + 1) as ord_num from user_state_history a,(select @i \\:= 1) d where a.user_type=1 and a.org_id=?1 and a.email in ?2 and a.create_time>=?3 and a.create_time<?4 order by email, org_id, create_time, state) as A LEFT JOIN (select a.*,(@j \\:= @j + 1) as ord_num from user_state_history a,(select @j \\:= 0) c where a.user_type=1 and a.org_id=?1 and a.email in ?2 and a.create_time>=?3 and a.create_time<?4 order by email, org_id, create_time, state) as B on A.ord_num=B.ord_num and A.org_id=B.org_id and A.email=B.email order by A.email,A.org_id,A.create_time,A.state", nativeQuery = true)
    List<UserStateDuration> find(Integer orgId, Collection<String> emails, Date startTime, Date endTime);

}
