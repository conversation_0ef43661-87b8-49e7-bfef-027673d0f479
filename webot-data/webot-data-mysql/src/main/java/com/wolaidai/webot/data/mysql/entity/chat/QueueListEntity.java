package com.wolaidai.webot.data.mysql.entity.chat;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "queue_list")
//会话排队表
public class QueueListEntity extends BaseEntity {
    //排队中
    public final static Integer INQUEUE_STATUS = 0;
    //已接听
    public final static Integer QUEUED_STATUS = 1;
    //已放弃
    public final static Integer DEQUEUE_STATUS = 2;
    //超时
    public final static Integer QUEUE_TIMEOUT_STATUS = 3;
    private String clientId;
    private Integer clientTypeId;
    private String origin;
    private Integer botId;
    //全局会话ID
    private String gcid;
    //全局会话启动时间
    private Date gcTime;
    private Integer businessId;
    private String businessName;
    private String customerName;
    //客户类型，0:普通用户；1：VIP用户
    private Integer customerType;
    @Type( type = "json" )
    @Column( columnDefinition = "json" )
    private JSONObject customerDetail;
    private Integer orgId;
    //排队中/已接听/已放弃/超时
    private Integer status = INQUEUE_STATUS;
    //最后一条消息
    private String lastMsg;
    //最后消息时间
    private Date lastMsgTime;
    //等待时间
    private Long waitSecond;
    private Date createTime;
    private Date updateTime;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public Integer getBotId() {
        return botId;
    }

    public void setBotId(Integer botId) {
        this.botId = botId;
    }

    public String getGcid() {
        return gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public Date getGcTime() {
        return gcTime;
    }

    public void setGcTime(Date gcTime) {
        this.gcTime = gcTime;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public JSONObject getCustomerDetail() {
        return customerDetail;
    }

    public void setCustomerDetail(JSONObject customerDetail) {
        this.customerDetail = customerDetail;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getLastMsg() {
        return lastMsg;
    }

    public void setLastMsg(String lastMsg) {
        this.lastMsg = lastMsg;
    }

    public Date getLastMsgTime() {
        return lastMsgTime;
    }

    public void setLastMsgTime(Date lastMsgTime) {
        this.lastMsgTime = lastMsgTime;
    }

    public Long getWaitSecond() {
        return waitSecond;
    }

    public void setWaitSecond(Long waitSecond) {
        this.waitSecond = waitSecond;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
