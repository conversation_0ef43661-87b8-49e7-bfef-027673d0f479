package com.wolaidai.webot.data.mysql.entity.report;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

@Entity
@Table(name = "service_data_report")
//客服工作量统计数据表
public class ServiceDataReportEntity extends BaseEntity {

    private Integer orgId;
    private Date dataTime;
    private String email;
    private String workNumber;
    private String nickName;
    private Integer sessionCount = 0;
    private Long durationSecondsAvg = 0l;
    private Integer totalOnline = 0;
    private Integer totalBusy = 0;
    private Integer totalRest = 0;
    private Long firstResponseAvg = 0l;
    private Long responseAvg = 0l;
    private Integer starOne = 0;
    private Integer starTwo = 0;
    private Integer starThree = 0;
    private Integer starFour = 0;
    private Integer starFive = 0;
    private Float satisfactionPercent = 0f;
    private Date createTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public Long getDurationSecondsAvg() {
        return durationSecondsAvg;
    }

    public void setDurationSecondsAvg(Long durationSecondsAvg) {
        this.durationSecondsAvg = durationSecondsAvg;
    }

    public Integer getTotalOnline() {
        return totalOnline;
    }

    public void setTotalOnline(Integer totalOnline) {
        this.totalOnline = totalOnline;
    }

    public Integer getTotalBusy() {
        return totalBusy;
    }

    public void setTotalBusy(Integer totalBusy) {
        this.totalBusy = totalBusy;
    }

    public Integer getTotalRest() {
        return totalRest;
    }

    public void setTotalRest(Integer totalRest) {
        this.totalRest = totalRest;
    }

    public Long getFirstResponseAvg() {
        return firstResponseAvg;
    }

    public void setFirstResponseAvg(Long firstResponseAvg) {
        this.firstResponseAvg = firstResponseAvg;
    }

    public Long getResponseAvg() {
        return responseAvg;
    }

    public void setResponseAvg(Long responseAvg) {
        this.responseAvg = responseAvg;
    }

    public Integer getStarOne() {
        return starOne;
    }

    public void setStarOne(Integer starOne) {
        this.starOne = starOne;
    }

    public Integer getStarTwo() {
        return starTwo;
    }

    public void setStarTwo(Integer starTwo) {
        this.starTwo = starTwo;
    }

    public Integer getStarThree() {
        return starThree;
    }

    public void setStarThree(Integer starThree) {
        this.starThree = starThree;
    }

    public Integer getStarFour() {
        return starFour;
    }

    public void setStarFour(Integer starFour) {
        this.starFour = starFour;
    }

    public Integer getStarFive() {
        return starFive;
    }

    public void setStarFive(Integer starFive) {
        this.starFive = starFive;
    }

    public Float getSatisfactionPercent() {
        return satisfactionPercent;
    }

    public void setSatisfactionPercent(Float satisfactionPercent) {
        this.satisfactionPercent = satisfactionPercent;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
