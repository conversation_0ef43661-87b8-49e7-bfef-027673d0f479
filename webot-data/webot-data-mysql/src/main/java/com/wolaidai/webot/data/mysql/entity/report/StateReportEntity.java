package com.wolaidai.webot.data.mysql.entity.report;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.Type;

import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.data.mysql.entity.BaseEntity;

@Entity
@Table(name = "state_report")
//客服状态统计数据表
public class StateReportEntity extends BaseEntity {

    private String email;
    private Integer orgId;
    private Date dataTime;
    private String workNumber;
    private String nickName;
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JSONArray onlineData = new JSONArray();
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JSONArray busyData = new JSONArray();
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JSONArray restData = new JSONArray();
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JSONArray leaveData = new JSONArray();
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JSONArray eatData = new JSONArray();
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JSONArray studyData = new JSONArray();
    private Date createTime;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public JSONArray getOnlineData() {
        return onlineData;
    }

    public void setOnlineData(JSONArray onlineData) {
        this.onlineData = onlineData;
    }

    public JSONArray getBusyData() {
        return busyData;
    }

    public void setBusyData(JSONArray busyData) {
        this.busyData = busyData;
    }

    public JSONArray getRestData() {
        return restData;
    }

    public void setRestData(JSONArray restData) {
        this.restData = restData;
    }

    public JSONArray getLeaveData() {
        return leaveData;
    }

    public void setLeaveData(JSONArray leaveData) {
        this.leaveData = leaveData;
    }

    public JSONArray getEatData() {
        return eatData;
    }

    public void setEatData(JSONArray eatData) {
        this.eatData = eatData;
    }

    public JSONArray getStudyData() {
        return studyData;
    }

    public void setStudyData(JSONArray studyData) {
        this.studyData = studyData;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
