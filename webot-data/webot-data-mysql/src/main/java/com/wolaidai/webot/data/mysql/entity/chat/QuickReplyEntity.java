package com.wolaidai.webot.data.mysql.entity.chat;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "quick_reply")
//快捷回复表
public class QuickReplyEntity extends BaseEntity {

    public static final int GLOBAL_N = 0;
    public static final int GLOBAL_Y = 1;

    public static final int ANSWER_TYPE_TEXT = 0;
    public static final int ANSWER_TYPE_ATTACHMENT = 1;

    public static final String  FILE_TYPE_PICTURE = "PICTURE";
    public static final String  FILE_TYPE_VIDEO = "VIDEO";
    public static final String  FILE_TYPE_OTHER = "OTHER";

    private String content;
    private String fileName;
    private Long fileSize;
    private String fileType;
    private String fileUrl;
    private String remark;
    //文本/附件
    private Integer answerType;
    @ManyToOne(fetch = FetchType.LAZY)
    private ReplyCategoryEntity category;
    private Integer orgId;
    //0:非公共；1:公共
    private Integer global = 0;
    private Integer position = 0;
    private String creator;
    private Date createTime;
    private Date updateTime;


    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getAnswerType() {
        return answerType;
    }

    public void setAnswerType(Integer answerType) {
        this.answerType = answerType;
    }

    public ReplyCategoryEntity getCategory() {
        return category;
    }

    public void setCategory(ReplyCategoryEntity category) {
        this.category = category;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getGlobal() {
        return global;
    }

    public void setGlobal(Integer global) {
        this.global = global;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
