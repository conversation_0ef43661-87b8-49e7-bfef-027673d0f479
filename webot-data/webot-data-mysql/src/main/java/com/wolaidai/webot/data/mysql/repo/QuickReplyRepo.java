package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.QuickReplyEntity;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

public interface QuickReplyRepo extends CrudRepository<QuickReplyEntity,Integer>, JpaSpecificationExecutor<QuickReplyEntity> {

    @Modifying
    @Transactional
    @Query(value = "delete from quick_reply where org_id = ?1 and category_id = ?2 and creator = ?3 and global = ?4", nativeQuery = true)
    void deleteByOrgIdAndCategoryIdAndCreatorAndGlobal(Integer orgId, Integer categoryId, String creator, Integer global);

    @Modifying
    @Transactional
    @Query(value = "delete from quick_reply where org_id = ?1 and category_id in ?2 and global = ?3", nativeQuery = true)
    void deleteByOrgIdAndCategoryIdsAndGlobal(Integer orgId, Collection<Integer> categoryIds, Integer global);

    @Modifying
    @Transactional
    @Query(value = "update quick_reply set category_id = ?1, update_time = now() where id in ?2 and org_id = ?3 and global = ?4", nativeQuery = true)
    void updateCategoryByIdsAndOrgIdAndGlobal(Integer categoryId, Collection<Integer> ids, Integer orgId, Integer global);

    QuickReplyEntity findByOrgIdAndCategoryIdAndContentAndCreatorAndGlobal(Integer orgId, Integer categoryId, String content, String creator, Integer global);

    QuickReplyEntity findByOrgIdAndCategoryIdAndContentAndGlobal(Integer orgId, Integer categoryId, String content, Integer global);

}
