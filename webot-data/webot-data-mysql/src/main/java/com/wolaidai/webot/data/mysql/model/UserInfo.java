package com.wolaidai.webot.data.mysql.model;

public interface UserInfo {

    String getEmail();

    String getFullName();
    
    Integer getStatus();

    String getExtraFieldJson();

    default UserExtraField getUserExtraField() {
        String s = getExtraFieldJson();
        if (null != s && s.trim().length() > 0) {
            return new UserExtraField(s);
        }
        return null;
    }

}
