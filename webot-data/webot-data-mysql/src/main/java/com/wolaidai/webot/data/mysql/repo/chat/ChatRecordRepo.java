package com.wolaidai.webot.data.mysql.repo.chat;

import com.wolaidai.webot.data.mysql.entity.chat.ChatRecordEntity;
import com.wolaidai.webot.data.mysql.repo.BaseRepo;
import org.springframework.data.jpa.repository.Query;

public interface ChatRecordRepo extends BaseRepo<ChatRecordEntity, Integer> {

    @Query(value = "select cs.* from chat_record cs join chat_members_snapshot cms " +
            " where cs.snapshot_id = cms.id and cs.org_id = ?1 and cs.session_id = ?3 " +
            " and json_contains(json_extract(cms.user_id,'$'),json_array(?2)) " +
            " order by cs.create_time desc limit 1", nativeQuery = true)
    ChatRecordEntity findFirstBySessionIdAndEmail(Integer orgId, String email, Integer sessionId);
}