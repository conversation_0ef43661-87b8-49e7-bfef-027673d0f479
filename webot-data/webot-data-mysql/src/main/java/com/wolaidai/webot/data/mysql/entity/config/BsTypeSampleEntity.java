package com.wolaidai.webot.data.mysql.entity.config;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import java.util.Map;

public class BsTypeSampleEntity extends BaseEntity {

    private Integer parentId;
    private String title;

    public BsTypeSampleEntity() {
    }

    public BsTypeSampleEntity(Integer id, Integer parentId, String title) {
        this.setId(id);
        this.parentId = parentId;
        this.title = title;
    }

    public String fullPathTitle(Map<Integer, BsTypeSampleEntity> map) {
        if(parentId != null) {
            BsTypeSampleEntity bsTypeSampleEntity = map.get(parentId);
            if (bsTypeSampleEntity != null) {
                return bsTypeSampleEntity.title + "," + title;
            }
        }
        return title;
    }
    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
