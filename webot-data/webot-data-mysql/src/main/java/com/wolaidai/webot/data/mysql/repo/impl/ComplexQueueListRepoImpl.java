package com.wolaidai.webot.data.mysql.repo.impl;

import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.repo.ComplexQueueListRepo;
import com.wolaidai.webot.data.mysql.repo.QueueListRepo;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class ComplexQueueListRepoImpl implements ComplexQueueListRepo {

    @Autowired
    private QueueListRepo queueListRepo;

    @Override
    public Page<QueueListEntity> findByOrgIdAndStatusAndTime(Integer orgId, Integer status, Date startTime, Date endTime, String uuid, Integer userId, Pageable pageable) {
        return queueListRepo.findAll((Root<QueueListEntity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder)->{
            List<Predicate> andPredicates = new ArrayList<>();
            andPredicates.add(criteriaBuilder.equal(root.get("orgId"), orgId));
            andPredicates.add(criteriaBuilder.equal(root.get("status"), status));
            andPredicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), startTime));
            andPredicates.add(criteriaBuilder.lessThan(root.get("createTime"), endTime));
            if(StringUtils.hasText(uuid)) {
                Expression<String> jsonPath = criteriaBuilder.literal("$.customers[0].uuid");
                Expression<String> jsonExtractFunction = criteriaBuilder.function(
                        "json_extract",
                        String.class,
                        root.get("customerDetail"),
                        jsonPath
                );
                Expression<String> uuidValue = criteriaBuilder.literal(uuid);
                andPredicates.add(criteriaBuilder.equal(jsonExtractFunction, uuidValue));
            }
            if(userId != null) {
                Expression<String> jsonPath = criteriaBuilder.literal("$.customers[0].userId");
                Expression<Integer> jsonExtractFunction = criteriaBuilder.function(
                        "json_extract",
                        Integer.class,
                        root.get("customerDetail"),
                        jsonPath
                );
                Expression<Integer> userIdValue = criteriaBuilder.literal(userId);
                andPredicates.add(criteriaBuilder.equal(jsonExtractFunction, userIdValue));
            }

            criteriaQuery.orderBy(criteriaBuilder.desc(root.get("createTime")));
            return criteriaBuilder.and(andPredicates.toArray(new Predicate[]{}));
        }, pageable);
    }

}
