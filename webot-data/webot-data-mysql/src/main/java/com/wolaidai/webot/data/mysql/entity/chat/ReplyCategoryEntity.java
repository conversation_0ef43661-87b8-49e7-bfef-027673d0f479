package com.wolaidai.webot.data.mysql.entity.chat;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.*;
import java.util.Date;
import java.util.Set;

@Entity
@Table(name = "reply_category")
//快捷回复分类表
public class ReplyCategoryEntity extends BaseEntity {

    public static final int GLOBAL_N = 0;
    public static final int GLOBAL_Y = 1;

    public static final int TYPE_TEXT = 0;
    public static final int TYPE_ATTACHMENT = 1;

    private String name;
    private Integer orgId;
    //0:非公共；1:公共
    private Integer global = 0;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="category_id")
    private ReplyCategoryEntity parentCategory;
    @OneToMany(mappedBy = "parentCategory")
    @OrderBy("position asc,id asc")
    private Set<ReplyCategoryEntity> childCategories;
    private Integer level;
    private Integer position = 0;
    private Integer type;
    private String creator;
    private Date createTime;
    private Date updateTime;

    public String fullPath() {
        ReplyCategoryEntity parentCategory = getParentCategory();
        if(parentCategory != null){
            return parentCategory.fullPath() + "/" + name;
        }
        return name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getGlobal() {
        return global;
    }

    public void setGlobal(Integer global) {
        this.global = global;
    }

    public ReplyCategoryEntity getParentCategory() {
        return parentCategory;
    }

    public void setParentCategory(ReplyCategoryEntity parentCategory) {
        this.parentCategory = parentCategory;
    }

    public Set<ReplyCategoryEntity> getChildCategories() {
        return childCategories;
    }

    public void setChildCategories(Set<ReplyCategoryEntity> childCategories) {
        this.childCategories = childCategories;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
