package com.wolaidai.webot.data.mysql.resultEntity;

import java.util.Date;

public class StatisticsTimeAnalyzeEntity {
    private Date time;
    private Number total;
    private Number analyze;

    public StatisticsTimeAnalyzeEntity(Date time, Number total, Number analyze) {
        this.time = time;
        this.total = total;
        this.analyze = analyze;
    }


    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Number getTotal() {
        return total;
    }

    public void setTotal(Number total) {
        this.total = total;
    }

    public Number getAnalyze() {
        return analyze;
    }

    public void setAnalyze(Number analyze) {
        this.analyze = analyze;
    }
}
