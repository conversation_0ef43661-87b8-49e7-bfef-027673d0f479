package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsAnalyzeEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsCountEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeAnalyzeEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface QueueListRepo extends BaseRepo<QueueListEntity, Integer> {

    @Modifying
    @Transactional
    @Query("update QueueListEntity set status=?2,updateTime=?3,wait_second=TIMESTAMPDIFF(SECOND,create_time,update_time) where id=?1 and status<>1")
    void updateStatusAndWaitSecondById(Integer id,Integer status,Date updateTime);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsCountEntity(t.businessId, count(t)) "
           + "from QueueListEntity t where t.orgId = :orgId and t.createTime >= :startTime and t.createTime <= :endTime group by t.businessId")
    List<StatisticsCountEntity> getBusinessCountByTime(@Param("orgId") Integer orgId,
                                                       @Param("startTime") Date startTime,
                                                       @Param("endTime") Date endTime);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsCountEntity(t.businessId, count(t)) "
            + "from QueueListEntity t where t.orgId = :orgId and t.createTime >= :startTime and t.createTime <= :endTime and t.status = :status group by t.businessId")
    List<StatisticsCountEntity> getBusinessCountByTimeAndStatus(@Param("orgId") Integer orgId,
                                                                @Param("startTime") Date startTime,
                                                                @Param("endTime") Date endTime,
                                                                @Param("status") Integer status);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsAnalyzeEntity(t.businessId, count(t), sum(t.waitSecond)) "
            + "from QueueListEntity t where t.orgId = :orgId and t.createTime >= :startTime and t.createTime <= :endTime and t.waitSecond > :waitSecond group by t.businessId")
    List<StatisticsAnalyzeEntity> getBusinessCountByTimeAndWaitSecond(@Param("orgId") Integer orgId,
                                                                      @Param("startTime") Date startTime,
                                                                      @Param("endTime") Date endTime,
                                                                      @Param("waitSecond") Long waitSecond);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(t.createTime) / 1800) * 1800) AS timeslice, count(t)) "
            + "from QueueListEntity t where t.orgId = :orgId and t.createTime >= :startTime and t.createTime <= :endTime group by timeslice")
    List<StatisticsTimeCountEntity> getTimeCountByTime(@Param("orgId") Integer orgId,
                                                                @Param("startTime") Date startTime,
                                                                @Param("endTime") Date endTime);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(t.createTime) / 1800) * 1800) AS timeslice, count(t)) "
            + "from QueueListEntity t where t.orgId = :orgId and t.createTime >= :startTime and t.createTime <= :endTime and t.status = :status group by timeslice")
    List<StatisticsTimeCountEntity> getTimeCountByTimeAndStatus(@Param("orgId") Integer orgId,
                                                                @Param("startTime") Date startTime,
                                                                @Param("endTime") Date endTime,
                                                                         @Param("status") Integer status);


    @Query("select new com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeAnalyzeEntity(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(t.createTime) / 1800) * 1800) AS timeslice, count(t), sum(t.waitSecond)) "
            + "from QueueListEntity t where t.orgId = :orgId and t.createTime >= :startTime and t.createTime <= :endTime and t.waitSecond > :waitSecond group by timeslice")
    List<StatisticsTimeAnalyzeEntity> getTimeCountByTimeAndWaitSecond(@Param("orgId") Integer orgId,
                                                                      @Param("startTime") Date startTime,
                                                                      @Param("endTime") Date endTime,
                                                                      @Param("waitSecond") Long waitSecond);


    List<QueueListEntity> findByStatusAndCreateTimeGreaterThanEqualAndCreateTimeLessThan(Integer status, Date beginTime, Date endTime);

    List<QueueListEntity> findByOrgIdAndStatusAndCreateTimeGreaterThanEqualAndCreateTimeLessThan(Integer orgId, Integer status, Date beginTime, Date endTime);

    Integer countByOrgIdAndCreateTimeGreaterThanEqual(Integer orgId, Date beginTime);

    Integer countByOrgIdAndStatusAndCreateTimeGreaterThanEqual(Integer orgId, Integer status, Date beginTime);

}
