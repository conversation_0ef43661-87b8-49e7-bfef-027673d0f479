package com.wolaidai.webot.data.mysql.entity.report;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "satisfaction_report")
public class SatisfactionReportEntity extends BaseEntity {
    //日期
    @Column(unique = true)
    private Date date;
    //会话数
    private Long sessionCount;
    //评价数
    private Long evaluatedCount;
    //评价率
    @Column(precision = 5, scale = 2)
    private BigDecimal evaluatedRate;
    //未评数
    private Long unEvaluatedCount;
    //未评率
    @Column(precision = 5, scale = 2)
    private BigDecimal unEvaluatedRate;
    //坐席邀请数
    private Long inviteCount;
    //坐席邀请率
    @Column(precision = 5, scale = 2)
    private BigDecimal inviteRate;
    //1星数
    private Long level1;
    //2星数
    private Long level2;
    //3星数
    private Long level3;
    //4星数
    private Long level4;
    //5星数
    private Long level5;
    //满意度
    @Column(precision = 5, scale = 2)
    private BigDecimal satisfactionRate;
    //组织ID
    private Integer orgId;
    //记录生成时间
    private Date creatTime;


    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Long getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Long sessionCount) {
        this.sessionCount = sessionCount;
    }

    public Long getEvaluatedCount() {
        return evaluatedCount;
    }

    public void setEvaluatedCount(Long evaluatedCount) {
        this.evaluatedCount = evaluatedCount;
    }

    public BigDecimal getEvaluatedRate() {
        return evaluatedRate;
    }

    public void setEvaluatedRate(BigDecimal evaluatedRate) {
        this.evaluatedRate = evaluatedRate;
    }

    public Long getUnEvaluatedCount() {
        return unEvaluatedCount;
    }

    public void setUnEvaluatedCount(Long unEvaluatedCount) {
        this.unEvaluatedCount = unEvaluatedCount;
    }

    public BigDecimal getUnEvaluatedRate() {
        return unEvaluatedRate;
    }

    public void setUnEvaluatedRate(BigDecimal unEvaluatedRate) {
        this.unEvaluatedRate = unEvaluatedRate;
    }

    public Long getInviteCount() {
        return inviteCount;
    }

    public void setInviteCount(Long inviteCount) {
        this.inviteCount = inviteCount;
    }

    public BigDecimal getInviteRate() {
        return inviteRate;
    }

    public void setInviteRate(BigDecimal inviteRate) {
        this.inviteRate = inviteRate;
    }

    public Long getLevel1() {
        return level1;
    }

    public void setLevel1(Long level1) {
        this.level1 = level1;
    }

    public Long getLevel2() {
        return level2;
    }

    public void setLevel2(Long level2) {
        this.level2 = level2;
    }

    public Long getLevel3() {
        return level3;
    }

    public void setLevel3(Long level3) {
        this.level3 = level3;
    }

    public Long getLevel4() {
        return level4;
    }

    public void setLevel4(Long level4) {
        this.level4 = level4;
    }

    public Long getLevel5() {
        return level5;
    }

    public void setLevel5(Long level5) {
        this.level5 = level5;
    }

    public BigDecimal getSatisfactionRate() {
        return satisfactionRate;
    }

    public void setSatisfactionRate(BigDecimal satisfactionRate) {
        this.satisfactionRate = satisfactionRate;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Date getCreatTime() {
        return creatTime;
    }

    public void setCreatTime(Date creatTime) {
        this.creatTime = creatTime;
    }
}
