package com.wolaidai.webot.data.mysql.entity.chat;

import com.wolaidai.webot.data.mysql.entity.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "session_transfer_list")
//会话转接历史表
public class SessionTransferListEntity extends BaseEntity {
    
    public final static Integer STATUS_START = 0;
    public final static Integer STATUS_OK = 1;
    public final static Integer STATUS_FAIL = 2;
    public final static Integer STATUS_TIMEOUT = 3;
    
    //来源客服email
    private String fromServiceUser;
    //转发客服email
    private String toServiceUser;
    private Integer orgId;
    @ManyToOne(fetch = FetchType.LAZY)
    private SessionListEntity session;
    private Integer status = STATUS_START;
    private String remark;
    private Date createTime;
    private Date updateTime;

    public String getFromServiceUser() {
        return fromServiceUser;
    }

    public void setFromServiceUser(String fromServiceUser) {
        this.fromServiceUser = fromServiceUser;
    }

    public String getToServiceUser() {
        return toServiceUser;
    }

    public void setToServiceUser(String toServiceUser) {
        this.toServiceUser = toServiceUser;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public SessionListEntity getSession() {
        return session;
    }

    public void setSession(SessionListEntity session) {
        this.session = session;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
