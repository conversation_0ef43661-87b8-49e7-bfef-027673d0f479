package com.wolaidai.webot.data.mysql.repo;

import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface UserStateRepo extends BaseRepo<UserStateEntity, Integer> {
    
    UserStateEntity findByOrgIdAndEmailAndCreateTimeGreaterThanEqual(Integer orgId, String email, Date time);
    UserStateEntity findByOrgIdAndEmail(Integer orgId, String email);
    List<UserStateEntity> findByOrgIdAndUserTypeAndCreateTimeGreaterThanEqual(Integer orgId, Integer userType, Date time);
    List<UserStateEntity> findByOrgIdAndUserTypeAndCreateTimeGreaterThanEqualOrderByUpdateTimeDesc(Integer orgId, Integer userType, Date time);
    Integer countByOrgIdAndUserTypeAndStateAndCreateTimeGreaterThanEqual(Integer orgId, Integer userType, Integer state, Date time);
    List<UserStateEntity> findByUserTypeAndCreateTimeGreaterThanEqual(Integer userType, Date time);
    List<UserStateEntity> findByOrgIdAndEmailInAndCreateTimeGreaterThanEqual(Integer orgId, Collection<String> emails, Date time);
    @Query(value = "select * from user_state where org_id = ?1 and user_type = ?2 order by CONVERT(nick_name USING GBK)", nativeQuery = true)
    List<UserStateEntity> findByOrgIdAndUserTypeOrderByNickName(Integer orgId, Integer userType);

    @Query(value = "select nick_name from user_state where org_id = ?1 and email in ?2", nativeQuery = true)
    List<String> findByOrgIdAndEmails(Integer orgId, Collection<String> emails);
}
