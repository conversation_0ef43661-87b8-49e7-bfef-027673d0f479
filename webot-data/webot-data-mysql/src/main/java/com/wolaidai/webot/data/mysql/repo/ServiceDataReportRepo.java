package com.wolaidai.webot.data.mysql.repo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.wolaidai.webot.data.mysql.entity.report.ServiceDataReportEntity;

public interface ServiceDataReportRepo extends BaseRepo<ServiceDataReportEntity, Integer> {

    List<ServiceDataReportEntity> findByOrgIdAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAsc(Integer orgId, Date beginTime, Date endTime);

    List<ServiceDataReportEntity> findByOrgIdAndEmailInAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAsc(Integer orgId, Collection<String> emails, Date beginTime, Date endTime);

    @Modifying
    @Transactional
    @Query(value = "delete from service_data_report where data_time>=?1 and data_time<?2", nativeQuery = true)
    Integer deleteByDataTime(Date begin, Date end);

    @Modifying
    @Transactional
    @Query(value = "delete from service_data_report where org_id =?1 and data_time>=?2 and data_time<?3", nativeQuery = true)
    Integer deleteByOrgIdAndDataTime(Integer orgId, Date begin, Date end);

    @Modifying
    @Transactional
    @Query(value = "delete from service_data_report where org_id=?1 and email in ?2 and data_time>=?3 and data_time<?4", nativeQuery = true)
    Integer deleteByOrgIdAndEmailsAndDataTime(Integer orgId, Collection<String> emails, Date begin, Date end);

}
