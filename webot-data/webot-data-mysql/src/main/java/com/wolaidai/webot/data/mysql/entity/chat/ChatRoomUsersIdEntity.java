package com.wolaidai.webot.data.mysql.entity.chat;

import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class ChatRoomUsersIdEntity implements Serializable {

    private Integer roomId;
    private Integer userId;

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public ChatRoomUsersIdEntity() {
    }

    public ChatRoomUsersIdEntity(Integer roomId, Integer userId) {
        this.roomId = roomId;
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChatRoomUsersIdEntity that = (ChatRoomUsersIdEntity) o;
        return roomId.equals(that.roomId) &&
                userId.equals(that.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(roomId, userId);
    }
}
