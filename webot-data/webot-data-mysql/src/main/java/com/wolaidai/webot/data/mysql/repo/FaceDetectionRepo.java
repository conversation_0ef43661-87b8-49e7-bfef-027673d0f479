package com.wolaidai.webot.data.mysql.repo;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.Query;

import com.wolaidai.webot.data.mysql.entity.face.FaceDetectionEntity;

public interface FaceDetectionRepo extends BaseRepo<FaceDetectionEntity, Integer> {

    @Query(value = "select * from face_detection s where s.session_id=?1 order by id desc limit 1", nativeQuery = true)
    FaceDetectionEntity findLastBySessionId(Integer sessionId); 

    @Query(value = "select sum(s.failure_times) from face_detection s where s.session_id=?1", nativeQuery = true)
    Integer getFailTimesBySessionId(Integer sessionId);

    @Query(value = "select distinct session_id from face_detection where session_id is not null and create_time>=?1 and create_time<=?2", nativeQuery = true)
    List<Integer> getSessionIdsByTime(Date startTime, Date endTime);

    @Query(value = "select distinct session_id from face_detection where session_id is not null and code is null and session_id not in (select session_id from face_detection where session_id is not null and code is not null) and create_time>=?1 and create_time<=?2", nativeQuery = true)
    List<Integer> getUndoneSessionIdsByTime(Date startTime, Date endTime);

    @Query(value = "select distinct session_id from face_detection where session_id is not null and code=0 and create_time>=?1 and create_time<=?2", nativeQuery = true)
    List<Integer> getSuccessSessionIdsByTime(Date startTime, Date endTime);

    @Query(value = "select distinct session_id from face_detection where session_id is not null and code!=0 and session_id not in (select session_id from face_detection where session_id is not null and code=0) and create_time>=?1 and create_time<=?2", nativeQuery = true)
    List<Integer> getFailSessionIdsByTime(Date startTime, Date endTime);

    List<FaceDetectionEntity> findAllBySessionIdIn(Set<Integer> sessionIds);

    List<FaceDetectionEntity> findBySessionId(Integer sessionId);

    Optional<FaceDetectionEntity> findFirstBySourceAndMobileOrderByCreateTimeDesc(String source, String mobile);

    List<FaceDetectionEntity> findByMobileOrderByCreateTimeDesc(String mobile);



}
