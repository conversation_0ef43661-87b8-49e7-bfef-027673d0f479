package com.wolaidai.webot.data.mysql.repo.account;

import com.wolaidai.webot.data.mysql.entity.account.UserEntity;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface UserRepo extends CrudRepository<UserEntity, Integer>, JpaSpecificationExecutor<UserEntity> {

    @Query(value = "select distinct u.email email,u.full_name fullName,u.status status,ud.extra_field_json extraFieldJson from auth.user u left join auth.user_detail ud on u.id=ud.user_id where ud.org_id=?1 and ud.product_id=?2 and u.email in ?3", nativeQuery = true)
    List<UserInfo> find(Integer orgId, Integer prodId, Collection<String> emails);
    
    @Query(value = "select distinct u.email email,u.full_name fullName,u.status status,ud.extra_field_json extraFieldJson FROM auth.user u JOIN auth.user_role ur ON u.id = ur.user_id JOIN auth.role r ON ur.role_id = r.id AND r.type = 1 JOIN auth.product p ON r.product_id = p.id JOIN auth.user_detail ud ON u.id = ud.user_id AND p.id = ud.product_id AND ud.org_id=?1 WHERE r.org_id = ?1 AND p.id = ?2 AND u.id IN (select uo.user_id FROM auth.user_org uo WHERE uo.org_id = ?1)", nativeQuery = true)
    List<UserInfo> find(Integer orgId, Integer prodId);
    
    @Query(value = "select distinct u.email email,u.full_name fullName,u.status status,ud.extra_field_json extraFieldJson FROM auth.user u JOIN auth.user_role ur ON u.id = ur.user_id AND ur.status = ?3 JOIN auth.role r ON ur.role_id = r.id AND r.status = ?3 AND r.type = 1 JOIN auth.product p ON r.product_id = p.id AND p.status = ?3 JOIN auth.user_detail ud ON u.id = ud.user_id AND p.id = ud.product_id AND ud.org_id=?1 WHERE u.status = ?3 AND r.org_id = ?1 AND p.id = ?2 AND u.id IN (select uo.user_id FROM auth.user_org uo WHERE uo.org_id = ?1)", nativeQuery = true)
    List<UserInfo> findByStatus(Integer orgId, Integer prodId, Integer status);

    @Query(value = "select ud.extra_field_json from auth.user u left join auth.user_detail ud on u.id=ud.user_id where ud.org_id=?1 and ud.product_id=?2 and u.email = ?3", nativeQuery = true)
    String findExtraField(Integer orgId, Integer prodId, String email);

    @Query(value = "select distinct o.org_id from auth.org_product o where o.status=1 and (o.valid_time is null or o.valid_time>?1)", nativeQuery = true)
    List<Integer> findValidOrgIds(Integer productId, Date now);

    @Query(value = "SELECT r.name FROM auth.user u JOIN auth.user_role ur ON u.id = ur.user_id JOIN auth.role r ON ur.role_id = r.id WHERE u.email = ?1 AND ur.status = 1 AND r.status = 1", nativeQuery = true)
    List<String> findUserRoleNames(String email);

}
