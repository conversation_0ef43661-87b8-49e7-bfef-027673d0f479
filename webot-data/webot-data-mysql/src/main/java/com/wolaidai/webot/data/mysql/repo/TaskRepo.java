package com.wolaidai.webot.data.mysql.repo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;

public interface TaskRepo extends BaseRepo<TaskEntity, Integer> {
    TaskEntity findByOrgIdAndId(Integer orgId, Integer id);

    TaskEntity findByOrgIdAndStatusAndPortAndTypeAndCreator(Integer orgId, Integer status, Integer port, String type, String creator);

    Page<TaskEntity> findByOrgIdAndCreatorAndCreateTimeGreaterThanEqualOrderByIdDesc(Integer orgId, String creator, Date date, Pageable pageable);

    List<TaskEntity> findByOrgIdAndIdInAndCreator(Integer orgId, Collection<Integer> ids, String creator);

    List<TaskEntity> findByStatusAndCreateTimeGreaterThanEqual(Integer status, Date date);
}
