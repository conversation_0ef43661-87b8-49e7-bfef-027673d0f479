package com.wolaidai.webot.data.redis.constant;

public interface RedisKey {
    String CS_REQUEST_ID = "cs:requestId:";
    String CS_GUEST_ID = "cs:guestId:orgId:%s";
    String EVENT_CLIENT_KEY = "connector:event:client";
    String EVENT_SERVER_KEY = "connector:event:server";
    String QUEUE_NORMAL = "queue:orgId:%s:normal";
    String QUEUE_VIP = "queue:orgId:%s:vip";
    String LOCK_KEY = "lock:";

    String CS_WORKTIME_CONFIG = "cs:config:worktime:orgId:%s";
    String WORKBENCH_CONFIG_STATUS = "cs:config:workbench:orgId:%s:status";
    String WORKBENCH_CONFIG = "cs:config:workbench:orgId:%s";
    String SERVICEUSER_CONFIG = "cs:config:serviceuser:orgId:%s";
    String AUTOREPLY_CONFIG = "cs:config:autoreply:orgId:%s:clientType:%s";
    String SATISFACTION_CONFIG = "cs:config:satisfaction:orgId:%s";
    String RESP_TIMEOUT_CONFIG = "cs:config:respTimeout:orgId:%s";
    
    String USER_ONLINE_LIST = "cs:user:online:orgId:%s";
    String USER_DEFAULT_RECEPTION = "cs:user:reception:default:orgId:%s";
    String USER_MAX_RECEPTION = "cs:user:reception:max:orgId:%s";
    String USER_CURRENT_RECEPTION = "cs:user:reception:current:orgId:%s";

    String IMPORT_QUICK_REPLY_JOB = "cs:import:quickReply:orgId:%s";
    String IMPORT_QUICK_REPLY_PERSONAL_JOB = "cs:import:quickReply:orgId:%s:creator:%s";
    String IMPORT_QUICK_REPLY_PROGRESS = "cs:import:quickReply:orgId:%s:uploadId:%s";
    String IMPORT_QUICK_REPLY_ERROR = "cs:import:quickReply:orgId:%s:uploadId:%s:error:";

    String IMPORT_BUSINESS_TYPE_JOB = "cs:import:businessType:orgId:%s";
    String IMPORT_BUSINESS_TYPE_PROGRESS = "cs:import:businessType:orgId:%s:uploadId:%s";
    String IMPORT_BUSINESS_TYPE_ERROR = "cs:import:businessType:orgId:%s:uploadId:%s:error:";

    String CS_SESSION_TRANS_KEY = "connector:cs:session:trans:%s";

    String GLOBAL_CONVERSATION_KEY = "connector:conversation:clientId:%s:botId:%s";

    String CS_RESP_DETAIL = "connector:cs:respDetail:sessionkey:%s";

    String EXPIRE_KEY_ZSET = "connector:expire:key:zset";

    String CS_FACE_DETECTION = "cs:faceDetection:token:%s";

}
