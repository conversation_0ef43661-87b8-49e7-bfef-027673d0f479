#spring.jpa.hibernate.ddl-auto=create
server.port=9393
server.servlet.context-path=/api
spring.mvc.date-format=yyyyMMddHHmmss
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.main.allow-circular-references=true
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-date-keys-as-timestamps=false
log.path=./logs
log.file=bot-server-online-cs.log
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.datasource.url=jdbc:mysql://**********:3306/cs_bot?useUnicode=true&characterEncoding=UTF-8
spring.datasource.username=root
spring.datasource.password=WvQQpZCEbRChwZ2BeUisoMRt
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

spring.jpa.properties.hibernate.generate_statistics=false
spring.jpa.properties.hibernate.jdbc.batch_size=1000
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates =true

spring.redis.database=1
spring.redis.url=redis://:Welabx20170808X@**********:6379
elastic.esClusterName=elasticsearch
elastic.esClusterNodes=**********:9301
elastic.esSecurityUser=
elastic.esXpackKeyPath=
elastic.esXpackKeyPassword=
spring.servlet.multipart.enabled=false
server.servlet.session.timeout=1d
spring.session.store-type=redis
app.envTest=true
app.productId=3
app.permissionUrl=https://xbot-dev.wld.net/oauth/permissions
app.apiAuthUrl=https://xbot-dev.wld.net/oauth/apiUserInfo
app.fileServerUrl=https://webot-dev.wld.net/file
app.customerInfoUrl=https://crm-fat.welab-inc.com/welab-crm-interview/v1/user/queryByMobile
app.ipInfoUrl=https://xexdata-fat.wolaidai.com/tools/getIpInfo
app.faceDocumentsUrl=https://japi-fat.wld.net/application/api/v1/internal/face_documents
app.faceLinkIntervalMinutes=3
app.maxFaceDetectFailureTimes=5
app.faceTokenTimeoutMinutes=10
app.faceDetectionUrl=https://webot-dev.wld.net/faceid
app.summaryWebhookUrl=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4c65ee4f-916d-49dc-9e17-f35c11d6bf2c


security.oauth2.client.resource-ids=webot
security.oauth2.client.client-id=9f6f71af2117d910
security.oauth2.client.client-secret=33f68d7fff
security.oauth2.client.scope=user_info,auto_login
security.oauth2.client.access-token-uri=https://xbot-dev.wld.net/oauth/token
security.oauth2.client.user-authorization-uri=https://xbot-dev.wld.net/oauth/authorize
security.oauth2.resource.token-info-uri=https://xbot-dev.wld.net/oauth/check_token


welab.privacy.root.url=https://japi-fat.wolaidai.com/privacy/api/v2/config-info
welab.privacy.secret.key=N2qevU18ctVFJjJV
appId=bot-server-online-cs