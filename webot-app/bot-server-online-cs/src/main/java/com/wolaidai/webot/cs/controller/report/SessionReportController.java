package com.wolaidai.webot.cs.controller.report;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.report.SessionReportReqModel;
import com.wolaidai.webot.cs.service.SessionReportService;
import com.wolaidai.webot.cs.service.TaskService;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.security.domain.UserDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(tags = "会话统计报表接口")
@RestController
@RequestMapping("/report")
public class SessionReportController extends BaseController {


    @Autowired
    private SessionReportService sessionReportService;
    @Autowired
    private TaskService taskService;

    @GetMapping("/session")
    @ApiOperation(value = "会话统计")
    public ResponseModel sessionReport(SessionReportReqModel sessionReportReqModel) throws Exception {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        Date startTime = sessionReportReqModel.getStartTime();
        Date endTime = sessionReportReqModel.getEndTime();
        if (startTime == null || endTime == null || startTime.after(endTime)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "请提供正确的时间段");
        }

        Integer type = sessionReportReqModel.getType();
        List<Map<String, Object>> result = sessionReportService.getSessionReport(orgId, type, startTime, endTime);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "success", result);
    }


    @GetMapping("/session/export")
    @ApiOperation(value = "会话统计导出")
    public ResponseModel sessionReportExport(SessionReportReqModel sessionReportReqModel) {
        UserDomain user = getUser();
        Date startTime = sessionReportReqModel.getStartTime();
        Date endTime = sessionReportReqModel.getEndTime();
        if (startTime == null || endTime == null || startTime.after(endTime)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "请提供正确的时间段");
        }
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.SESSION_REPORT, TaskEntity.PORT_EXPORT, sessionReportReqModel, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }

}
