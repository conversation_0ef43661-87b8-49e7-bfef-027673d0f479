package com.wolaidai.webot.cs.service;

import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.repo.TaskRepo;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class CheckTaskService{
    final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private TaskRepo taskRepo;
    @Autowired
    private TaskService taskService;

    public void checkTask() {
        LOGGER.info("start checkTask...");
        long start = System.currentTimeMillis();
        List<TaskEntity> tasks = taskRepo.findByStatusAndCreateTimeGreaterThanEqual(TaskEntity.EXECUTING_STATUS, DateUtils.addDays(new Date(), -10));
        for (TaskEntity task : tasks) {
            taskService.executeTask(task);
        }
        LOGGER.info("end checkTask: {} ms", System.currentTimeMillis() - start);
    }
}
