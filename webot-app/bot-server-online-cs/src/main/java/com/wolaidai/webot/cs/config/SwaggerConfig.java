package com.wolaidai.webot.cs.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.paths.DefaultPathProvider;
import springfox.documentation.spring.web.plugins.Docket;


@Configuration
@EnableOpenApi
public class SwaggerConfig {

    @Autowired
    private AppPropertyConfig appPropertyConfig;

    @Autowired
    private ServerProperties serverProperties;

    @Bean
    public Docket createRestApi() {
        Docket docket = new Docket(DocumentationType.OAS_30)
                .apiInfo(apiInfo())
                .pathProvider(new DefaultPathProvider(){
                    @Override
                    public String getOperationPath(String operationPath) {
                        if(serverProperties.getServlet().getContextPath()!=null){
                            operationPath = operationPath.replaceFirst(serverProperties.getServlet().getContextPath(),"/");
                        }
                        return super.getOperationPath(operationPath);
                    }
                })
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.wolaidai.webot.cs.controller"))
                .paths(PathSelectors.any())
                .build()
                .useDefaultResponseMessages(false);
        if (!appPropertyConfig.isEnvTest()) {
            docket.enable(false);
        }
        return docket;
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("webot management server")
                .description("来啊~ 快活啊~")
                .termsOfServiceUrl("/")
                .contact(new Contact("Iverson", "", ""))
                .version("0.1")
                .build();
    }

}
