package com.wolaidai.webot.cs.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.sessionlist.*;
import com.wolaidai.webot.cs.model.sessionlist.personal.PersonalChatHistoryRowModel;
import com.wolaidai.webot.cs.model.sessionlist.personal.PersonalChatHistorySearchModel;
import com.wolaidai.webot.cs.model.sessionlist.personal.PersonalSessionSearchModel;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import com.wolaidai.webot.cs.service.SessionListService;
import com.wolaidai.webot.cs.service.TaskService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.elastic.entity.ChatHistoryElasticEntity;
import com.wolaidai.webot.data.elastic.entity.SessionListElasticEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexChatHistoryElasticRepo;
import com.wolaidai.webot.data.elastic.repo.ComplexSessionListElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.face.FaceDetectionEntity;
import com.wolaidai.webot.data.mysql.entity.report.SessionStatisticsEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.repo.FaceDetectionRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionStatisticsRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;
import com.wolaidai.webot.security.domain.UserDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(tags = "聊天历史记录接口")
@RestController
@RequestMapping("/sessionList")
public class SessionListController extends BaseController {

    @Autowired
    private SessionListRepo sessionListRepo;
    @Autowired
    private ComplexChatHistoryElasticRepo complexChatHistoryElasticRepo;
    @Autowired
    private ComplexSessionListElasticRepo complexSessionListElasticRepo;
    @Autowired
    private UserRepo userRepo;
    @Autowired
    private SessionStatisticsRepo sessionStatisticsRepo;
    @Autowired
    private SessionListService sessionListService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    private FaceDetectionRepo faceDetectionRepo;

    @Autowired
    private CustomerInfoEncService customerInfoEncService;

    @GetMapping("/{id}/personal")
    @ApiOperation(value = "交互记录-会话列表", response = PersonalSessionSearchModel.class)
    public ResponseModel searchPersonalSession(@PathVariable Integer id, PersonalSessionSearchModel model) {
        //type=1 客服端请求，仅允许查看7天内的历史记录
        Date startTime = model.getStartTime();
        Date endTime = model.getEndTime();
        if(Objects.equals(model.getType(), 1)) {
            Date todayStart = DateUtils.truncate(new Date(), Calendar.DATE);
            Date sixDaysAgo = DateUtils.addDays(todayStart, -6);
            if((model.getStartTime() != null && model.getStartTime().before(sixDaysAgo))) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "只能查看7天内的会话记录");
            }
            if(startTime == null && endTime == null) {
                startTime = sixDaysAgo;
                endTime = new Date();
            }
        }
        UserDomain user = getUser();
        String mobile = null;
        Integer orgId = user.getOrganizationId();
        SessionListEntity entity = sessionListRepo.findByOrgIdAndId(orgId, id);
        if (entity == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "当前会话不存在");
        }
        if (entity.getCustomerDetail() != null) {
            JSONArray customers = entity.getCustomerDetail().getJSONArray("customers");
            if (customers != null && customers.size() > 0) {
                JSONObject customer = customers.getJSONObject(0);
                mobile = customerInfoEncService.decryptMobile(customer.getString("mobile"));
            }
        }

        model.setList(sessionListService.searchPersonalSession(orgId, model.getKey(), mobile, entity.getClientId(), startTime, endTime));
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/{id}/chat/personal")
    @ApiOperation(value = "聊天详情-查看", response = PersonalChatHistorySearchModel.class)
    public ResponseModel searchPersonalChatHistory(@PathVariable Integer id, PersonalChatHistorySearchModel model, @RequestParam(required = false) Integer type) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        SessionListEntity sessionListEntity = sessionListRepo.findByOrgIdAndId(orgId, id);
        if (sessionListEntity == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "当前会话不存在");
        }
        if (StringUtils.isBlank(sessionListEntity.getGcid())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
        }
        Integer lastSkillGroupId = null;
        SearchHits<ChatHistoryElasticEntity> searchHits = complexChatHistoryElasticRepo.findByGcidOrSessionKeyOrderByDateAsc(sessionListEntity.getGcid(), sessionListEntity.getSessionKey());
        List<ChatHistoryElasticEntity> chatHistories = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        for (ChatHistoryElasticEntity entity : chatHistories) {
            PersonalChatHistoryRowModel rowModel = new PersonalChatHistoryRowModel();
            String scene = entity.getScene();
            String chatType = entity.getType();
            rowModel.setClientId(entity.getClientId());
            rowModel.setType(chatType);
            rowModel.setSender(entity.getSender());
            rowModel.setRecall(entity.isRecall());
            rowModel.setManual(entity.getManual());
            rowModel.setMsgId(entity.getMsgId());
            rowModel.setDate(entity.getDate());
            rowModel.setMedia(entity.getMedia());
            rowModel.setContent(entity.getContent());
            //客服端对用户敏感信息做处理
            if(Objects.equals(type, 1)){
                processUserSensitiveInfo(rowModel);
            }
            //事件转义且合并连续相同事件
            if (ChatHistoryElasticEntity.TYPE_EVENT.equals(chatType) && "SWITCH_SKILLGROUP".equals(entity.getContent())) {
                Integer skillGroupId = entity.getSkillGroupId();
                if (lastSkillGroupId != null && lastSkillGroupId.equals(skillGroupId)) {
                    continue;
                } else {
                    lastSkillGroupId = skillGroupId;
                    JSONObject extend = entity.getExtend();
                    //无法转义直接忽略
                    if (extend == null) {
                        continue;
                    }
                    //转义事件
                    rowModel.setContent(extend.getString("skillGroupName"));
                }
            } else {
                lastSkillGroupId = null;
            }
            //归类媒体文件
            if (ChatHistoryElasticEntity.TYPE_VIDEO.equals(chatType)
                    || ChatHistoryElasticEntity.TYPE_IMAGE.equals(chatType) || ChatHistoryElasticEntity.TYPE_VOICE.equals(chatType)) {
                PersonalChatHistoryRowModel mediaModel = new PersonalChatHistoryRowModel();
                BeanUtils.copyProperties(rowModel, mediaModel);
                model.getMediaList().add(mediaModel);
            }
            //将机器人对话和人工对话分开
            if (ChatHistoryElasticEntity.SCENE_TYPE_CS.equals(scene)) {
                PersonalChatHistoryRowModel csModel = new PersonalChatHistoryRowModel();
                BeanUtils.copyProperties(rowModel, csModel);
                model.getCsList().add(csModel);
            } else {
                PersonalChatHistoryRowModel botModel = new PersonalChatHistoryRowModel();
                BeanUtils.copyProperties(rowModel, botModel);
                model.getBotList().add(botModel);
            }
            model.getAllList().add(rowModel);
        }
        //消息数忽略事件和撤回这两类
        model.setAllCount(model.getAllList().stream().filter(p ->
                !p.getType().equals(ChatHistoryElasticEntity.TYPE_EVENT)
                        && !Objects.equals(p.getSender(), ChatHistoryElasticEntity.SENDER_TYPE_SYSTEM)
                        && !p.isRecall()).count());
        model.setCsCount(model.getCsList().stream().filter(p ->
                !p.getType().equals(ChatHistoryElasticEntity.TYPE_EVENT)
                        && !Objects.equals(p.getSender(), ChatHistoryElasticEntity.SENDER_TYPE_SYSTEM)
                        && !p.isRecall()).count());
        model.setBotCount(model.getBotList().stream().filter(p ->
                !p.getType().equals(ChatHistoryElasticEntity.TYPE_EVENT)
                        && !Objects.equals(p.getSender(), ChatHistoryElasticEntity.SENDER_TYPE_SYSTEM)
                        && !p.isRecall()).count());
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    private void processUserSensitiveInfo(PersonalChatHistoryRowModel rowModel) {
        if("text".equals(rowModel.getType()) && "user".equals(rowModel.getSender())) {
            String content = rowModel.getContent();
            Date todayStart = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            Boolean hisFlag = false;
            if(rowModel.getDate().before(todayStart)) {
                hisFlag = true;
            }
            content = CommonUtil.processUserSensitiveInfo(content, hisFlag);
            rowModel.setContent(content);
        }
    }

    @GetMapping("/history")
    @ApiOperation(value = "当前客服历史会话列表", response = StaffHistorySessionSearchModel.class)
    public ResponseModel searchStaffHistorySession(StaffHistorySessionSearchModel model) {
        //客服工作台会话记录只能查看当天的数据
        Date todayStart = DateUtils.truncate(new Date(), Calendar.DATE);
        Date todayEnd = DateUtils.addSeconds(DateUtils.addDays(todayStart, 1), -1);
        if((model.getStartTime() != null && model.getStartTime().before(todayStart)) || (model.getEndTime() != null && model.getEndTime().after(todayEnd))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "只能查看当天的会话记录");
        }
        if(model.getStartTime() == null && model.getEndTime() == null) {
            model.setStartTime(todayStart);
            model.setEndTime(todayEnd);
        }

        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String encMobile = customerInfoEncService.encryptMobile(model.getMobile());
        PageRequest pageRequest = PageRequest.of(model.getPageNumber(), model.getPageSize(), Sort.Direction.DESC, "createTime");
        SearchHits<SessionListElasticEntity> searchHits = complexSessionListElasticRepo.findRecordByParams(orgId, user.getEmail(),
                model.getCustomerName(), model.getMark(), model.getKey(), model.getServiceSummaryStatus(), model.getStartTime(),
                model.getEndTime(), model.getBusinessIds(), model.getClientIds(), model.getAppraiseLevels(),
                model.getUuid(), model.getUserId(), encMobile, pageRequest);
        if (searchHits != null) {
            model.setTotal(searchHits.getTotalHits());
            List<SessionListElasticEntity> sessionListElasticEntities = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
            model.setTotal(searchHits.getTotalHits());
            for (SessionListElasticEntity elasticEntity : sessionListElasticEntities) {
                StaffHistorySessionRowModel rowModel = new StaffHistorySessionRowModel();
                rowModel.setId(elasticEntity.getId());
                rowModel.setBusinessName(elasticEntity.getBusinessName());
                rowModel.setCustomerName(elasticEntity.getCustomerName());
                rowModel.setMark(elasticEntity.getMark());
                rowModel.setStartTime(elasticEntity.getGcTime());
                rowModel.setServiceSummaryStatus(elasticEntity.getServiceSummaryStatus());
                if (elasticEntity.getCustomerDetail() != null) {
                    JSONArray customers = elasticEntity.getCustomerDetail().getJSONArray("customers");
                    if (customers != null && customers.size() > 0) {
                        JSONObject customer = customers.getJSONObject(0);
                        rowModel.setUuid(customer.getString("uuid"));
                        rowModel.setUserId(customer.getInteger("userId"));
                        String mobile = customerInfoEncService.decryptMobile(customer.getString("mobile"));
                        if (StringUtils.isNotBlank(mobile) && mobile.length() > 10) {
                            mobile = CommonUtil.maskPhoneNum(mobile);
                            rowModel.setMobile(mobile);
                        }
                    }
                }
                //用户满意度
                if (elasticEntity.getSatisfactionLevel() != null) {
                    if (Objects.equals(-1, elasticEntity.getSatisfactionLevel())) {
                        rowModel.setAppraiseStatus(-1);
                    } else {
                        rowModel.setAppraiseStatus(1);
                        rowModel.setAppraiseLevel(elasticEntity.getSatisfactionLevel());
                    }
                }
                rowModel.setClientId(elasticEntity.getClientTypeId());
                model.getList().add(rowModel);
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }


    @GetMapping("/global")
    @ApiOperation(value = "公共历史记录查询", response = GlobalSessionSearchModel.class)
    public ResponseModel searchGlobalSession(GlobalSessionSearchModel model) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        PageRequest pageRequest = PageRequest.of(model.getPageNumber(), model.getPageSize(), Sort.Direction.DESC, "createTime");
        List<Integer> includeSessionIds = new ArrayList<>();
        List<Integer> excludeSessionIds = new ArrayList<>();
        sessionListService.getSessionIdByFaceDetection(model.getStartTime(), model.getEndTime(), model.getFaceStatus(), includeSessionIds, excludeSessionIds);
        if(model.getFaceStatus()!=null && Arrays.asList(2,3,4).contains(model.getFaceStatus()) && CollectionUtils.isEmpty(includeSessionIds)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
        }
        //过滤消息数
        sessionListService.getSessionIdByMsgCount((JSONObject)JSON.toJSON(model), includeSessionIds);
        if ((model.getUserMsgCountMin()!=null || model.getUserMsgCountMax()!=null || model.getServiceMsgCountMin()!=null || model.getServiceMsgCountMax()!=null) 
            && CollectionUtils.isEmpty(includeSessionIds)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
        }
        String encMobile = customerInfoEncService.encryptMobile(model.getMobile());
        SearchHits<SessionListElasticEntity> searchHits = complexSessionListElasticRepo.findRecordByParams(orgId,
                model.getStaffEmails(), model.getBusinessIds(), model.getClientIds(), model.getAppraiseLevels(),
                model.getContent(), model.getHasPic(), model.getRespKeys(), model.getStartTime(), model.getEndTime(),
                model.getUuid(), model.getUserId(), encMobile, model.getCustomerName(),
                includeSessionIds, excludeSessionIds, pageRequest);
        if (searchHits != null) {
            model.setTotal(searchHits.getTotalHits());
            List<SessionListElasticEntity> sessionListElastics = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
            Set<Integer> sessionIds = sessionListElastics.stream().map(SessionListElasticEntity::getId).collect(Collectors.toSet());
            Map<Integer, SessionStatisticsEntity> statisticsEntityMap = sessionStatisticsRepo.findBySessionIdIn(sessionIds).stream().collect(Collectors.toMap(SessionStatisticsEntity::getSessionId, Function.identity()));
            List<FaceDetectionEntity> faceList = faceDetectionRepo.findAllBySessionIdIn(sessionIds);
            Map<Integer, Set<Integer>> faceResultMap = faceList.stream().collect(Collectors.groupingBy(FaceDetectionEntity::getSessionId, Collectors.mapping(FaceDetectionEntity::getCode, Collectors.toSet())));
            for (SessionListElasticEntity elasticEntity : sessionListElastics) {
                String gcid = elasticEntity.getGcid();
                GlobalSessionRowModel rowModel = new GlobalSessionRowModel();
                rowModel.setId(elasticEntity.getId());
                rowModel.setBusinessName(elasticEntity.getBusinessName());
                rowModel.setCustomerName(elasticEntity.getCustomerName());
                if (elasticEntity.getCustomerDetail() != null) {
                    JSONArray customers = elasticEntity.getCustomerDetail().getJSONArray("customers");
                    if (customers != null && customers.size() > 0) {
                        JSONObject customer = customers.getJSONObject(0);
                        rowModel.setUuid(customer.getString("uuid"));
                        rowModel.setUserId(customer.getInteger("userId"));
                        rowModel.setMobile(CommonUtil.maskPhoneNum(customerInfoEncService.decryptMobile(customer.getString("mobile"))));
                    }
                }
                String extraField = userRepo.findExtraField(orgId, appPropertyConfig.getProductId(), elasticEntity.getLastServiceUser());
                if (StringUtils.isNotBlank(extraField)) {
                    JSONObject extra = JSON.parseObject(extraField);
                    rowModel.setLastCustomerService(extra.getString("nickName"));
                }
                rowModel.setStartTime(elasticEntity.getCreateTime());
                rowModel.setEndTime(elasticEntity.getOfflineTime());
                rowModel.setWaitTime(DateUtil.formatSecondsTime(elasticEntity.getWaitSecond(), null));
                rowModel.setDurationTime(DateUtil.formatSecondsTime(elasticEntity.getDurationSecond(), null));
                SessionStatisticsEntity statisticsEntity = statisticsEntityMap.get(elasticEntity.getId());
                if (statisticsEntity != null) {
                    rowModel.setBotReply(statisticsEntity.getBotAnswerCnt());
                    rowModel.setArtificialReply(statisticsEntity.getManualAnswerCnt());
                    rowModel.setUserReply(statisticsEntity.getSeekBotCnt() + statisticsEntity.getSeekManualCnt());
                } else if (StringUtils.isNotBlank(gcid)) {
                    rowModel.setBotReply(complexChatHistoryElasticRepo.countByGcidAndSenderAndTypeNotAndRecallNot(gcid,
                            ChatHistoryElasticEntity.SENDER_TYPE_BOT, ChatHistoryElasticEntity.TYPE_EVENT));
                    rowModel.setArtificialReply(complexChatHistoryElasticRepo.countByGcidAndSenderAndTypeNotAndRecallNot(gcid,
                            ChatHistoryElasticEntity.SENDER_TYPE_MANUAL, ChatHistoryElasticEntity.TYPE_EVENT));
                    rowModel.setUserReply(complexChatHistoryElasticRepo.countByGcidAndSenderAndTypeNotAndRecallNot(gcid,
                            ChatHistoryElasticEntity.SENDER_TYPE_USER, ChatHistoryElasticEntity.TYPE_EVENT));
                }
                if (StringUtils.isNotBlank(gcid)) {
                    if (model.getHasPic() == null) { //忽略是否包含图片才单独去查
                        long imageCount = complexChatHistoryElasticRepo.countByGcidAndType(gcid, ChatHistoryElasticEntity.TYPE_IMAGE);
                        if (imageCount > 0) {
                            rowModel.setHasPic(1);
                        }
                    } else {
                        rowModel.setHasPic(model.getHasPic());
                    }
                }
                //用户满意度
                if (elasticEntity.getSatisfactionLevel() != null) {
                    if (Objects.equals(-1, elasticEntity.getSatisfactionLevel())) {
                        rowModel.setAppraiseStatus(-1);
                    } else {
                        rowModel.setAppraiseStatus(1);
                        rowModel.setAppraiseLevel(elasticEntity.getSatisfactionLevel());
                    }
                }
                //人脸验证结果
                Set<Integer> faceResultSet = faceResultMap.get(elasticEntity.getId());
                String faceStatus = "";
                if (CollectionUtils.isEmpty(faceResultSet)) {
                    faceStatus = "未发送验证请求";
                } else if (faceResultSet.contains(0)) {
                    faceStatus = "验证通过";
                } else if (faceResultSet.stream().anyMatch(v -> v!=null && v>0)){
                    faceStatus = "验证不通过";
                } else if (faceResultSet.contains(null)) {
                    faceStatus = "用户未验证";
                }
                rowModel.setFaceStatus(faceStatus);

                rowModel.setClientId(elasticEntity.getClientTypeId());
                rowModel.setFirstRespTimeout(elasticEntity.getFirstRespTimeout());
                rowModel.setAvgRespTimeout(elasticEntity.getAvgRespTimeout());
                rowModel.setRespTimeout(elasticEntity.getRespTimeout());
                model.getList().add(rowModel);
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/export")
    @ApiOperation(value = "公共历史会话-导出")
    public ResponseModel exportSessionRecord(GlobalSessionExportModel model) {
        UserDomain user = getUser();
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.SESSION_RECORD, TaskEntity.PORT_EXPORT, model, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }

    @GetMapping("/chat/export")
    @ApiOperation(value = "公共历史聊天详情-导出")
    public ResponseModel exportChatDetail(GlobalSessionExportModel model) {
        UserDomain user = getUser();
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.CHAT_DETAIL, TaskEntity.PORT_EXPORT, model, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }

    @GetMapping("/media")
    @ApiOperation(value = "媒体文件查询", response = GlobalMediaSearchModel.class)
    public ResponseModel searchSessionMedia(GlobalMediaSearchModel model) {
        if (model.getStartTime() == null || model.getEndTime() == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "起止时间不能为空");
        }
        if (StringUtils.isNotBlank(model.getMediaType())
                && !ChatHistoryElasticEntity.TYPE_IMAGE.equals(model.getMediaType())
                && !ChatHistoryElasticEntity.TYPE_VIDEO.equals(model.getMediaType())) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "文件类型不合法");
        }
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        PageRequest pageRequest = PageRequest.of(model.getPageNumber(), model.getPageSize(), Sort.Direction.DESC, "date");
        SearchHits<ChatHistoryElasticEntity> searchHits = complexChatHistoryElasticRepo.findByParams(orgId,
                model.getCustomerName(), model.getMediaType(), model.getStartTime(), model.getEndTime(), model.getStaffEmails(), pageRequest);
        if (searchHits != null) {
            model.setTotal(searchHits.getTotalHits());
            List<ChatHistoryElasticEntity> chatHistoryList = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
            Set<String> gcidSet = chatHistoryList.stream().map(ChatHistoryElasticEntity::getGcid).collect(Collectors.toSet());
            Map<String, SessionListEntity> sessionListMap = sessionListRepo.findByGcidIn(gcidSet).stream().collect(Collectors.toMap(SessionListEntity::getGcid, Function.identity()));
            for (ChatHistoryElasticEntity elasticEntity : chatHistoryList) {
                String sender =  Objects.equals(elasticEntity.getSender(), ChatHistoryElasticEntity.SENDER_TYPE_BOT) ? "2" : "1";
                String medialUrl = buildMedialUrl(sender, elasticEntity.getContent());
                SessionListEntity session = sessionListMap.get(elasticEntity.getGcid());
                List<String> emails = Lists.newArrayList(session.getServiceUser(),
                        session.getFromServiceUser(), session.getLastServiceUser());
                if (session.getTransferToUsers() != null && session.getTransferToUsers().size() > 0) {
                    emails.addAll(JSONObject.parseArray(session.getTransferToUsers().toJSONString(), String.class));
                }
                emails.removeAll(Collections.singleton(null));
                List<String> staffNames = userStateRepo.findByOrgIdAndEmails(orgId, emails);
                Set<String> nameSet = new LinkedHashSet<>();
                if (CollectionUtils.isNotEmpty(staffNames)) {
                    staffNames.removeAll(Collections.singleton(null));
                    nameSet.addAll(staffNames);
                }
                model.getRowList().add(new GlobalMediaSearchModel.MediaInfoRow(session.getCustomerName(),
                        medialUrl, elasticEntity, nameSet));
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

}
