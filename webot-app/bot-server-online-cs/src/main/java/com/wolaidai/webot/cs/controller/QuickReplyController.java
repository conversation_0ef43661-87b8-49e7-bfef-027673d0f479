package com.wolaidai.webot.cs.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.ExcelReader;
import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.common.util.StringUtil;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.constant.OssKeyPath;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.quickreply.*;
import com.wolaidai.webot.cs.model.upload.UploadCheckResultModel;
import com.wolaidai.webot.cs.model.upload.UploadErrorModel;
import com.wolaidai.webot.cs.model.upload.UploadProgressModel;
import com.wolaidai.webot.cs.service.QuickReplyService;
import com.wolaidai.webot.cs.service.TaskService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.chat.QuickReplyEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ReplyCategoryEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.repo.ComplexQuickReplyRepo;
import com.wolaidai.webot.data.mysql.repo.QuickReplyRepo;
import com.wolaidai.webot.data.mysql.repo.ReplyCategoryRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import com.wolaidai.webot.security.domain.UserDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Api(tags = "快捷回复接口")
@RestController
@RequestMapping("/quickReply")
public class QuickReplyController extends BaseController {

    @Autowired
    protected AppPropertyConfig appPropertyConfig;

    @Autowired
    private QuickReplyRepo quickReplyRepo;

    @Autowired
    private ReplyCategoryRepo replyCategoryRepo;

    @Autowired
    private ComplexQuickReplyRepo complexQuickReplyRepo;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private QuickReplyService quickReplyService;
    @Autowired
    private TaskService taskService;
    private final int NODE_TYPE_CATEGORY_INT = 0;
    private final int NODE_TYPE_CONTENT_INT = 1;

    @PostMapping("/text")
    @ApiOperation(value = "创建个人文本快捷回复")
    public ResponseModel createQuickReplyText(@Valid @RequestBody QuickReplyTextAddModel model) {
        UserDomain user = getUser();
        String email = user.getEmail();
        Integer orgId = user.getOrganizationId();
        ReplyCategoryEntity categoryEntity = replyCategoryRepo.findByIdAndOrgIdAndCreatorAndGlobal(
                model.getCategoryId(), orgId, email, ReplyCategoryEntity.GLOBAL_N);
        if (categoryEntity == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        QuickReplyEntity sameContent = quickReplyRepo.findByOrgIdAndCategoryIdAndContentAndCreatorAndGlobal(
                orgId, model.getCategoryId(), model.getContent(), email, QuickReplyEntity.GLOBAL_N);
        if (sameContent != null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名文本快捷回复已存在");
        }
        QuickReplyEntity entity = new QuickReplyEntity();
        entity.setOrgId(orgId);
        entity.setAnswerType(QuickReplyEntity.ANSWER_TYPE_TEXT);
        entity.setCategory(categoryEntity);
        entity.setContent(model.getContent());
        entity.setCreator(email);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        quickReplyRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/text/global")
    @ApiOperation(value = "创建公共文本快捷回复")
    public ResponseModel createGlobalQuickReplyText(@Valid @RequestBody QuickReplyTextAddModel model) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        ReplyCategoryEntity categoryEntity =
                replyCategoryRepo.findByIdAndOrgIdAndGlobal(model.getCategoryId(), orgId, ReplyCategoryEntity.GLOBAL_Y);
        if (categoryEntity == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        QuickReplyEntity sameContent = quickReplyRepo.findByOrgIdAndCategoryIdAndContentAndGlobal(
                orgId, model.getCategoryId(), model.getContent(), QuickReplyEntity.GLOBAL_Y);
        if (sameContent != null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名文本快捷回复已存在");
        }
        QuickReplyEntity entity = new QuickReplyEntity();
        entity.setOrgId(orgId);
        entity.setAnswerType(QuickReplyEntity.ANSWER_TYPE_TEXT);
        entity.setCategory(categoryEntity);
        entity.setContent(model.getContent());
        entity.setGlobal(QuickReplyEntity.GLOBAL_Y);
        entity.setCreator(user.getEmail());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        quickReplyRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/attachment")
    @ApiOperation(value = "创建个人附件快捷回复")
    public ResponseModel createQuickReplyAttachment(@Valid QuickReplyAttachmentAddModel model) throws IOException {
        UserDomain user = getUser();
        String email = user.getEmail();
        Integer orgId = user.getOrganizationId();
        MultipartFile file = model.getFile();
        String attachmentType = model.getAttachmentType();
        ResponseModel responseModel = checkAttachment(attachmentType, file);
        if (responseModel != null) {
            return responseModel;
        }
        ReplyCategoryEntity categoryEntity =
                replyCategoryRepo.findByIdAndOrgIdAndCreatorAndGlobal(model.getCategoryId(), orgId, email, ReplyCategoryEntity.GLOBAL_N);
        if (categoryEntity == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        QuickReplyEntity entity = new QuickReplyEntity();
        entity.setOrgId(orgId);
        entity.setAnswerType(QuickReplyEntity.ANSWER_TYPE_ATTACHMENT);
        entity.setCategory(categoryEntity);
        entity.setFileName(file.getOriginalFilename());
        entity.setFileSize(file.getSize());
        entity.setFileType(attachmentType);
        entity.setRemark(model.getRemark());
        entity.setCreator(email);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        byte[] fileBytes = file.getBytes();
        String keyPath = generateFileKeyPath(entity);
        if (StringUtils.isNotBlank(appPropertyConfig.getBaseFileDir())) {
            File keyFile = new File(appPropertyConfig.getBaseFileDir() + "/" + keyPath);
            keyFile.getParentFile().mkdirs();
            try (OutputStream os = new FileOutputStream(keyFile)) {
                IOUtils.write(fileBytes, os);
            }
        } else {
            OssFileClient.putObject(appPropertyConfig.getOssBucketName(), keyPath, new ByteArrayInputStream(fileBytes));
        }
        entity.setFileUrl(keyPath);
        quickReplyRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/attachment/global")
    @ApiOperation(value = "创建公共附件快捷回复")
    public ResponseModel createGlobalQuickReplyAttachment(@Valid QuickReplyAttachmentAddModel model) throws IOException{
        UserDomain user = getUser();
        String email = user.getEmail();
        Integer orgId = user.getOrganizationId();
        MultipartFile file = model.getFile();
        String attachmentType = model.getAttachmentType();
        ResponseModel responseModel = checkAttachment(attachmentType, file);
        if (responseModel != null) {
            return responseModel;
        }
        ReplyCategoryEntity categoryEntity =
                replyCategoryRepo.findByIdAndOrgIdAndGlobal(model.getCategoryId(), orgId, ReplyCategoryEntity.GLOBAL_Y);
        if (categoryEntity == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        QuickReplyEntity entity = new QuickReplyEntity();
        entity.setOrgId(orgId);
        entity.setAnswerType(QuickReplyEntity.ANSWER_TYPE_ATTACHMENT);
        entity.setCategory(categoryEntity);
        entity.setFileName(file.getOriginalFilename());
        entity.setFileSize(file.getSize());
        entity.setFileType(attachmentType);
        entity.setGlobal(QuickReplyEntity.GLOBAL_Y);
        entity.setRemark(model.getRemark());
        entity.setCreator(email);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        byte[] fileBytes = file.getBytes();
        String keyPath = generateFileKeyPath(entity);
        if (StringUtils.isNotBlank(appPropertyConfig.getBaseFileDir())) {
            File keyFile = new File(appPropertyConfig.getBaseFileDir() + "/" + keyPath);
            keyFile.getParentFile().mkdirs();
            try (OutputStream os = new FileOutputStream(keyFile)) {
                IOUtils.write(fileBytes, os);
            }
        } else {
            OssFileClient.putObject(appPropertyConfig.getOssBucketName(), keyPath, new ByteArrayInputStream(fileBytes));
        }
        entity.setFileUrl(keyPath);
        quickReplyRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/{id}/text")
    @ApiOperation(value = "修改个人文本快捷回复")
    public ResponseModel updateQuickReplyText(@PathVariable Integer id, @Valid @RequestBody QuickReplyTextUpdModel model) {
        Optional<QuickReplyEntity> replyRepoById = quickReplyRepo.findById(id);
        if (!replyRepoById.isPresent()) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "快捷回复不存在");
        }
        QuickReplyEntity entity = replyRepoById.get();
        QuickReplyEntity sameContent = quickReplyRepo.findByOrgIdAndCategoryIdAndContentAndGlobal(
                getUser().getOrganizationId(), entity.getCategory().getId(), model.getContent(), QuickReplyEntity.GLOBAL_N);
        if (sameContent != null && !sameContent.getId().equals(id)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名文本快捷回复已存在");
        }
        entity.setContent(model.getContent());
        entity.setUpdateTime(new Date());
        quickReplyRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/{id}/text/global")
    @ApiOperation(value = "修改公共文本快捷回复")
    public ResponseModel updateGlobalQuickReplyText(@PathVariable Integer id, @Valid @RequestBody QuickReplyTextUpdModel model) {
        Optional<QuickReplyEntity> replyRepoById = quickReplyRepo.findById(id);
        if (!replyRepoById.isPresent()) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "快捷回复不存在");
        }
        QuickReplyEntity entity = replyRepoById.get();
        QuickReplyEntity sameContent = quickReplyRepo.findByOrgIdAndCategoryIdAndContentAndGlobal(
                getUser().getOrganizationId(), entity.getCategory().getId(), model.getContent(), QuickReplyEntity.GLOBAL_Y);
        if (sameContent != null && !sameContent.getId().equals(id)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名文本快捷回复已存在");
        }
        entity.setContent(model.getContent());
        entity.setUpdateTime(new Date());
        quickReplyRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/{id}/attachment")
    @ApiOperation(value = "修改个人附件快捷回复")
    public ResponseModel updateQuickReplyAttachment(@PathVariable Integer id, @Valid QuickReplyAttachmentUpdModel model) throws IOException{
        MultipartFile file = model.getFile();
        String attachmentType = model.getAttachmentType();
        ResponseModel responseModel = checkAttachment(attachmentType, file);
        if (responseModel != null) {
            return responseModel;
        }
        Optional<QuickReplyEntity> replyRepoById = quickReplyRepo.findById(id);
        if (!replyRepoById.isPresent()) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "快捷回复不存在");
        }
        QuickReplyEntity entity = replyRepoById.get();
        if (StringUtils.isNotBlank(attachmentType) && !file.isEmpty()) {
            entity.setAnswerType(QuickReplyEntity.ANSWER_TYPE_ATTACHMENT);
            entity.setFileName(file.getOriginalFilename());
            entity.setFileSize(file.getSize());
            entity.setFileType(attachmentType);
            byte[] fileBytes = file.getBytes();
            String keyPath = generateFileKeyPath(entity);
            if (StringUtils.isNotBlank(appPropertyConfig.getBaseFileDir())) {
                File keyFile = new File(appPropertyConfig.getBaseFileDir() + "/" + keyPath);
                keyFile.getParentFile().mkdirs();
                try (OutputStream os = new FileOutputStream(keyFile)) {
                    IOUtils.write(fileBytes, os);
                }
            } else {
                OssFileClient.putObject(appPropertyConfig.getOssBucketName(), keyPath, new ByteArrayInputStream(fileBytes));
            }
            entity.setFileUrl(keyPath);
        }
        entity.setRemark(model.getRemark());
        entity.setUpdateTime(new Date());
        quickReplyRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/{id}/attachment/global")
    @ApiOperation(value = "修改公共附件快捷回复")
    public ResponseModel updateGlobalQuickReplyAttachment(@PathVariable Integer id, QuickReplyAttachmentUpdModel model) throws IOException {
        MultipartFile file = model.getFile();
        String attachmentType = model.getAttachmentType();
        ResponseModel responseModel = checkAttachment(attachmentType, file);
        if (responseModel != null) {
            return responseModel;
        }
        Optional<QuickReplyEntity> replyRepoById = quickReplyRepo.findById(id);
        if (!replyRepoById.isPresent()) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "快捷回复不存在");
        }
        QuickReplyEntity entity = replyRepoById.get();
        if (StringUtils.isNotBlank(attachmentType) && !file.isEmpty()) {
            entity.setAnswerType(QuickReplyEntity.ANSWER_TYPE_ATTACHMENT);
            entity.setFileName(file.getOriginalFilename());
            entity.setFileSize(file.getSize());
            entity.setFileType(attachmentType);
            byte[] fileBytes = file.getBytes();
            String keyPath = generateFileKeyPath(entity);
            if (StringUtils.isNotBlank(appPropertyConfig.getBaseFileDir())) {
                File keyFile = new File(appPropertyConfig.getBaseFileDir() + "/" + keyPath);
                keyFile.getParentFile().mkdirs();
                try (OutputStream os = new FileOutputStream(keyFile)) {
                    IOUtils.write(fileBytes, os);
                }
            } else {
                OssFileClient.putObject(appPropertyConfig.getOssBucketName(), keyPath, new ByteArrayInputStream(fileBytes));
            }
            entity.setFileUrl(keyPath);
        }
        entity.setRemark(model.getRemark());
        entity.setUpdateTime(new Date());
        quickReplyRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @DeleteMapping("/text")
    @ApiOperation(value = "删除个人文本快捷回复")
    public ResponseModel deleteQuickReplyText(QuickReplyDeleteModel deleteModel) {
        for (Integer id : deleteModel.getIdList()) {
            Optional<QuickReplyEntity> replyRepoById = quickReplyRepo.findById(id);
            if (!replyRepoById.isPresent()) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "快捷回复不存在");
            }
            quickReplyRepo.deleteById(id);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @DeleteMapping("/text/global")
    @ApiOperation(value = "删除公共文本快捷回复")
    public ResponseModel deleteGlobalQuickReplyText(QuickReplyDeleteModel deleteModel) {
        for (Integer id : deleteModel.getIdList()) {
            Optional<QuickReplyEntity> replyRepoById = quickReplyRepo.findById(id);
            if (!replyRepoById.isPresent()) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "快捷回复不存在");
            }
            quickReplyRepo.deleteById(id);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @DeleteMapping("/attachment")
    @ApiOperation(value = "删除个人附件快捷回复")
    public ResponseModel deleteQuickReplyAttachment(QuickReplyDeleteModel deleteModel) {
        for (Integer id : deleteModel.getIdList()) {
            Optional<QuickReplyEntity> replyRepoById = quickReplyRepo.findById(id);
            if (!replyRepoById.isPresent()) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "快捷回复不存在");
            }
            quickReplyRepo.deleteById(id);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @DeleteMapping("/attachment/global")
    @ApiOperation(value = "删除公共附件快捷回复")
    public ResponseModel deleteGlobalQuickReplyAttachment(QuickReplyDeleteModel deleteModel) {
        for (Integer id : deleteModel.getIdList()) {
            Optional<QuickReplyEntity> replyRepoById = quickReplyRepo.findById(id);
            if (!replyRepoById.isPresent()) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "快捷回复不存在");
            }
            quickReplyRepo.deleteById(id);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @GetMapping("/text/tree")
    @ApiOperation(value = "查询个人文本快捷回复(树形)", response = QuickReplyTextResponseModel.class)
    public ResponseModel searchTreeQuickReplyText(QuickReplyTextResponseModel model) {
        UserDomain user = getUser();
        quickReplyService.getTextQuickReply(user.getOrganizationId(), user.getEmail(), QuickReplyEntity.GLOBAL_N, model);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/attachment/tree")
    @ApiOperation(value = "查询个人附件快捷回复(树形)", response = QuickReplyAttachmentResponseModel.class)
    public ResponseModel searchTreeQuickReplyAttachment(QuickReplyAttachmentResponseModel model) {
        getAttachmentQuickReply(QuickReplyEntity.GLOBAL_N, model);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/text/tree/global")
    @ApiOperation(value = "查询公共文本快捷回复(树形)", response = QuickReplyTextResponseModel.class)
    public ResponseModel searchTreeGlobalQuickReplyText(QuickReplyTextResponseModel model) {
        UserDomain user = getUser();
        quickReplyService.getTextQuickReply(user.getOrganizationId(), user.getEmail(), QuickReplyEntity.GLOBAL_Y, model);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/attachment/tree/global")
    @ApiOperation(value = "查询公共附件快捷回复(树形)", response = QuickReplyAttachmentResponseModel.class)
    public ResponseModel searchTreeGlobalQuickReplyAttachment(QuickReplyAttachmentResponseModel model) {
        getAttachmentQuickReply(QuickReplyEntity.GLOBAL_Y, model);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/text")
    @ApiOperation(value = "查询个人快捷回复文本", response = QuickReplyTextSearchModel.class)
    public ResponseModel searchQuickReplyText(QuickReplyTextSearchModel model) {
        Page<QuickReplyEntity> quickReplies = complexQuickReplyRepo.findQuickReplies(getUser().getOrganizationId(),
                getUser().getEmail(), QuickReplyEntity.ANSWER_TYPE_TEXT, model.getCategoryId(), model.getKey(), QuickReplyEntity.GLOBAL_N,
                model.getPageNumber() == -1 ? Pageable.unpaged() : PageRequest.of(model.getPageNumber(), model.getPageSize()));
        model.setTotal(quickReplies.getTotalElements());
        for (QuickReplyEntity replyEntity : quickReplies.getContent()) {
            model.getList().add(new QuickReplyTextRowModel(replyEntity));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/text/global")
    @ApiOperation(value = "查询公共快捷回复文本", response = QuickReplyTextSearchModel.class)
    public ResponseModel searchGlobalQuickReplyText(QuickReplyTextSearchModel model) {
        Page<QuickReplyEntity> quickReplies = complexQuickReplyRepo.findQuickReplies(getUser().getOrganizationId(),
                null, QuickReplyEntity.ANSWER_TYPE_TEXT, model.getCategoryId(), model.getKey(), QuickReplyEntity.GLOBAL_Y,
                model.getPageNumber() == -1 ? Pageable.unpaged() : PageRequest.of(model.getPageNumber(), model.getPageSize()));
        model.setTotal(quickReplies.getTotalElements());
        for (QuickReplyEntity replyEntity : quickReplies.getContent()) {
            model.getList().add(new QuickReplyTextRowModel(replyEntity));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/attachment")
    @ApiOperation(value = "查询个人快捷回复附件", response = QuickReplyAttachmentSearchModel.class)
    public ResponseModel searchQuickReplyAttachment(QuickReplyAttachmentSearchModel model) {
        Page<QuickReplyEntity> quickReplies = complexQuickReplyRepo.findQuickReplies(getUser().getOrganizationId(),
                getUser().getEmail(), QuickReplyEntity.ANSWER_TYPE_ATTACHMENT, model.getCategoryId(), model.getKey(), QuickReplyEntity.GLOBAL_N,
                model.getPageNumber() == -1 ? Pageable.unpaged() : PageRequest.of(model.getPageNumber(), model.getPageSize()));
        model.setTotal(quickReplies.getTotalElements());
        for (QuickReplyEntity replyEntity : quickReplies.getContent()) {
            replyEntity.setFileUrl(generateOssAttachmentUrl(replyEntity.getFileUrl()));
            model.getList().add(new QuickReplyAttachmentRowModel(replyEntity));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/attachment/global")
    @ApiOperation(value = "查询公共快捷回复附件", response = QuickReplyAttachmentSearchModel.class)
    public ResponseModel searchGlobalQuickReplyAttachment(QuickReplyAttachmentSearchModel model) {
        Page<QuickReplyEntity> quickReplies = complexQuickReplyRepo.findQuickReplies(getUser().getOrganizationId(),
                null, QuickReplyEntity.ANSWER_TYPE_ATTACHMENT, model.getCategoryId(), model.getKey(), QuickReplyEntity.GLOBAL_Y,
                model.getPageNumber() == -1 ? Pageable.unpaged() : PageRequest.of(model.getPageNumber(), model.getPageSize()));
        model.setTotal(quickReplies.getTotalElements());
        for (QuickReplyEntity replyEntity : quickReplies.getContent()) {
            replyEntity.setFileUrl(generateOssAttachmentUrl(replyEntity.getFileUrl()));
            model.getList().add(new QuickReplyAttachmentRowModel(replyEntity));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @PutMapping("/text/move")
    @ApiOperation(value = "移动个人文本快捷回复")
    public ResponseModel moveQuickReplyText(@Valid @RequestBody QuickReplyMoveModel moveModel) {
        if (CollectionUtils.isEmpty(moveModel.getReplyIds())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "移动内容为空");
        }
        Integer orgId = getUser().getOrganizationId();
        Integer categoryId = moveModel.getCategoryId();
        ReplyCategoryEntity category = replyCategoryRepo.findByIdAndOrgIdAndGlobal(categoryId, orgId, ReplyCategoryEntity.GLOBAL_N);
        if (category == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        quickReplyRepo.updateCategoryByIdsAndOrgIdAndGlobal(categoryId, moveModel.getReplyIds(), orgId, QuickReplyEntity.GLOBAL_N);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/text/global/move")
    @ApiOperation(value = "移动公共文本快捷回复")
    public ResponseModel moveGlobalQuickReplyText(@Valid @RequestBody QuickReplyMoveModel moveModel) {
        if (CollectionUtils.isEmpty(moveModel.getReplyIds())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "移动内容为空");
        }
        Integer orgId = getUser().getOrganizationId();
        Integer categoryId = moveModel.getCategoryId();
        ReplyCategoryEntity category = replyCategoryRepo.findByIdAndOrgIdAndGlobal(categoryId, orgId, ReplyCategoryEntity.GLOBAL_Y);
        if (category == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        quickReplyRepo.updateCategoryByIdsAndOrgIdAndGlobal(categoryId, moveModel.getReplyIds(), orgId, QuickReplyEntity.GLOBAL_Y);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/attachment/move")
    @ApiOperation(value = "移动个人附件快捷回复")
    public ResponseModel moveQuickReplyAttachment(@Valid @RequestBody QuickReplyMoveModel moveModel) {
        if (CollectionUtils.isEmpty(moveModel.getReplyIds())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "移动内容为空");
        }
        Integer orgId = getUser().getOrganizationId();
        Integer categoryId = moveModel.getCategoryId();
        ReplyCategoryEntity category = replyCategoryRepo.findByIdAndOrgIdAndGlobal(categoryId, orgId, ReplyCategoryEntity.GLOBAL_N);
        if (category == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        quickReplyRepo.updateCategoryByIdsAndOrgIdAndGlobal(categoryId, moveModel.getReplyIds(), orgId, QuickReplyEntity.GLOBAL_N);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/attachment/global/move")
    @ApiOperation(value = "移动公共附件快捷回复")
    public ResponseModel moveGlobalQuickReplyAttachment(@Valid @RequestBody QuickReplyMoveModel moveModel) {
        if (CollectionUtils.isEmpty(moveModel.getReplyIds())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "移动内容为空");
        }
        Integer orgId = getUser().getOrganizationId();
        Integer categoryId = moveModel.getCategoryId();
        ReplyCategoryEntity category = replyCategoryRepo.findByIdAndOrgIdAndGlobal(categoryId, orgId, ReplyCategoryEntity.GLOBAL_Y);
        if (category == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        quickReplyRepo.updateCategoryByIdsAndOrgIdAndGlobal(categoryId, moveModel.getReplyIds(), orgId, QuickReplyEntity.GLOBAL_Y);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/text/import")
    @ApiOperation(value = "个人文本快捷回复导入")
    public ResponseModel importQuickReplyText(MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (!(StringUtils.endsWithIgnoreCase(fileName, ".xlsx") || StringUtils.endsWithIgnoreCase(fileName, ".xls"))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "只支持xlsx或xls格式文件");
        }
        if (file.getSize() > 30 * 1024 * 1024) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "文件大小不能超过30M");
        }
        UserDomain user = getUser();
        String email = user.getEmail();
        Integer orgId = user.getOrganizationId();
        String suffix = StringUtils.substringAfterLast(fileName, ".");
        File tmpFile = File.createTempFile("importQuickReplyText_personal_userId_" + user.getId(), "." + suffix);
        file.transferTo(tmpFile);
        ExcelReader excelReader = new ExcelReader(tmpFile.getAbsolutePath());
        UploadCheckResultModel resultModel = validateRecords(fileName, excelReader);
        String uploadId = resultModel.getUploadId();
        String importKey = String.format(RedisKey.IMPORT_QUICK_REPLY_PERSONAL_JOB, orgId, email);
        if (resultModel.getErrors().isEmpty()) {
            synchronized (this) {
                String hasJob = redisTemplate.opsForValue().get(importKey);
                if (StringUtils.isNotBlank(hasJob)) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "当前已经有任务正在导入，请稍后再试");
                }
                redisTemplate.opsForValue().set(importKey, JSON.toJSONString(resultModel), 1, TimeUnit.HOURS);
                UploadProgressModel uploadProgressModel = new UploadProgressModel();
                uploadProgressModel.setTotal(excelReader.getTotalCount());
                redisTemplate.opsForValue().set(String.format(RedisKey.IMPORT_QUICK_REPLY_PROGRESS, orgId,
                        uploadId), JSON.toJSONString(uploadProgressModel), 1, TimeUnit.DAYS);
            }
            new Thread(() -> {
                importRecords(fileName, excelReader, uploadId, QuickReplyEntity.GLOBAL_N, orgId, email);
                redisTemplate.delete(importKey);
            }).start();
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导入成功", resultModel);
        } else {
            redisTemplate.opsForValue().set(String.format(RedisKey.IMPORT_QUICK_REPLY_ERROR, orgId,
                    uploadId), JSON.toJSONString(resultModel.getErrors()), 1, TimeUnit.DAYS);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "导入失败", resultModel);
        }
    }

    @PostMapping("/text/import/global")
    @ApiOperation(value = "公共文本快捷回复导入")
    public ResponseModel importGlobalQuickReplyText(MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (!(StringUtils.endsWithIgnoreCase(fileName, ".xlsx")
                || StringUtils.endsWithIgnoreCase(fileName, ".xls"))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "只支持xlsx或xls格式文件");
        }
        if (file.getSize() > 30 * 1024 * 1024) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "文件大小不能超过30M");
        }
        UserDomain user = getUser();
        String email = user.getEmail();
        Integer orgId = user.getOrganizationId();
        String suffix = StringUtils.substringAfterLast(fileName, ".");
        File tmpFile = File.createTempFile("importQuickReplyText_global_userId_" + user.getId(), "." + suffix);
        file.transferTo(tmpFile);
        ExcelReader excelReader = new ExcelReader(tmpFile.getAbsolutePath());
        UploadCheckResultModel resultModel = validateRecords(fileName, excelReader);
        String uploadId = resultModel.getUploadId();
        String importKey = String.format(RedisKey.IMPORT_QUICK_REPLY_JOB, orgId);
        if (resultModel.getErrors().isEmpty()) {
            synchronized (this) {
                String hasJob = redisTemplate.opsForValue().get(importKey);
                if (StringUtils.isNotBlank(hasJob)) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "当前已经有任务正在导入，请稍后再试");
                }
                redisTemplate.opsForValue().set(importKey, JSON.toJSONString(resultModel), 1, TimeUnit.HOURS);
                UploadProgressModel uploadProgressModel = new UploadProgressModel();
                uploadProgressModel.setTotal(excelReader.getTotalCount());
                redisTemplate.opsForValue().set(String.format(RedisKey.IMPORT_QUICK_REPLY_PROGRESS, orgId,
                        uploadId), JSON.toJSONString(uploadProgressModel), 1, TimeUnit.DAYS);
            }
            new Thread(() -> {
                importRecords(fileName, excelReader, uploadId, QuickReplyEntity.GLOBAL_Y, orgId, email);
                redisTemplate.delete(importKey);
            }).start();
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导入成功", resultModel);
        } else {
            redisTemplate.opsForValue().set(String.format(RedisKey.IMPORT_QUICK_REPLY_ERROR, orgId,
                    uploadId), JSON.toJSONString(resultModel.getErrors()), 1, TimeUnit.DAYS);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "导入失败", resultModel);
        }
    }

    @GetMapping("/text/import/{uploadId}/process")
    @ApiOperation(value = "查询导入进度")
    public ResponseModel getQuickReplyTextImportProcess(@PathVariable String uploadId) {
        String progressResult = redisTemplate.opsForValue().get(String.format(RedisKey.IMPORT_QUICK_REPLY_PROGRESS, getUser().getOrganizationId(), uploadId));
        if (StringUtils.isNotBlank(progressResult)) {
            JSONObject progressObj = JSON.parseObject(progressResult);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", progressObj);
        }
        return null;
    }

    @GetMapping("/text/import/{uploadId}/errorLog")
    @ApiOperation(value = "查询导入错误日志")
    public ResponseModel getQuickReplyTextImportErrorLog(@PathVariable String uploadId) {
        String errorResult = redisTemplate.opsForValue().get(String.format(RedisKey.IMPORT_QUICK_REPLY_ERROR, getUser().getOrganizationId(), uploadId));
        if (StringUtils.isNotBlank(errorResult)) {
            JSONArray errors = JSON.parseArray(errorResult);
            List<String> errorList = errors.stream().map(v -> {
                UploadErrorModel uploadErrorModel = JSON.toJavaObject((JSON) v, UploadErrorModel.class);
                return StringUtils.joinWith(",", uploadErrorModel.getSheetName() + "(第" + uploadErrorModel.getIndex() + "行)", uploadErrorModel.getContent(), uploadErrorModel.getError());
            }).collect(Collectors.toList());
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", errorList);
        }
        return null;
    }

    @GetMapping("/text/import/template")
    @ApiOperation(value = "快捷回复模板下载")
    public void getQuickReplyTextImportTemplate(HttpServletResponse response) throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("files/RuleTemplate_quickReply.xlsx");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("content-disposition",
                "attachment;filename=QuickReplyTemplate.xlsx");
        try (InputStream input = classPathResource.getInputStream(); OutputStream output = response.getOutputStream();) {
            IOUtils.copy(input, output);
        }
    }

    @GetMapping("/text/export")
    @ApiOperation(value = "个人文本快捷回复导出")
    public ResponseModel exportQuickReplyText() {
        UserDomain user = getUser();
        UserExtraField userExtraField = getUserExtraField();
        JSONObject json = new JSONObject();
        json.put("global", QuickReplyEntity.GLOBAL_N);
        json.put("nickName", userExtraField == null ? "" : userExtraField.getNickName());
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.QUICK_REPLY, TaskEntity.PORT_EXPORT, json, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }

    @GetMapping("/text/export/global")
    @ApiOperation(value = "公共文本快捷回复导出")
    public ResponseModel exportGlobalQuickReplyText() {
        UserDomain user = getUser();
        JSONObject json = new JSONObject();
        json.put("global", QuickReplyEntity.GLOBAL_Y);
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.QUICK_REPLY, TaskEntity.PORT_EXPORT, json, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }

    /**
     * 导入记录
     * @param fileName
     * @param excelReader
     * @param uploadId
     * @param openType
     * @param orgId
     * @param creator
     */
    private void importRecords(String fileName, ExcelReader excelReader, String uploadId,
                               Integer openType, Integer orgId, String creator) {
        UploadProgressModel uploadProgressModel = new UploadProgressModel();
        uploadProgressModel.setTotal(excelReader.getTotalCount());
        String processKey = String.format(RedisKey.IMPORT_QUICK_REPLY_PROGRESS, orgId, uploadId);
        String currentSheetName = "";
        int currentRowIndex = -1;
        int progress = 0;
        int repeatCnt = 0;
        try {
            //重新读取excel
            excelReader.reset();
            for (int sheetIndex = 0; sheetIndex < excelReader.getTotalSheet(); sheetIndex++) {
                excelReader.switchSheet(sheetIndex);
                String[] headers = excelReader.getHeaders();
                if (headers != null && headers.length > 0) {
                    int currentIndex = 1;
                    currentSheetName = excelReader.currentSheetName();
                    while (excelReader.hasNext()) {
                        currentRowIndex = ++currentIndex;
                        String[] columnValues = excelReader.getColumnValues();
                        boolean insertSuccess = insertQuickReply(orgId, creator, openType, columnValues);
                        if (!insertSuccess) {
                            uploadProgressModel.setRepeatCnt(++repeatCnt);
                        } else {
                            uploadProgressModel.setProgress(++progress);
                        }
                        uploadProgressModel.setPercent(BigDecimal.valueOf((uploadProgressModel.getProgress() + uploadProgressModel.getRepeatCnt()) * 100)
                                .divide(BigDecimal.valueOf(uploadProgressModel.getTotal()), 2, RoundingMode.HALF_UP).doubleValue());
                        LOGGER.info("total:{}, progress:{}, repeatCnt:{}, percent:{}",
                                uploadProgressModel.getTotal(), uploadProgressModel.getProgress(), uploadProgressModel.getRepeatCnt(), uploadProgressModel.getPercent());
                        redisTemplate.opsForValue().set(processKey, JSON.toJSONString(uploadProgressModel), 1, TimeUnit.DAYS);
                    }
                    uploadProgressModel.setPercent(100);
                    redisTemplate.opsForValue().set(processKey, JSON.toJSONString(uploadProgressModel), 1, TimeUnit.DAYS);
                }
            }
        } catch (Exception e) {
            if (openType == QuickReplyEntity.GLOBAL_N) {
                LOGGER.error("导入个人文本快捷回复失败,文件名:{},工作表:{},行数:{}", fileName, currentSheetName, currentRowIndex, e);
            } else if(openType == QuickReplyEntity.GLOBAL_Y){
                LOGGER.error("导入公共文本快捷回复失败,文件名:{},工作表:{},行数:{}", fileName, currentSheetName, currentRowIndex, e);
            }
            String errorMsg = "导入异常";
            uploadProgressModel.setErrMsg(errorMsg);
            redisTemplate.opsForValue().set(processKey, JSON.toJSONString(uploadProgressModel), 1, TimeUnit.DAYS);
            redisTemplate.opsForValue().set(String.format(RedisKey.IMPORT_QUICK_REPLY_ERROR, orgId, uploadId),
                    JSON.toJSONString(Collections.singletonList(new UploadErrorModel(UploadErrorModel.UNKNOWN_TYPE, currentSheetName,
                            currentRowIndex, null, errorMsg))), 1, TimeUnit.DAYS);
        } finally {
            excelReader.close();
        }
    }

    /**
     * 入库
     * @param orgId
     * @param creator
     * @param global
     * @param columnValues
     */
    private boolean insertQuickReply(Integer orgId, String creator, Integer global, String[] columnValues) {
        ReplyCategoryEntity parentEntity;
        //一级分类
        String category1 = columnValues[0];
        ReplyCategoryEntity sameNameExist;
        if (global == QuickReplyEntity.GLOBAL_N) {
            sameNameExist = replyCategoryRepo.findByOrgIdAndNameAndCreatorAndTypeAndGlobal(
                    orgId, category1, creator, ReplyCategoryEntity.TYPE_TEXT, global);
        } else {
            sameNameExist = replyCategoryRepo.findByOrgIdAndNameAndTypeAndGlobal(orgId, category1, ReplyCategoryEntity.TYPE_TEXT, global);
        }
        //兄弟不能重名
        if (sameNameExist != null && sameNameExist.getParentCategory() == null) {
            parentEntity = sameNameExist;
        } else {
            ReplyCategoryEntity entity = new ReplyCategoryEntity();
            entity.setName(category1);
            entity.setOrgId(orgId);
            entity.setGlobal(global);
            entity.setLevel(1);
            entity.setType(ReplyCategoryEntity.TYPE_TEXT);
            entity.setCreator(creator);
            entity.setCreateTime(new Date());
            entity.setUpdateTime(entity.getCreateTime());
            replyCategoryRepo.save(entity);
            parentEntity = entity;
        }
        //二级分类
        String category2 = columnValues[1];
        if(StringUtils.isNotBlank(category2)) {
            if (global == QuickReplyEntity.GLOBAL_N) {
                sameNameExist = replyCategoryRepo.findByOrgIdAndNameAndCreatorAndTypeAndGlobalAndParentCategory(orgId, category2, creator, ReplyCategoryEntity.TYPE_TEXT, global, parentEntity);
            } else {
                sameNameExist = replyCategoryRepo.findByOrgIdAndNameAndTypeAndGlobalAndParentCategory(orgId, category2, ReplyCategoryEntity.TYPE_TEXT, global, parentEntity);
            }
            //兄弟不能重名
            if (sameNameExist == null || (sameNameExist.getParentCategory() != null
                    && !Objects.equals(parentEntity.getId(), sameNameExist.getParentCategory().getId()))) {
                ReplyCategoryEntity entity = new ReplyCategoryEntity();
                entity.setName(category2);
                entity.setOrgId(orgId);
                entity.setParentCategory(parentEntity);
                entity.setGlobal(global);
                entity.setLevel(2);
                entity.setType(ReplyCategoryEntity.TYPE_TEXT);
                entity.setCreator(creator);
                entity.setCreateTime(new Date());
                entity.setUpdateTime(entity.getCreateTime());
                replyCategoryRepo.save(entity);
                parentEntity = entity;
            } else {
                parentEntity = sameNameExist;
            }
        }
        //内容
        String content = columnValues[2];
        QuickReplyEntity sameContent;
        if (global == QuickReplyEntity.GLOBAL_N) {
            sameContent = quickReplyRepo.findByOrgIdAndCategoryIdAndContentAndCreatorAndGlobal(orgId, parentEntity.getId(), content, creator, global);
        } else {
            sameContent = quickReplyRepo.findByOrgIdAndCategoryIdAndContentAndGlobal(orgId, parentEntity.getId(), content, global);
        }
        if (sameContent != null) {
            return false;
        }
        QuickReplyEntity entity = new QuickReplyEntity();
        entity.setOrgId(orgId);
        entity.setGlobal(global);
        entity.setAnswerType(QuickReplyEntity.ANSWER_TYPE_TEXT);
        entity.setCategory(parentEntity);
        entity.setContent(content);
        entity.setCreator(creator);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        quickReplyRepo.save(entity);
        return true;
    }

    /**
     * 上传内容校验
     * @param fileName
     * @param excelReader
     * @return
     */
    private UploadCheckResultModel validateRecords(String fileName, ExcelReader excelReader) {
        UploadCheckResultModel resultModel = new UploadCheckResultModel();
        resultModel.setUploadId(UUID.randomUUID().toString());
        resultModel.setFileName(fileName);
        resultModel.setTotalCount(excelReader.getTotalCount());
        for (int sheetIndex = 0; sheetIndex < excelReader.getTotalSheet(); sheetIndex++) {
            excelReader.switchSheet(sheetIndex);
            String[] headers = excelReader.getHeaders();
            String currentSheetName = excelReader.currentSheetName();
            if (headers != null && headers.length > 0) {
                if (!CommonUtil.excelHeaderCheck(new String[]{"一级分类", "二级分类", "内容"}, headers)) {
                    resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE, currentSheetName, 1, "", "excel列头不符合模板要求"));
                    return resultModel;
                }
                int currentIndex = 1;
                while (excelReader.hasNext()) {
                    ++currentIndex;
                    String[] columnValues = excelReader.getColumnValues();
                    int length = columnValues.length;
                    if (length == 0) {
                        continue;
                    }
                    String category1 = columnValues[0];
                    if (StringUtils.isBlank(category1)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE, currentSheetName, currentIndex, null, "一级分类不能为空"));
                    } else if (category1.length() > 20) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE, currentSheetName, currentIndex, category1, "一级分类不能超过20个字符"));
                    }
                    if (length < 3) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE, currentSheetName, currentIndex, null, "内容不能为空"));
                        continue;
                    }
                    String category2 = columnValues[1];
                    if (StringUtils.isNotBlank(category2) && category2.length() > 20) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE, currentSheetName, currentIndex, category2, "二级分类不能超过20个字符"));
                    }
                    String content = columnValues[2];
                    if (StringUtils.isBlank(content)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE, currentSheetName, currentIndex, null, "内容不能为空"));
                    } else if (content.length() > 2000) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE, currentSheetName, currentIndex, content, "内容不能超过2000个字符"));
                    }
                }
            } else {
                resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE, currentSheetName, 1, "", "excel列头不能为空"));
                return resultModel;
            }
        }
        excelReader.close();
        return resultModel;
    }

    

    /**
     *查询附件快捷回复
     * @param global
     * @param model
     */
    private void getAttachmentQuickReply(int global, QuickReplyAttachmentResponseModel model) {
        UserDomain user = getUser();
        String email = user.getEmail();
        Integer orgId = user.getOrganizationId();
        List<ReplyCategoryEntity> replyCategoryEntityList;
        if (ReplyCategoryEntity.GLOBAL_Y == global) {
            replyCategoryEntityList = replyCategoryRepo
                    .findByOrgIdAndTypeAndGlobalAndParentCategoryIsNullOrderByPositionAsc(orgId, ReplyCategoryEntity.TYPE_ATTACHMENT, global);
        } else {
            replyCategoryEntityList = replyCategoryRepo
                    .findByOrgIdAndCreatorAndTypeAndGlobalAndParentCategoryIsNullOrderByPositionAsc(orgId, email, ReplyCategoryEntity.TYPE_ATTACHMENT, global);
        }
        for (ReplyCategoryEntity categoryEntity : replyCategoryEntityList) {
            Integer categoryId = categoryEntity.getId();
            //一级-分类
            QuickReplyAttachmentDataModel dataModelLevel1 = new QuickReplyAttachmentDataModel();
            dataModelLevel1.setId(categoryId);
            dataModelLevel1.setContent(categoryEntity.getName());
            dataModelLevel1.setType(NODE_TYPE_CATEGORY_INT);
            dataModelLevel1.setNodeId(categoryId + "-" + NODE_TYPE_CATEGORY_INT);
            dataModelLevel1.setPosition(categoryEntity.getPosition());
            if (CollectionUtils.isNotEmpty(categoryEntity.getChildCategories())) {
                for (ReplyCategoryEntity childCategory : categoryEntity.getChildCategories()) {
                    //二级-分类
                    QuickReplyAttachmentDataModel dataModelLevel2 = new QuickReplyAttachmentDataModel();
                    dataModelLevel2.setId(childCategory.getId());
                    dataModelLevel2.setContent(childCategory.getName());
                    dataModelLevel2.setType(NODE_TYPE_CATEGORY_INT);
                    dataModelLevel2.setNodeId(childCategory.getId() + "-" + NODE_TYPE_CATEGORY_INT);
                    dataModelLevel2.setPosition(childCategory.getPosition());
                    List<QuickReplyEntity> quickReplyEntityList = complexQuickReplyRepo.findQuickReplies(orgId, childCategory.getId(), email, global);
                    for (QuickReplyEntity quickReplyEntity : quickReplyEntityList) {
                        //三级-附件
                        QuickReplyAttachmentDataModel dataModelLevel3 = new QuickReplyAttachmentDataModel();
                        dataModelLevel3.setId(quickReplyEntity.getId());
                        dataModelLevel3.setContent(quickReplyEntity.getRemark());
                        dataModelLevel3.setFileName(quickReplyEntity.getFileName());
                        dataModelLevel3.setFileType(quickReplyEntity.getFileType());
                        dataModelLevel3.setFileUrl(generateOssAttachmentUrl(quickReplyEntity.getFileUrl()));
                        String fileUrl = dataModelLevel3.getFileUrl();
                        dataModelLevel3.setFileId(fileUrl.substring(fileUrl.indexOf("/1/") + 3));
                        dataModelLevel3.setType(NODE_TYPE_CONTENT_INT);
                        dataModelLevel3.setNodeId(quickReplyEntity.getId() + "-" + NODE_TYPE_CONTENT_INT);
                        dataModelLevel3.setPosition(quickReplyEntity.getPosition());
                        dataModelLevel2.getChildren().add(dataModelLevel3);
                    }
                    //二级下的节点排序
                    dataModelLevel2.setChildren(dataModelLevel2.getChildren().stream()
                            .sorted(Comparator.comparing(QuickReplyAttachmentDataModel :: getPosition)).collect(Collectors.toList()));
                    dataModelLevel1.getChildren().add(dataModelLevel2);
                }
            }
            List<QuickReplyEntity> quickReplyEntityList = complexQuickReplyRepo.findQuickReplies(orgId, categoryId, email, global);
            for (QuickReplyEntity quickReplyEntity : quickReplyEntityList) {
                //二级-附件
                QuickReplyAttachmentDataModel dataModelLevel2 = new QuickReplyAttachmentDataModel();
                dataModelLevel2.setId(quickReplyEntity.getId());
                dataModelLevel2.setContent(quickReplyEntity.getRemark());
                dataModelLevel2.setFileName(quickReplyEntity.getFileName());
                dataModelLevel2.setFileType(quickReplyEntity.getFileType());
                dataModelLevel2.setFileUrl(generateOssAttachmentUrl(quickReplyEntity.getFileUrl()));
                String fileUrl = dataModelLevel2.getFileUrl();
                dataModelLevel2.setFileId(fileUrl.substring(fileUrl.indexOf("/1/") + 3));
                dataModelLevel2.setType(NODE_TYPE_CONTENT_INT);
                dataModelLevel2.setNodeId(quickReplyEntity.getId() + "-" + NODE_TYPE_CONTENT_INT);
                dataModelLevel2.setPosition(quickReplyEntity.getPosition());
                dataModelLevel1.getChildren().add(dataModelLevel2);
            }
            //一级下的节点排序
            dataModelLevel1.setChildren(dataModelLevel1.getChildren().stream()
                    .sorted(Comparator.comparing(QuickReplyAttachmentDataModel :: getPosition)).collect(Collectors.toList()));
            model.getList().add(dataModelLevel1);
        }
        //根节点排序
        model.setList(model.getList().stream()
                .sorted(Comparator.comparing(QuickReplyAttachmentDataModel :: getPosition)).collect(Collectors.toList()));
    }
    /**
     * 校验附件合法性
     * @param attachmentType
     * @param file
     * @return
     * @throws IOException
     */
    private ResponseModel checkAttachment(String attachmentType, MultipartFile file) throws IOException {
        if (StringUtils.isBlank(attachmentType) || file.isEmpty()) {
            return null;
        }
        if (!Objects.equals(attachmentType, QuickReplyEntity.FILE_TYPE_PICTURE)
                && !Objects.equals(attachmentType, QuickReplyEntity.FILE_TYPE_VIDEO)
                && !Objects.equals(attachmentType, QuickReplyEntity.FILE_TYPE_OTHER)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "不支持该类型");
        }
        if (file.getBytes().length == 0 || StringUtil.isEmpty(file.getOriginalFilename())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "文件上传异常");
        }
        if(Objects.equals(attachmentType, QuickReplyEntity.FILE_TYPE_PICTURE) &&
                !(StringUtils.endsWithIgnoreCase(file.getOriginalFilename(),".gif")
                        || StringUtils.endsWithIgnoreCase(file.getOriginalFilename(),".jpeg")
                        || StringUtils.endsWithIgnoreCase(file.getOriginalFilename(),".jpg")
                        || StringUtils.endsWithIgnoreCase(file.getOriginalFilename(),".png"))){
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "图片文件支持gif/jpeg/jpg/png格式");
        }
        if(Objects.equals(attachmentType, QuickReplyEntity.FILE_TYPE_PICTURE)
                || Objects.equals(attachmentType, QuickReplyEntity.FILE_TYPE_OTHER)) {
            if (file.getSize() > 1024 * 1024 * 20) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "附件大小不能超过20M");
            }
        }
        if(Objects.equals(attachmentType, QuickReplyEntity.FILE_TYPE_VIDEO)) {
            if (file.getSize() > 1024 * 1024 * 100) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "视频大小不能超过100M");
            }
        }
        return null;
    }

    /**
     * 附件相对路径
     * @param quickReplyEntity
     * @return
     */
    private String generateFileKeyPath(QuickReplyEntity quickReplyEntity) {
        if (quickReplyEntity.getFileType().equals(QuickReplyEntity.FILE_TYPE_PICTURE)) {
            return OssKeyPath.IMAGE_MATERIAL_PATH + "/" + UUID.randomUUID()
                    + "." + StringUtils.substringAfterLast(quickReplyEntity.getFileName(),".");
        }
        if (quickReplyEntity.getFileType().equals(QuickReplyEntity.FILE_TYPE_VIDEO)) {
            return OssKeyPath.VIDEO_MATERIAL_PATH + "/" + UUID.randomUUID()
                    + "." + StringUtils.substringAfterLast(quickReplyEntity.getFileName(),".");
        }
        if (quickReplyEntity.getFileType().equals(QuickReplyEntity.FILE_TYPE_OTHER)) {
            return OssKeyPath.OTHER_MATERIAL_PATH + "/" + UUID.randomUUID()
                    + "." + StringUtils.substringAfterLast(quickReplyEntity.getFileName(),".");
        }
        return "";
    }

    /**
     * 生成附件绝对路径
     * @param keyPath
     * @return
     */
    private String generateOssAttachmentUrl(String keyPath) {
        return appPropertyConfig.getFileServerUrl() + "/1/" + Base64.getEncoder().encodeToString(keyPath.getBytes());
    }


}
