package com.wolaidai.webot.cs.model.report;

import java.text.SimpleDateFormat;
import java.util.Date;

import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.report.AttendanceReportEntity;

public class AttendanceRowModel extends BaseModel {

    private String email;
    private String date;
    private String workNumber = "";
    private String nickName = "";
    private String firstLogin = "";
    private String lastLogout = "";
    private String totalLogin;
    private String totalOnline;
    private String totalBusy;
    private String totalRest;
    private String totalLeave;
    private String totalEat;
    private String totalStudy;
    private Integer sessionCount;
    private String sessionDuration;
    private String sessionCostAvg;
    private String firstAnswerAvg;
    private String answerAvg;
    private Integer evaluationCount;
    private String evaluationPercent;
    private Integer noEvaluationCount;
    private String noEvaluationPercent;
    private Integer starOne;
    private Integer starTwo;
    private Integer starThree;
    private Integer starFour;
    private Integer starFive;
    private String satisfactionPercent;
    private Date createTime;

    public AttendanceRowModel(AttendanceReportEntity s, SimpleDateFormat sdf) {
        this.email = s.getEmail();
        this.date = sdf.format(s.getDataTime());
        this.workNumber = s.getWorkNumber();
        this.nickName = s.getNickName();
        this.firstLogin = DateUtil.formatDateTime(s.getFirstLogin(), "");
        this.lastLogout = DateUtil.formatDateTime(s.getLastLogout(), "");
        this.totalLogin = DateUtil.formatSecondsTime(s.getTotalLogin(), "");
        this.totalOnline = DateUtil.formatSecondsTime(s.getTotalOnline(), "");
        this.totalBusy = DateUtil.formatSecondsTime(s.getTotalBusy(), "");
        this.totalRest = DateUtil.formatSecondsTime(s.getTotalRest(), "");
        this.totalLeave = DateUtil.formatSecondsTime(s.getTotalLeave(), "");
        this.totalEat = DateUtil.formatSecondsTime(s.getTotalEat(), "");
        this.totalStudy = DateUtil.formatSecondsTime(s.getTotalStudy(), "");
        this.sessionCount = s.getSessionCount();
        this.sessionDuration = DateUtil.formatSecondsTime(s.getSessionDuration(), "");
        this.sessionCostAvg = DateUtil.formatSecondsTime(s.getSessionCostAvg(), "");
        this.firstAnswerAvg = DateUtil.formatSecondsTime(s.getFirstAnswerAvg(), "");
        this.answerAvg = DateUtil.formatSecondsTime(s.getAnswerAvg(), "");
        this.evaluationCount = s.getEvaluationCount();
        this.evaluationPercent = s.getEvaluationPercent() + "%";
        this.noEvaluationCount = s.getNoEvaluationCount();
        this.noEvaluationPercent = s.getNoEvaluationPercent() + "%";
        this.starOne = s.getStarOne();
        this.starTwo = s.getStarTwo();
        this.starThree = s.getStarThree();
        this.starFour = s.getStarFour();
        this.starFive = s.getStarFive();
        this.satisfactionPercent = s.getSatisfactionPercent() + "%";
        this.createTime = s.getCreateTime();
    }

    public AttendanceRowModel(AttendanceMergeModel s) {
        this.email = s.getEmail();
        this.date = s.getDate();
        this.workNumber = s.getWorkNumber();
        this.nickName = s.getNickName();
        this.totalLogin = DateUtil.formatSecondsTime(s.getTotalLogin(), "");
        this.totalOnline = DateUtil.formatSecondsTime(s.getTotalOnline(), "");
        this.totalBusy = DateUtil.formatSecondsTime(s.getTotalBusy(), "");
        this.totalRest = DateUtil.formatSecondsTime(s.getTotalRest(), "");
        this.totalLeave = DateUtil.formatSecondsTime(s.getTotalLeave(), "");
        this.totalEat = DateUtil.formatSecondsTime(s.getTotalEat(), "");
        this.totalStudy = DateUtil.formatSecondsTime(s.getTotalStudy(), "");
        this.sessionCount = s.getSessionCount();
        this.sessionDuration = DateUtil.formatSecondsTime(s.getSessionDuration(), "");
        this.sessionCostAvg = DateUtil.formatSecondsTime(s.getSessionCostAvg(), "");
        this.firstAnswerAvg = DateUtil.formatSecondsTime(s.getFirstAnswerAvg(), "");
        this.answerAvg = DateUtil.formatSecondsTime(s.getAnswerAvg(), "");
        this.evaluationCount = s.getEvaluationCount();
        this.evaluationPercent = s.getEvaluationPercent() + "%";
        this.noEvaluationCount = s.getNoEvaluationCount();
        this.noEvaluationPercent = s.getNoEvaluationPercent() + "%";
        this.starOne = s.getStarOne();
        this.starTwo = s.getStarTwo();
        this.starThree = s.getStarThree();
        this.starFour = s.getStarFour();
        this.starFive = s.getStarFive();
        this.satisfactionPercent = s.getSatisfactionPercent() + "%";
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getFirstLogin() {
        return firstLogin;
    }

    public void setFirstLogin(String firstLogin) {
        this.firstLogin = firstLogin;
    }

    public String getLastLogout() {
        return lastLogout;
    }

    public void setLastLogout(String lastLogout) {
        this.lastLogout = lastLogout;
    }

    public String getTotalLogin() {
        return totalLogin;
    }

    public void setTotalLogin(String totalLogin) {
        this.totalLogin = totalLogin;
    }

    public String getTotalOnline() {
        return totalOnline;
    }

    public void setTotalOnline(String totalOnline) {
        this.totalOnline = totalOnline;
    }

    public String getTotalBusy() {
        return totalBusy;
    }

    public void setTotalBusy(String totalBusy) {
        this.totalBusy = totalBusy;
    }

    public String getTotalRest() {
        return totalRest;
    }

    public void setTotalRest(String totalRest) {
        this.totalRest = totalRest;
    }

    public String getTotalLeave() {
        return totalLeave;
    }

    public void setTotalLeave(String totalLeave) {
        this.totalLeave = totalLeave;
    }

    public String getTotalEat() {
        return totalEat;
    }

    public void setTotalEat(String totalEat) {
        this.totalEat = totalEat;
    }

    public String getTotalStudy() {
        return totalStudy;
    }

    public void setTotalStudy(String totalStudy) {
        this.totalStudy = totalStudy;
    }

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public String getSessionDuration() {
        return sessionDuration;
    }

    public void setSessionDuration(String sessionDuration) {
        this.sessionDuration = sessionDuration;
    }

    public String getSessionCostAvg() {
        return sessionCostAvg;
    }

    public void setSessionCostAvg(String sessionCostAvg) {
        this.sessionCostAvg = sessionCostAvg;
    }

    public String getFirstAnswerAvg() {
        return firstAnswerAvg;
    }

    public void setFirstAnswerAvg(String firstAnswerAvg) {
        this.firstAnswerAvg = firstAnswerAvg;
    }

    public String getAnswerAvg() {
        return answerAvg;
    }

    public void setAnswerAvg(String answerAvg) {
        this.answerAvg = answerAvg;
    }

    public Integer getEvaluationCount() {
        return evaluationCount;
    }

    public void setEvaluationCount(Integer evaluationCount) {
        this.evaluationCount = evaluationCount;
    }

    public String getEvaluationPercent() {
        return evaluationPercent;
    }

    public void setEvaluationPercent(String evaluationPercent) {
        this.evaluationPercent = evaluationPercent;
    }

    public Integer getNoEvaluationCount() {
        return noEvaluationCount;
    }

    public void setNoEvaluationCount(Integer noEvaluationCount) {
        this.noEvaluationCount = noEvaluationCount;
    }

    public String getNoEvaluationPercent() {
        return noEvaluationPercent;
    }

    public void setNoEvaluationPercent(String noEvaluationPercent) {
        this.noEvaluationPercent = noEvaluationPercent;
    }

    public Integer getStarOne() {
        return starOne;
    }

    public void setStarOne(Integer starOne) {
        this.starOne = starOne;
    }

    public Integer getStarTwo() {
        return starTwo;
    }

    public void setStarTwo(Integer starTwo) {
        this.starTwo = starTwo;
    }

    public Integer getStarThree() {
        return starThree;
    }

    public void setStarThree(Integer starThree) {
        this.starThree = starThree;
    }

    public Integer getStarFour() {
        return starFour;
    }

    public void setStarFour(Integer starFour) {
        this.starFour = starFour;
    }

    public Integer getStarFive() {
        return starFive;
    }

    public void setStarFive(Integer starFive) {
        this.starFive = starFive;
    }

    public String getSatisfactionPercent() {
        return satisfactionPercent;
    }

    public void setSatisfactionPercent(String satisfactionPercent) {
        this.satisfactionPercent = satisfactionPercent;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
