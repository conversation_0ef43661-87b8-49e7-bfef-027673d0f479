package com.wolaidai.webot.cs.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.service.AttendanceReportService;
import com.wolaidai.webot.cs.service.QuickReplyService;
import com.wolaidai.webot.cs.service.SatisfactionReportService;
import com.wolaidai.webot.cs.service.ServiceDataReportService;
import com.wolaidai.webot.cs.service.ServiceSummaryService;
import com.wolaidai.webot.cs.service.SessionDetailReportService;
import com.wolaidai.webot.cs.service.SessionListService;
import com.wolaidai.webot.cs.service.SessionReportService;
import com.wolaidai.webot.cs.service.StateReportService;
import com.wolaidai.webot.cs.service.TaskService;
import com.wolaidai.webot.cs.service.TaskUnit;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.repo.TaskRepo;

@Service
public class TaskServiceImpl implements TaskService {
    final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private TaskRepo taskRepo;
    @Autowired
    private ApplicationContext applicationContext;

    private final HashMap<String, Class<?>> taskUnitMap = new HashMap<String, Class<?>>() {
        private static final long serialVersionUID = 1L;
        {
            put(TaskEntity.ATTENDANCE_REPORT, AttendanceReportService.class);
            put(TaskEntity.STATE_REPORT, StateReportService.class);
            put(TaskEntity.SESSION_DETAIL_REPORT, SessionDetailReportService.class);
            put(TaskEntity.SERVICE_DATA_REPORT, ServiceDataReportService.class);
            put(TaskEntity.SESSION_REPORT, SessionReportService.class);
            put(TaskEntity.SATISFACTION_REPORT, SatisfactionReportService.class);
            put(TaskEntity.SATISFACTION_LABEL_REPORT, SatisfactionReportService.class);
            put(TaskEntity.SESSION_RECORD, SessionListService.class);
            put(TaskEntity.CHAT_DETAIL, SessionListService.class);
            put(TaskEntity.QUICK_REPLY, QuickReplyService.class);
            put(TaskEntity.BUSINESS_TYPE, ServiceSummaryService.class);
            put(TaskEntity.SERVICE_SUMMARY_REPORT, ServiceSummaryService.class);
        }
    };

    private final ExecutorService executorService = Executors.newFixedThreadPool(8);

    @Override
    public String submitTask(Integer orgId, String type, Integer port, Object params, String creator) {
        if (null != taskRepo.findByOrgIdAndStatusAndPortAndTypeAndCreator(orgId, TaskEntity.EXECUTING_STATUS, port, type, creator)) {
            return "已经有相同类型的任务正在执行中，请稍后再试";
        }
        TaskEntity task = new TaskEntity();
        task.setOrgId(orgId);
        task.setExtraParams((JSONObject) JSON.toJSON(params));
        task.setType(type);
        task.setPort(port);
        task.setCreateTime(new Date());
        task.setUpdateTime(task.getCreateTime());
        task.setCreator(creator);
        task.setStatus(TaskEntity.EXECUTING_STATUS);
        taskRepo.save(task);
        executeTask(task);
        return null;
    }

    @Override
    public void executeTask(TaskEntity task) {
        executorService.submit(() -> {
            try {
                TaskUnit unit = (TaskUnit) applicationContext.getBean(taskUnitMap.get(task.getType()));
                unit.run(task);
            } catch (Exception e) {
                LOGGER.error("export report task {} error:", task.getId(), e);
                CommonUtil.saveTaskProgress(task, -1);
            }
        });
    }
}
