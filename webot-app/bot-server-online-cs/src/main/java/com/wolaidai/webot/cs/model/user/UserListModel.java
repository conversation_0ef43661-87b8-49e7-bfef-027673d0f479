package com.wolaidai.webot.cs.model.user;

import java.util.ArrayList;
import java.util.List;

import com.wolaidai.webot.cs.model.BaseModel;

public class UserListModel extends BaseModel {

    private Integer status;

    private List<UserRowModel> list = new ArrayList<>();

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<UserRowModel> getList() {
        return list;
    }

    public void setList(List<UserRowModel> list) {
        this.list = list;
    }

}
