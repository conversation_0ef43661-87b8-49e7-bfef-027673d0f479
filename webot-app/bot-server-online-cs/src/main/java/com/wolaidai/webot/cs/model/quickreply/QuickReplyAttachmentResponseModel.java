package com.wolaidai.webot.cs.model.quickreply;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "快捷回复附件列表")
public class QuickReplyAttachmentResponseModel extends BaseModel {

    private List<QuickReplyAttachmentDataModel> list = new ArrayList<>();

    public List<QuickReplyAttachmentDataModel> getList() {
        return list;
    }

    public void setList(List<QuickReplyAttachmentDataModel> list) {
        this.list = list;
    }
}
