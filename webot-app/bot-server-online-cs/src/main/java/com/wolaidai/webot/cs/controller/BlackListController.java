package com.wolaidai.webot.cs.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.blacklist.BlackListCreateModel;
import com.wolaidai.webot.cs.model.blacklist.BlackListRowModel;
import com.wolaidai.webot.cs.model.blacklist.BlackListSearchModel;
import com.wolaidai.webot.cs.model.blacklist.BlackListUpdateModel;
import com.wolaidai.webot.cs.model.blacklist.BlackListWorkbenchCreateModel;
import com.wolaidai.webot.cs.model.sessionlist.personal.PersonalSessionSearchModel;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import com.wolaidai.webot.cs.service.SessionListService;
import com.wolaidai.webot.data.mysql.entity.blacklist.BlackListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.repo.BlackListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;
import com.wolaidai.webot.security.domain.UserDomain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "黑名单接口")
@RestController
@RequestMapping("/blackList")
public class BlackListController extends BaseController {

    @Autowired
    private BlackListRepo blackListRepo;
    @Autowired
    private SessionListService sessionListService;
    @Autowired
    private SessionListRepo sessionListRepo;
    @Autowired
    private UserRepo userRepo;
    @Autowired
    private CustomerInfoEncService customerInfoEncService;

    @PostMapping
    @ApiOperation(value = "工作台拉黑")
    public ResponseModel addBlackList(@Valid @RequestBody BlackListWorkbenchCreateModel model) {
        if (StringUtils.isBlank(model.getRemark())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "申请拉黑描述说明不能为空");
        }
        SessionListEntity session = null;
        if (StringUtils.isBlank(model.getSessionKey()) || null == (session = sessionListRepo.findBySessionKey(model.getSessionKey()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        JSONObject detail = session.getCustomerDetail();
        String phone = customerInfoEncService.getDecryptMobile(detail);
        UserDomain user = getUser();
        Integer existsId = blackListRepo.findByOrgIdAndPhoneAndClientId(user.getOrganizationId(), phone, session.getClientId());
        if (null != existsId) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "当前用户已在黑名单中，请勿重复操作");
        }
        BlackListEntity black = new BlackListEntity();
        black.setOrgId(user.getOrganizationId());
        black.setName(session.getCustomerName());
        black.setPhone(phone);
        black.setClientId(session.getClientId());
        black.setClientType(session.getClientTypeId());
        black.setRemark(model.getRemark());
        black.setCreator(user.getEmail());
        black.setStatus(BlackListEntity.STATUS_UNCHECK);
        black.setCreateTime(new Date());
        blackListRepo.save(black);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/manage")
    @ApiOperation(value = "管理员快捷拉黑")
    public ResponseModel createBlackList(@Valid @RequestBody BlackListCreateModel model) {
        SessionListEntity session = null;
        if (null == model.getSessionId() || null == (session = sessionListRepo.findByOrgIdAndId(getUser().getOrganizationId(), model.getSessionId()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        JSONObject detail = session.getCustomerDetail();
        String phone = customerInfoEncService.getDecryptMobile(detail);
        UserDomain user = getUser();
        Integer existsId = blackListRepo.findByOrgIdAndPhoneAndClientId(user.getOrganizationId(), phone, session.getClientId());
        if (null != existsId) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "当前用户已在黑名单中，请勿重复操作");
        }
        Date now = new Date();
        BlackListEntity black = new BlackListEntity();
        black.setOrgId(user.getOrganizationId());
        black.setName(session.getCustomerName());
        black.setPhone(phone);
        black.setClientId(session.getClientId());
        black.setClientType(session.getClientTypeId());
        black.setReviewRemark(model.getRemark());
        black.setCreator(user.getEmail());
        black.setReviewCreator(user.getEmail());
        black.setStatus(BlackListEntity.STATUS_PASS);
        black.setCreateTime(now);
        black.setUpdateTime(now);
        if (null != model.getValidDays() && model.getValidDays() > 0) {
            black.setValidDays(model.getValidDays());
            black.setExpireTime(DateUtils.addDays(now, model.getValidDays()));
        }
        blackListRepo.save(black);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @GetMapping("/workBench")
    @ApiOperation(value = "工作台搜索黑名单", response = BlackListSearchModel.class)
    public ResponseModel searchWorkBenchBlackList(BlackListSearchModel model) {
        searchBlackList(model, true);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/todo")
    @ApiOperation(value = "搜索待审批黑名单", response = BlackListSearchModel.class)
    public ResponseModel searchTodoBlackList(BlackListSearchModel model) {
        model.setStatus(Arrays.asList(BlackListEntity.STATUS_UNCHECK));
        searchBlackList(model, true);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/did")
    @ApiOperation(value = "搜索已审批黑名单", response = BlackListSearchModel.class)
    public ResponseModel searchBlackList(BlackListSearchModel model) {
        if (null != model.getStatus()) {
            model.getStatus().remove(BlackListEntity.STATUS_UNCHECK);
        }
        if (null == model.getStatus() || model.getStatus().size() == 0) {
            model.setStatus(Arrays.asList(BlackListEntity.STATUS_PASS, BlackListEntity.STATUS_REJECT, BlackListEntity.STATUS_CANCEL));
        }
        searchBlackList(model, true);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    private void searchBlackList(BlackListSearchModel model, boolean markPhone) {
        UserDomain user = getUser();
        Object[] orderBy = new String[] { "createTime", "desc" };
        if (StringUtils.isNotBlank(model.getSort()) && StringUtils.isNotBlank(model.getDirection())) {
            orderBy = new String[] { model.getSort(), model.getDirection() };
        }
        String[][] like = null;
        ArrayList<String[]> likeList = new ArrayList<>();
        if (StringUtils.isNotBlank(model.getName())) {
            likeList.add(new String[] { model.getName(), "name", "and" });
        }
        if (likeList.size() > 0) {
            like = likeList.toArray(new String[likeList.size()][]);
        }
        Object[][] equal = null;
        ArrayList<Object[]> equalList = new ArrayList<>();
        ArrayList<Object[]> statusList = new ArrayList<>();
        for (Integer s : model.getStatus()) {
            if (Objects.equals(s, BlackListEntity.STATUS_UNCHECK) || Objects.equals(s, BlackListEntity.STATUS_PASS) || Objects.equals(s, BlackListEntity.STATUS_REJECT) || Objects.equals(s, BlackListEntity.STATUS_CANCEL)) {
                statusList.add(new Object[] { "status", s, "or" });
            }
        }
        if (statusList.size() > 0) {
            equalList.addAll(statusList);
        }
        if (StringUtils.isNotBlank(model.getPhone())) {
            equalList.add(new Object[] { "phone", model.getPhone(), "and" });
        }
        equal = equalList.size() > 0 ? equalList.toArray(new Object[equalList.size()][]) : null;
        Page<BlackListEntity> page = blackListRepo.find(user.getOrganizationId(), like, equal, "creator", model.getCreators(), model.getPageNumber(), model.getPageSize(), "createTime", model.getBeginDate(), model.getEndDate(), orderBy);
        if (page.hasContent()) {
            HashSet<String> emails = new HashSet<>();
            for (BlackListEntity b : page.getContent()) {
                emails.add(b.getCreator());
                emails.add(b.getCancelCreator());
            }
            emails.remove(null);
            Map<String, UserInfo> m = new HashMap<>();
            if (emails.size() > 0) {
                List<UserInfo> list = userRepo.find(user.getOrganizationId(), appPropertyConfig.getProductId(), emails);
                m = list.stream().collect(Collectors.toMap(UserInfo::getEmail, Function.identity()));
            }
            final Map<String, UserInfo> map = m;
            model.setList(page.getContent().stream().map(i -> new BlackListRowModel(i, markPhone, map.get(i.getCreator()), map.get(i.getCancelCreator()))).collect(Collectors.toList()));
        }
        model.setTotal(page.getTotalElements());
    }

    @PutMapping("/manage")
    @ApiOperation(value = "审核黑名单")
    public ResponseModel checkBlackList(@Valid @RequestBody BlackListUpdateModel model) {
        if (!Objects.equals(model.getStatus(), BlackListEntity.STATUS_REJECT) && !Objects.equals(model.getStatus(), BlackListEntity.STATUS_PASS)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "状态类型不正确");
        }
        List<BlackListEntity> list = null;
        if (null == model.getIds() || model.getIds().size() == 0 || (list = blackListRepo.findByOrgIdAndIdInAndStatus(getUser().getOrganizationId(), model.getIds(), BlackListEntity.STATUS_UNCHECK)).size() != model.getIds().size()) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到相应的待审批黑名单记录");
        }
        Integer validDays = model.getValidDays();
        Date now = new Date();
        Date expireDate = null;
        if (null != validDays && validDays > 0) {
            expireDate = DateUtils.addDays(now, validDays);
        }
        String email = getUser().getEmail();
        for (BlackListEntity b : list) {
            b.setStatus(model.getStatus());
            b.setUpdateTime(now);
            b.setValidDays(validDays);
            b.setExpireTime(expireDate);
            b.setReviewCreator(email);
            b.setReviewRemark(model.getRemark());
        }
        blackListRepo.saveAll(list);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/cancel/{ids}")
    @ApiOperation(value = "批量取消黑名单")
    public ResponseModel cancelBlackList(@PathVariable Integer[] ids) {
        UserDomain user = getUser();
        HashSet<Integer> idSet = new HashSet<>(Arrays.asList(ids));
        List<BlackListEntity> list = blackListRepo.findByOrgIdAndIdIn(getUser().getOrganizationId(), idSet);
        if (list.size() != idSet.size()) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到黑名单");
        }
        String email = user.getEmail();
        Date now = new Date();
        for (BlackListEntity b : list) {
            b.setStatus(BlackListEntity.STATUS_CANCEL);
            b.setCancelCreator(email);
            b.setCancelTime(now);
        }
        blackListRepo.saveAll(list);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @DeleteMapping("/{ids}")
    @ApiOperation(value = "批量删除黑名单")
    public ResponseModel deleteBlackList(@PathVariable Integer[] ids) {
        List<BlackListEntity> list = blackListRepo.findByOrgIdAndIdIn(getUser().getOrganizationId(), Arrays.asList(ids));
        if (list.size() != ids.length) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到黑名单");
        }
        blackListRepo.deleteAll(list);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @GetMapping("/{id}/sessionList")
    @ApiOperation(value = "查询个人会话列表", response = PersonalSessionSearchModel.class)
    public ResponseModel searchPersonalSession(@PathVariable Integer id, PersonalSessionSearchModel model) {
        BlackListEntity b = blackListRepo.findByOrgIdAndId(getUser().getOrganizationId(), id);
        if (null == b) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到黑名单");
        }
        model.setList(sessionListService.searchPersonalSession(getUser().getOrganizationId(), model.getKey(), b.getPhone(), b.getClientId(), model.getStartTime(), model.getEndTime()));
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }
}
