package com.wolaidai.webot.cs.model.servicesummary;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class BusinessTypeTreeModel extends BaseModel {

    private List<BusinessUnitRowModel> list = new ArrayList<>();

    public List<BusinessUnitRowModel> getList() {
        return list;
    }

    public void setList(List<BusinessUnitRowModel> list) {
        this.list = list;
    }

    public static class BusinessUnitRowModel {
        private String summaryKey;
        private String name;
        private List<BusinessTypeRowModel> children = new ArrayList<>();

        public String getSummaryKey() {
            return summaryKey;
        }

        public void setSummaryKey(String summaryKey) {
            this.summaryKey = summaryKey;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<BusinessTypeRowModel> getChildren() {
            return children;
        }

        public void setChildren(List<BusinessTypeRowModel> children) {
            this.children = children;
        }

        public BusinessUnitRowModel() {
        }

        public BusinessUnitRowModel(BusinessUnitEntity unitEntity, List<BusinessTypeEntity> entities) {
            this.summaryKey = "u_" + unitEntity.getId();
            this.name = unitEntity.getName();
            for (BusinessTypeEntity entity : entities) {
                if (BusinessTypeEntity.INACTIVE_STATUS.equals(entity.getStatus())) {
                    continue;
                }
                this.children.add(new BusinessTypeRowModel(entity));
            }
        }

    }

    public static class BusinessTypeRowModel {
        private String summaryKey;
        private String name;
        private List<BusinessTypeRowModel> children = new ArrayList<>();

        public BusinessTypeRowModel() {
        }

        public BusinessTypeRowModel(BusinessTypeEntity entity) {
            this.summaryKey = "t_" + entity.getId();
            this.name = entity.getTitle();
            if (CollectionUtils.isNotEmpty(entity.getChildTypes())) {
                for (BusinessTypeEntity childType : entity.getChildTypes()) {
                    if (BusinessTypeEntity.INACTIVE_STATUS.equals(childType.getStatus())) {
                        continue;
                    }
                    this.children.add(new BusinessTypeRowModel(childType));
                }
            }
        }

        public String getSummaryKey() {
            return summaryKey;
        }

        public void setSummaryKey(String summaryKey) {
            this.summaryKey = summaryKey;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<BusinessTypeRowModel> getChildren() {
            return children;
        }

        public void setChildren(List<BusinessTypeRowModel> children) {
            this.children = children;
        }
    }
}
