package com.wolaidai.webot.cs.model.replycategory;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "快捷回复分类")
public class CategoryAddModel extends BaseModel {

    @NotBlank(message = "{NotBlank.category.title}")
    @ApiModelProperty(value = "分类标题")
    private String title;
    @ApiModelProperty(value = "父节点ID")
    private Integer parentCategoryId;
    @NotNull(message = "{NotNull.category.type}")
    @ApiModelProperty(value = "分类类型:0-文本,1-附件")
    private Integer type;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getParentCategoryId() {
        return parentCategoryId;
    }

    public void setParentCategoryId(Integer parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
