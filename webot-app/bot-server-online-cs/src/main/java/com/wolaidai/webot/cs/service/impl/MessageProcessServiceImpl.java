package com.wolaidai.webot.cs.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.entity.Event;
import com.wolaidai.webot.cs.entity.EventProcessResult;
import com.wolaidai.webot.cs.service.ApiService;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import com.wolaidai.webot.cs.service.MessageProcessService;
import com.wolaidai.webot.cs.service.QueueListService;
import com.wolaidai.webot.cs.service.SessionStatisticsService;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.repo.*;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

@Service
public class MessageProcessServiceImpl implements MessageProcessService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private QueueListRepo queueListRepo;

    @Autowired
    private SessionListRepo sessionListRepo;

    @Autowired
    private SessionTransferListRepo sessionTransferListRepo;

    @Autowired
    private UserStateRepo userStateRepo;

    @Autowired
    private UserStateHistoryRepo userStateHistoryRepo;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private QueueListService queueListService;
    
    @Autowired
    private ApiService apiService;

    @Autowired
    private SessionStatisticsService sessionStatisticsService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private CustomerInfoEncService customerInfoEncService;


    @Override
    public EventProcessResult process(Event event) {
        EventProcessResult eventProcessResult = new EventProcessResult();
        String eventKey = event.getEventKey();
        try {
            JSONObject content = event.getContent();
            if (Event.INQUEUE_KEY.equals(eventKey)) {
                StopWatch stopWatch = new StopWatch();
                String clientId = content.getString("clientId");
                Integer clientType = content.getInteger("clientType");
                Integer botId = content.getInteger("botId");
                RLock lock = redissonClient.getLock(RedisKey.LOCK_KEY + "clientId:" + clientId);
                boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
                if (res) {
                    stopWatch.start("task1");
                    //该clientId存在在线会话
                    String onlineSessionClientId = sessionListRepo.findClientIdByOnlineStatusAndCreateTime(event.getOrgId(), clientId, DateUtils.truncate(new Date(), Calendar.DATE));
                    if(onlineSessionClientId!=null){
                        LOGGER.error("已在会话中，eventKey:{}，content:{}", eventKey, content.toJSONString());
                        eventProcessResult.setSuccess(false);
                        eventProcessResult.setErrorMsg("已在会话中");
                        return eventProcessResult;
                    }
                    stopWatch.stop();

                    stopWatch.start("task2");
                    boolean clientIdExist = false;
                    //检查排队队列是否已存在相同clientId
                    for (String queueKey : Arrays.asList(RedisKey.QUEUE_VIP, RedisKey.QUEUE_NORMAL)) {
                        queueKey = String.format(queueKey, event.getOrgId());
                        Long size = redisTemplate.opsForZSet().zCard(queueKey);
                        if (size > 0) {
                            try (Cursor<ZSetOperations.TypedTuple<String>> cursor = redisTemplate.opsForZSet().scan(queueKey, ScanOptions.scanOptions().match("*\"clientId\":\"" + clientId + "\"*").count(size).build())) {
                                if (cursor.hasNext()) {
                                    clientIdExist = true;
                                    break;
                                }
                            }
                        }
                    }
                    stopWatch.stop();

                    if (!clientIdExist) {
                        QueueListEntity queueList = new QueueListEntity();
                        queueList.setClientId(clientId);
                        queueList.setClientTypeId(clientType);
                        queueList.setOrigin(content.getString("origin"));
                        queueList.setBotId(botId);
                        queueList.setGcid(content.getString("gcid"));
                        queueList.setGcTime(content.getDate("gcTime"));
                        queueList.setBusinessId(content.getInteger("businessId"));
                        queueList.setBusinessName(content.getString("businessName"));

                        String customerName = content.getString("customerName");
                        Integer customerType = content.getInteger("customerType");
                        if(customerType==null){
                            customerType = 0;
                        }
                        JSONObject customerDetail = content.getJSONObject("customerDetail");
                        if (null == customerDetail) {
                            customerDetail = new JSONObject();
                        }

                        String ua = customerDetail.getString("ua");
                        if (StringUtils.isNotBlank(ua)) {
                            if (StringUtils.containsAnyIgnoreCase(ua, "iphone", "ipad")) {
                                customerDetail.put("platform", "iOS");
                            } else if (StringUtils.containsAnyIgnoreCase(ua, "android")) {
                                customerDetail.put("platform", "Android");
                            } else if (StringUtils.containsAnyIgnoreCase(ua, "windows")) {
                                customerDetail.put("platform", "Windows");
                            } else if (StringUtils.containsAnyIgnoreCase(ua, "macintosh")) {
                                customerDetail.put("platform", "macOS");
                            }
                        }
                        stopWatch.start("task3");
                        JSONArray customers = customerDetail.getJSONArray("customers");
                        String account = content.getString("account");
                        if(StringUtils.isNotBlank(account)&&CollectionUtils.isEmpty(customers)){
                            JSONObject customerInfo = apiService.getCustomerInfoByMobile(account);
                            if(customerInfo!=null&&Objects.equals(customerInfo.getInteger("code"),0)){
                                customers = customerInfo.getJSONArray("result");
                                if (CollectionUtils.isNotEmpty(customers)) {
                                    customerName = customers.getJSONObject(0).getString("name");
                                    customerType = "VIP".equals(customers.getJSONObject(0).getString("isVip")) ? 1 : 0;
                                }
                            }
                            if(CollectionUtils.isEmpty(customers)){
                                customers = new JSONArray();
                                customers.add(new JSONObject().fluentPut("mobile", account));
                            }
                        }
                        //处理用户敏感信息
                        if(customers != null && customers.size() > 0) {
                            customerInfoEncService.encryptCustomerInfo(customers);
                            customerDetail.put("customers", customers);
                        }
                        if (StringUtils.isBlank(customerName)) {
                            String guestIdKey = String.format(RedisKey.CS_GUEST_ID, event.getOrgId());
                            customerName = "访客" + redisTemplate.opsForValue().increment(guestIdKey);
                            redisTemplate.expireAt(guestIdKey, DateUtils.truncate(DateUtils.addDays(new Date(), 1), Calendar.DATE));
                        }
                        stopWatch.stop();
                        stopWatch.start("task4");
                        JSONObject ipInfo = customerDetail.getJSONObject("ipInfo");
                        if(ipInfo == null){
                            String ip = customerDetail.getString("ip");
                            if(StringUtils.isNotBlank(ip)){
                                ipInfo = apiService.getIpInfo(ip);
                                if(ipInfo!=null&&Objects.equals(ipInfo.getInteger("ret"), 0)){
                                    customerDetail.put("ipInfo", ipInfo.getJSONObject("address"));
                                }
                            }
                        }
                        stopWatch.stop();
                        stopWatch.start("task5");
                        queueList.setCustomerName(customerName);
                        queueList.setCustomerType(customerType);
                        queueList.setCustomerDetail(customerDetail);
                        queueList.setLastMsg(content.getString("lastMsg"));
                        queueList.setLastMsgTime(content.getDate("lastMsgTime"));
                        queueList.setOrgId(event.getOrgId());
                        queueList.setCreateTime(new Date());
                        queueList.setUpdateTime(queueList.getCreateTime());
                        queueListRepo.save(queueList);
                        String serviceUserConfig = configService.read(queueList.getOrgId(), String.format(RedisKey.SERVICEUSER_CONFIG, queueList.getOrgId()), null, CommonConfigEntity.TYPE_SERVICEUSER_CONFIG);
                        JSONObject serviceUserConfigJson = new JSONObject();
                        if (serviceUserConfig != null) {
                            serviceUserConfigJson.putAll(JSON.parseObject(serviceUserConfig));
                        }
                        JSONObject queueJson = new JSONObject().fluentPut("queueId", queueList.getId()).fluentPut("customerType",queueList.getCustomerType()).fluentPut("clientId", queueList.getClientId()).fluentPut("clientType",queueList.getClientTypeId()).fluentPut("botId", queueList.getBotId());
                        String redisQueueKey = null;
                        if (serviceUserConfigJson.getBooleanValue("vipPriority")) {
                            if (Objects.equals(queueList.getCustomerType(), 1)) {
                                redisQueueKey = RedisKey.QUEUE_VIP;
                            }
                        }
                        if (redisQueueKey == null) {
                            redisQueueKey = RedisKey.QUEUE_NORMAL;
                        }
                        redisTemplate.opsForZSet().add(String.format(redisQueueKey, event.getOrgId()), queueJson.toJSONString(), System.currentTimeMillis());
                        redisTemplate.opsForList().rightPush(RedisKey.EVENT_SERVER_KEY, JSONObject.toJSONString(new Event(UUID.randomUUID().toString(), Event.QUEUING_KEY, event.getOrgId(), queueJson, new Date())));
                        //检查队列
                        queueListService.checkQueueList();
                        stopWatch.stop();
                        LOGGER.info("INQUEUE cost:"+stopWatch.prettyPrint());
                    } else{
                        LOGGER.error("已在排队中，eventKey:{}，content:{}", eventKey, content.toJSONString());
                        eventProcessResult.setSuccess(false);
                        eventProcessResult.setErrorMsg("已在排队中");
                        return eventProcessResult;
                    }
                } else {
                    LOGGER.error("重复提交，eventKey:{}，content:{}", eventKey, content.toJSONString());
                }
            } else if (Event.DEQUEUE_KEY.equals(eventKey)) {
                Integer queueId = content.getInteger("queueId");
                RLock lock = redissonClient.getLock(RedisKey.LOCK_KEY+"queueId:"+queueId);
                try {
                    boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
                    if (res) {
                        queueListRepo.updateStatusAndWaitSecondById(queueId, QueueListEntity.DEQUEUE_STATUS, event.getEventTime());
                        //清除排队缓存
                        Stream.of(RedisKey.QUEUE_VIP, RedisKey.QUEUE_NORMAL).forEach(v -> {
                            String key = String.format(v, event.getOrgId());
                            Set<String> queueDatas = redisTemplate.opsForZSet().range(key, 0, -1);
                            for (String queueData : queueDatas) {
                                JSONObject queueJson = JSON.parseObject(queueData);
                                if (Objects.equals(queueId, queueJson.getInteger("queueId"))) {
                                    redisTemplate.opsForZSet().remove(key, queueData);
                                    break;
                                }
                            }
                        });
                    } else {
                        LOGGER.error("重复提交，eventKey:{}，content:{}", eventKey, content.toJSONString());
                    }
                }finally {
                    if(lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else if (Event.BEGIN_SESSION_KEY.equals(eventKey)) {

            } else if (Event.END_SESSION_KEY.equals(eventKey)) {
                String sessionKey = event.getContent().getString("sessionKey");
                SessionListEntity sessionList = sessionListRepo.findBySessionKey(sessionKey);
                if(sessionList!=null && DateUtils.isSameDay(new Date(),sessionList.getCreateTime()) && Objects.equals(sessionList.getStatus(),SessionListEntity.STATUS_OFFLINE)){
                    String currentReceptionKey = String.format(RedisKey.USER_CURRENT_RECEPTION, sessionList.getOrgId());
                    redisTemplate.opsForHash().increment(currentReceptionKey, sessionList.getLastServiceUser(),-1);
                    redisTemplate.expireAt(currentReceptionKey,DateUtils.truncate(DateUtils.addDays(new Date(), 1), Calendar.DATE));
                    //异步保存会话统计数据
                    sessionStatisticsService.saveSessionStatistics(sessionList);
                }
                //检查队列
                queueListService.checkQueueList();
            } else if (Event.TRANS_SUCCESS_KEY.equals(eventKey)) {
                Integer transId = event.getContent().getInteger("transId");
                Optional<SessionTransferListEntity> sessionTransferList = sessionTransferListRepo.findById(transId);
                if(sessionTransferList.isPresent()){
                    SessionTransferListEntity sessionTransferListData = sessionTransferList.get();
                    if(Objects.equals(sessionTransferListData.getStatus(),SessionTransferListEntity.STATUS_START)) {
                        String currentReceptionKey = String.format(RedisKey.USER_CURRENT_RECEPTION, event.getOrgId());
                        redisTemplate.opsForHash().increment(currentReceptionKey, sessionTransferListData.getFromServiceUser(), -1);
                        redisTemplate.opsForHash().increment(currentReceptionKey, sessionTransferListData.getToServiceUser(), 1);
                        redisTemplate.expireAt(currentReceptionKey, DateUtils.truncate(DateUtils.addDays(new Date(), 1), Calendar.DATE));
                        sessionTransferListRepo.updateStatusById(event.getContent().getInteger("transId"), event.getOrgId(), SessionTransferListEntity.STATUS_OK, event.getEventTime());
                    }
                }
                //检查队列
                queueListService.checkQueueList();
            } else if (Event.TRANS_REJECT_KEY.equals(eventKey)) {
                sessionTransferListRepo.updateStatusById(event.getContent().getInteger("transId"), event.getOrgId(), SessionTransferListEntity.STATUS_FAIL, event.getEventTime());
            } else if (Event.TRANS_TIMEOUT_KEY.equals(eventKey)) {
                sessionTransferListRepo.updateStatusById(event.getContent().getInteger("transId"), event.getOrgId(), SessionTransferListEntity.STATUS_TIMEOUT, event.getEventTime());
            } else if (Event.SERVICE_OFFLINE_KEY.equals(eventKey)) {
                String email = event.getContent().getString("email");
                RLock lock = redissonClient.getLock(RedisKey.LOCK_KEY + "user_state:"+email);
                try {
                    boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
                    if (res) {
                        UserStateEntity userState = userStateRepo.findByOrgIdAndEmail(event.getOrgId(), email);
                        if (userState != null) {
                            if (DateUtils.isSameDay(userState.getCreateTime(), new Date())&&!Objects.equals(userState.getState(),UserStateEntity.STATE_OFFLINE)) {
                                userState.setState(UserStateEntity.STATE_OFFLINE);
                                userState.setUpdateTime(new Date());
                                userStateRepo.save(userState);
                                UserStateHistoryEntity history = new UserStateHistoryEntity();
                                history.setCreateTime(new Date());
                                history.setEmail(userState.getEmail());
                                history.setOrgId(userState.getOrgId());
                                history.setState(userState.getState());
                                history.setUserType(userState.getUserType());
                                userStateHistoryRepo.save(history);
                            }
                        }
                        redisTemplate.opsForZSet().remove(String.format(RedisKey.USER_ONLINE_LIST, event.getOrgId()), email);
                        List<UserStateEntity> states = userStateRepo.findByOrgIdAndUserTypeAndCreateTimeGreaterThanEqual(event.getOrgId(), UserStateEntity.TYPE_NORMAL, DateUtils.truncate(new Date(), Calendar.DATE));
                        int totalQueueSize = states.size();
                        int currentQueueSize = (int) states.stream().filter(i -> Objects.equals(i.getState(),UserStateEntity.STATE_ONLINE)).count();
                        redisTemplate.opsForList().rightPush(RedisKey.EVENT_SERVER_KEY, JSONObject.toJSONString(new Event(UUID.randomUUID().toString(),Event.USER_STATE_KEY,event.getOrgId(),
                                new JSONObject().fluentPut("email",email).fluentPut("state",UserStateEntity.STATE_OFFLINE).fluentPut("currentQueueSize",currentQueueSize).fluentPut("totalQueueSize",totalQueueSize),new Date())));
                    }else {
                        LOGGER.error("重复提交，eventKey:{}，content:{}", eventKey, content.toJSONString());
                    }
                }finally {
                    if(lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                LOGGER.error("不支持该eventKey:{}", eventKey);
                eventProcessResult.setSuccess(false);
                eventProcessResult.setErrorMsg("eventKey错误");
                return eventProcessResult;
            }
        }catch (Exception e){
            LOGGER.error("事件处理失败,eventKey:{}", eventKey,e);
            eventProcessResult.setSuccess(false);
            eventProcessResult.setErrorMsg(e.getMessage());
        }
        return eventProcessResult;
    }
}
