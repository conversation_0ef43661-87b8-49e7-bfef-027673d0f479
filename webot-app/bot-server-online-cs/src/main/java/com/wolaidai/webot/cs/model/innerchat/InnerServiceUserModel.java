package com.wolaidai.webot.cs.model.innerchat;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;

public class InnerServiceUserModel extends BaseModel {

    private Integer userId;
    private String name;
    private String nickName;
    private String email;
    private String workNumber;
    private Integer status;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public InnerServiceUserModel() {
    }

    public InnerServiceUserModel(UserStateEntity entity) {
        this.userId = entity.getId();
        this.name = entity.getName();
        this.nickName = entity.getNickName();
        this.email = entity.getEmail();
        this.workNumber = entity.getWorkNumber();
        this.status = entity.getState();
    }
}
