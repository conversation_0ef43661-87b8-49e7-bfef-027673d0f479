package com.wolaidai.webot.cs.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.model.quickreply.QuickReplyTextDataModel;
import com.wolaidai.webot.cs.model.quickreply.QuickReplyTextResponseModel;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.mysql.entity.chat.QuickReplyEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ReplyCategoryEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.repo.ComplexQuickReplyRepo;
import com.wolaidai.webot.data.mysql.repo.ReplyCategoryRepo;

@Service
public class QuickReplyService implements TaskUnit {
    @Autowired
    protected AppPropertyConfig appPropertyConfig;

    @Autowired
    private ReplyCategoryRepo replyCategoryRepo;

    @Autowired
    private ComplexQuickReplyRepo complexQuickReplyRepo;

    public boolean run(TaskEntity task) throws IOException {
        Integer orgId = task.getOrgId();
        JSONObject params = task.getExtraParams();
        Integer global = params.getInteger("global");
        if (Objects.equals(global, ReplyCategoryEntity.GLOBAL_N)) {
            task.setName(params.getString("nickName") + "_快捷回复_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + ".xlsx");
        } else if (Objects.equals(global, ReplyCategoryEntity.GLOBAL_Y)) {
            task.setName("公共_快捷回复_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + ".xlsx");
        }
        CommonUtil.saveTaskProgress(task, 0);
        XSSFWorkbook sheets = new XSSFWorkbook();
        XSSFSheet sheet = sheets.createSheet();
        sheet.setDefaultColumnWidth(15);
        XSSFRow headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("一级分类");
        headerRow.createCell(1).setCellValue("二级分类");
        headerRow.createCell(2).setCellValue("内容");
        QuickReplyTextResponseModel model = new QuickReplyTextResponseModel();
        getTextQuickReply(orgId,task.getCreator(),global, model);
        CommonUtil.saveTaskProgress(task, 75);
        for (QuickReplyTextDataModel dataModel : model.getList()) {
            List<String> fullPath = new ArrayList<String>();
            dataModel.getFullContent(fullPath);
            for (String content : fullPath) {
                int currentCellIndex = 0;
                XSSFRow row = sheet.createRow(sheet.getLastRowNum() + 1);
                String[] fullContents = content.split(",");
                row.createCell(currentCellIndex++).setCellValue(fullContents[0]);
                row.createCell(currentCellIndex++).setCellValue(fullContents[1]);
                row.createCell(currentCellIndex).setCellValue(fullContents[2]);
            }
        }
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(sheets, appPropertyConfig.getOssBucketName(), task);
        return true;
    }
    /**
     * 查询快捷回复
     * @param global
     * @param model
     */
    public void getTextQuickReply(Integer orgId,String creator, Integer global, QuickReplyTextResponseModel model) {
        List<ReplyCategoryEntity> replyCategoryEntityList;
        if (ReplyCategoryEntity.GLOBAL_Y == global) {
            replyCategoryEntityList = replyCategoryRepo
                    .findByOrgIdAndTypeAndGlobalAndParentCategoryIsNullOrderByPositionAsc(orgId, ReplyCategoryEntity.TYPE_TEXT, global);
        } else {
            replyCategoryEntityList = replyCategoryRepo
                    .findByOrgIdAndCreatorAndTypeAndGlobalAndParentCategoryIsNullOrderByPositionAsc(orgId, creator, ReplyCategoryEntity.TYPE_TEXT, global);
        }
        for (ReplyCategoryEntity categoryEntity : replyCategoryEntityList) {
            //一级-分类
            QuickReplyTextDataModel dataModelLevel1 = new QuickReplyTextDataModel(categoryEntity);
            if (CollectionUtils.isNotEmpty(categoryEntity.getChildCategories())) {
                for (ReplyCategoryEntity childCategory : categoryEntity.getChildCategories()) {
                    //二级-分类
                    QuickReplyTextDataModel dataModelLevel2 = new QuickReplyTextDataModel(childCategory);
                    List<QuickReplyEntity> quickReplyEntityList = complexQuickReplyRepo.findQuickReplies(orgId, childCategory.getId(), creator, global);
                    for (QuickReplyEntity quickReplyEntity : quickReplyEntityList) {
                        //三级-文本
                        QuickReplyTextDataModel dataModelLevel3 = new QuickReplyTextDataModel(quickReplyEntity);
                        dataModelLevel2.getChildren().add(dataModelLevel3);
                    }
                    //二级下的节点排序
                    dataModelLevel2.setChildren(dataModelLevel2.getChildren().stream()
                            .sorted(Comparator.comparing(QuickReplyTextDataModel :: getPosition)).collect(Collectors.toList()));
                    dataModelLevel1.getChildren().add(dataModelLevel2);
                }
            }
            List<QuickReplyEntity> quickReplyEntityList = complexQuickReplyRepo.findQuickReplies(orgId, categoryEntity.getId(), creator, global);
            for (QuickReplyEntity quickReplyEntity : quickReplyEntityList) {
                //二级-文本
                QuickReplyTextDataModel dataModelLevel2 = new QuickReplyTextDataModel(quickReplyEntity);
                dataModelLevel1.getChildren().add(dataModelLevel2);
            }
            //一级下的节点排序
            dataModelLevel1.setChildren(dataModelLevel1.getChildren().stream()
                    .sorted(Comparator.comparing(QuickReplyTextDataModel :: getPosition)).collect(Collectors.toList()));
            model.getList().add(dataModelLevel1);
        }
        //根节点排序
        model.setList(model.getList().stream()
                .sorted(Comparator.comparing(QuickReplyTextDataModel :: getPosition)).collect(Collectors.toList()));
    }
}
