package com.wolaidai.webot.cs.controller.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.ExcelReader;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.exception.ValidationException;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.servicesummary.*;
import com.wolaidai.webot.cs.model.upload.UploadCheckResultModel;
import com.wolaidai.webot.cs.model.upload.UploadErrorModel;
import com.wolaidai.webot.cs.model.upload.UploadProgressModel;
import com.wolaidai.webot.cs.service.BusinessTypeTransService;
import com.wolaidai.webot.cs.service.TaskService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.enums.AuditAction;
import com.wolaidai.webot.data.mysql.enums.AuditModule;
import com.wolaidai.webot.data.mysql.repo.ServiceSummaryRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessTypeRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessUnitRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import com.wolaidai.webot.security.domain.UserDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Api(tags = "服务小结设置接口")
@RestController
@RequestMapping("/serviceSummary/config")
public class ServiceSummaryConfigController extends BaseController {

    @Autowired
    private BusinessUnitRepo businessUnitRepo;

    @Autowired
    private BusinessTypeRepo businessTypeRepo;

    @Autowired
    private ServiceSummaryRepo serviceSummaryRepo;

    @Autowired
    private BusinessTypeTransService businessTypeTransService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private TaskService taskService;

    @GetMapping("/businessUnit")
    @ApiOperation(value = "查询业务单元",response = BusinessUnitSearchModel.class)
    public ResponseModel getBusinessUnit(BusinessUnitSearchModel model) {
        List<BusinessUnitEntity> unitEntityList = businessUnitRepo.findByOrgIdOrderByPositionAsc(getUser().getOrganizationId());
        for (BusinessUnitEntity unitEntity : unitEntityList) {
            //状态过滤
            if (model.getStatus() != null && !Objects.equals(model.getStatus(), unitEntity.getStatus())) {
                continue;
            }
            model.getList().add(new BusinessUnitSearchModel.BusinessUnitRowModel(unitEntity));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @PostMapping("/businessUnit")
    @ApiOperation(value = "添加业务单元")
    public ResponseModel addBusinessUnit(@Valid @RequestBody BusinessUnitRequestModel model) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        BusinessUnitEntity sameName = businessUnitRepo.findByOrgIdAndName(orgId, model.getName());
        if (sameName != null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名业务单元已存在");
        }
        BusinessUnitEntity entity = new BusinessUnitEntity();
        entity.setName(model.getName());
        entity.setOrgId(orgId);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        businessUnitRepo.save(entity);
        auditLog(AuditAction.CREATE, AuditModule.SERVICESUMMARY_CONFIG, null, "添加业务单元");
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/businessUnit/{id}")
    @ApiOperation(value = "更新业务单元")
    public ResponseModel updateBusinessUnit(@PathVariable Integer id, @Valid @RequestBody BusinessUnitRequestModel model) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        BusinessUnitEntity entity = businessUnitRepo.findByIdAndOrgId(id, orgId);
        if (entity == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "业务单元不存在");
        }
        BusinessUnitEntity sameName = businessUnitRepo.findByOrgIdAndName(orgId, model.getName());
        if (sameName != null && !sameName.getId().equals(id)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名业务单元已存在");
        }
        entity.setName(model.getName());
        entity.setUpdateTime(new Date());
        businessUnitRepo.save(entity);
        auditLog(AuditAction.UPDATE, AuditModule.SERVICESUMMARY_CONFIG, null, "更新业务单元");
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @DeleteMapping("/businessUnit/{id}")
    @ApiOperation(value = "删除业务单元")
    public ResponseModel deleteBusinessUnit(@PathVariable Integer id) {
        Integer orgId = getUser().getOrganizationId();
        BusinessUnitEntity entity = businessUnitRepo.findByIdAndOrgId(id, orgId);
        if (entity == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "业务单元不存在");
        }
        //校验是否被引用
        long ref = serviceSummaryRepo.countByOrgIdAndUnitId(orgId, id);
        if (ref > 0) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "该业务单元有关联数据，无法删除");
        }
        //删除业务单元下属类型
        businessTypeRepo.deleteByOrgIdAndUnit(orgId, id);
        //删除业务单元
        businessUnitRepo.delete(entity);
        auditLog(AuditAction.DELETE, AuditModule.SERVICESUMMARY_CONFIG, null, "删除业务单元");
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/businessUnit/sort")
    @ApiOperation(value = "业务单元排序")
    public ResponseModel sortBusinessUnit(@RequestBody Map<Integer, Integer> sortParam) {
        List<BusinessUnitEntity> unitEntityList = businessUnitRepo.findByOrgId(getUser().getOrganizationId());
        for (BusinessUnitEntity unitEntity : unitEntityList) {
            unitEntity.setPosition(sortParam.get(unitEntity.getId()));
            unitEntity.setUpdateTime(new Date());
            businessUnitRepo.save(unitEntity);
        }
        auditLog(AuditAction.UPDATE, AuditModule.SERVICESUMMARY_CONFIG, null, "排序业务单元");
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @GetMapping("/businessUnit/{id}/type")
    @ApiOperation(value = "查询业务类型",response = BusinessTypeSearchModel.class)
    public ResponseModel getBusinessType(@PathVariable Integer id, BusinessTypeSearchModel searchModel) {
        Integer orgId = getUser().getOrganizationId();
        Integer modelStatus = searchModel.getStatus();
        BusinessUnitEntity entity = businessUnitRepo.findByIdAndOrgId(id, orgId);
        if (entity == null || (modelStatus != null && !Objects.equals(modelStatus, entity.getStatus()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "业务单元不存在");
        }
        List<BusinessTypeEntity> typeEntityList = businessTypeRepo.findByOrgIdAndUnitIdAndParentTypeIsNull(orgId,
                id, Sort.by(new Sort.Order(Sort.Direction.ASC, "position"), new Sort.Order(Sort.Direction.ASC, "id")));
        for (BusinessTypeEntity typeEntity : typeEntityList) {
            if (modelStatus != null && !Objects.equals(modelStatus, typeEntity.getStatus())) {
                continue;
            }
            searchModel.getList().add(new BusinessTypeSearchModel.BusinessTypeRowModel(modelStatus, typeEntity));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", searchModel);
    }

    @GetMapping("/businessUnit/type")
    @ApiOperation(value = "查询业务类型(树形)",response = BusinessTypeTreeModel.class)
    public ResponseModel getBusinessTypeTree(BusinessTypeTreeModel searchModel) {
        Integer orgId = getUser().getOrganizationId();
        List<BusinessUnitEntity> unitEntityList = businessUnitRepo.findByOrgIdOrderByPositionAsc(orgId);
        for (BusinessUnitEntity unitEntity : unitEntityList) {
            if (BusinessUnitEntity.INACTIVE_STATUS.equals(unitEntity.getStatus())) {
                continue;
            }
            List<BusinessTypeEntity> typeEntityList = businessTypeRepo.findByOrgIdAndUnitIdAndParentTypeIsNull(orgId,
                    unitEntity.getId(), Sort.by(new Sort.Order(Sort.Direction.ASC, "position"), new Sort.Order(Sort.Direction.ASC, "id")));
            searchModel.getList().add(new BusinessTypeTreeModel.BusinessUnitRowModel(unitEntity, typeEntityList));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", searchModel);
    }

    @PostMapping("/businessUnit/{id}/type")
    @ApiOperation(value = "保存业务类型")
    public ResponseModel setBusinessType(@PathVariable Integer id, @RequestBody BusinessTypeRequestModel model) {
        if (Objects.nonNull(model.getStatus()) && !boolTypeValue.contains(model.getStatus())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "状态值非法");
        }
        auditLog(AuditAction.UPDATE, AuditModule.SERVICESUMMARY_CONFIG, null, "设置业务类型");
        try {
            businessTypeTransService.saveBusinessType(getUser().getOrganizationId(), id, model);
        } catch (ValidationException e) {
            LOGGER.error("保存业务类型失败",e);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, e.getMessage());
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/businessUnit/import")
    @ApiOperation(value = "导入业务类型")
    public ResponseModel importBusinessType(MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (!(StringUtils.endsWithIgnoreCase(fileName, ".xlsx")
                || StringUtils.endsWithIgnoreCase(fileName, ".xls"))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "只支持xlsx或xls格式文件");
        }
        if (file.getSize() > 30 * 1024 * 1024) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "文件大小不能超过30M");
        }
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String suffix = StringUtils.substringAfterLast(fileName, ".");
        File tmpFile = File.createTempFile("importBusinessType_userId_" + user.getId(), "." + suffix);
        file.transferTo(tmpFile);
        ExcelReader excelReader = new ExcelReader(tmpFile.getAbsolutePath());
        UploadCheckResultModel resultModel = validateRecords(fileName, excelReader);
        String uploadId = resultModel.getUploadId();
        String importKey = String.format(RedisKey.IMPORT_BUSINESS_TYPE_JOB, orgId);
        if (resultModel.getErrors().isEmpty()) {
            synchronized (this) {
                String hasJob = redisTemplate.opsForValue().get(importKey);
                if (StringUtils.isNotBlank(hasJob)) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "当前已经有任务正在导入，请稍后再试");
                }
                redisTemplate.opsForValue().set(importKey, JSON.toJSONString(resultModel), 1, TimeUnit.HOURS);
                UploadProgressModel uploadProgressModel = new UploadProgressModel();
                uploadProgressModel.setTotal(excelReader.getTotalCount());
                redisTemplate.opsForValue().set(String.format(RedisKey.IMPORT_BUSINESS_TYPE_PROGRESS, orgId,
                        uploadId), JSON.toJSONString(uploadProgressModel), 1, TimeUnit.DAYS);
            }
            new Thread(() -> {
                importRecords(fileName, uploadId, orgId, excelReader);
                redisTemplate.delete(importKey);
            }).start();
            auditLog(AuditAction.IMPORT, AuditModule.SERVICESUMMARY_CONFIG, null, "导入业务类型");
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导入成功", resultModel);
        } else {
            redisTemplate.opsForValue().set(String.format(RedisKey.IMPORT_BUSINESS_TYPE_ERROR, orgId,
                    uploadId), JSON.toJSONString(resultModel.getErrors()), 1, TimeUnit.DAYS);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "导入失败", resultModel);
        }
    }

    @GetMapping("/businessUnit/export")
    @ApiOperation(value = "导出业务类型")
    public ResponseModel exportBusinessType() {
        UserDomain user = getUser();
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.BUSINESS_TYPE, TaskEntity.PORT_EXPORT, null, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }

    @GetMapping("/businessUnit/import/{uploadId}/process")
    @ApiOperation(value = "查询导入进度")
    public ResponseModel getBusinessTypeImportProcess(@PathVariable String uploadId) {
        String progressResult = redisTemplate.opsForValue().get(String.format(RedisKey.IMPORT_BUSINESS_TYPE_PROGRESS, getUser().getOrganizationId(), uploadId));
        if (StringUtils.isNotBlank(progressResult)) {
            JSONObject progressObj = JSON.parseObject(progressResult);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", progressObj);
        }
        return null;
    }

    @GetMapping("/businessUnit/import/{uploadId}/errorLog")
    @ApiOperation(value = "查询导入错误日志")
    public ResponseModel getBusinessTypeImportErrorLog(@PathVariable String uploadId) {
        String errorResult = redisTemplate.opsForValue().get(String.format(RedisKey.IMPORT_BUSINESS_TYPE_ERROR, getUser().getOrganizationId(), uploadId));
        if (StringUtils.isNotBlank(errorResult)) {
            JSONArray errors = JSON.parseArray(errorResult);
            List<String> errorList = errors.stream().map(v -> {
                UploadErrorModel uploadErrorModel = JSON.toJavaObject((JSON) v, UploadErrorModel.class);
                return StringUtils.joinWith(",", uploadErrorModel.getSheetName() + "(第" + uploadErrorModel.getIndex() + "行)", uploadErrorModel.getContent(), uploadErrorModel.getError());
            }).collect(Collectors.toList());
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", errorList);
        }
        return null;
    }

    @GetMapping("/businessUnit/import/template")
    @ApiOperation(value = "业务类型模板下载")
    public void getBusinessTypeImportTemplate(HttpServletResponse response) throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("files/RuleTemplate_businessType.xlsx");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("content-disposition",
                "attachment;filename=BusinessTypeTemplate.xlsx");
        try (InputStream input = classPathResource.getInputStream(); OutputStream output = response.getOutputStream()) {
                IOUtils.copy(input, output);
        }
    }

    /**
     * 上传内容校验
     * @param fileName
     * @param excelReader
     * @return
     */
    private UploadCheckResultModel validateRecords(String fileName, ExcelReader excelReader) {
        UploadCheckResultModel resultModel = new UploadCheckResultModel();
        resultModel.setUploadId(UUID.randomUUID().toString());
        resultModel.setFileName(fileName);
        resultModel.setTotalCount(excelReader.getTotalCount());
        for (int sheetIndex = 0; sheetIndex < excelReader.getTotalSheet(); sheetIndex++) {
            excelReader.switchSheet(sheetIndex);
            String[] headers = excelReader.getHeaders();
            String currentSheetName = excelReader.currentSheetName();
            if (headers != null && headers.length > 0) {
                if (!CommonUtil.excelHeaderCheck(new String[]{"业务单元", "二级分类", "三级分类", "四级分类", "五级分类"}, headers)) {
                    resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE,
                            currentSheetName, 1, "", "excel列头不符合模板要求"));
                    return resultModel;
                }
                int currentIndex = 1;
                while (excelReader.hasNext()) {
                    ++currentIndex;
                    String[] columnValues = excelReader.getColumnValues();
                    int length = columnValues.length;
                    if (length == 0) {
                        continue;
                    }
                    List<Integer> nullIndex = new ArrayList<>();
                    List<Integer> notNullIndex = new ArrayList<>();
                    for (int i = 0; i < length; i++) {
                        if (StringUtils.isBlank(columnValues[i])) {
                            nullIndex.add(i);
                        } else {
                            notNullIndex.add(i);
                        }
                    }
                    //都不为空
                    if (CollectionUtils.isEmpty(nullIndex)) {
                        continue;
                    }
                    //不空最大下标<空最小下标
                    if (Collections.max(notNullIndex) < Collections.min(nullIndex)) {
                        continue;
                    }
                    if (nullIndex.contains(0)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "业务单元不能为空"));
                        continue;
                    }
                    if (nullIndex.contains(1)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "二级分类不能为空"));
                    }
                    if (nullIndex.contains(2)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "三级分类不能为空"));
                    }
                    if (nullIndex.contains(3)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "四级分类不能为空"));
                    }
                    if (nullIndex.contains(4)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "五级分类不能为空"));
                    }
                }
            } else {
                resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE,
                        currentSheetName, 1, "", "excel列头不能为空"));
                return resultModel;
            }
        }
        excelReader.close();
        return resultModel;
    }

    /**
     * 导入记录
     * @param fileName
     * @param uploadId
     * @param orgId
     * @param excelReader
     */
    private void importRecords(String fileName, String uploadId, Integer orgId, ExcelReader excelReader) {
        UploadProgressModel uploadProgressModel = new UploadProgressModel();
        uploadProgressModel.setTotal(excelReader.getTotalCount());
        String processKey = String.format(RedisKey.IMPORT_BUSINESS_TYPE_PROGRESS, orgId, uploadId);
        String currentSheetName = "";
        int currentRowIndex = -1;
        int progress = 0;
        int repeatCnt = 0;
        try {
            //重新读取excel
            excelReader.reset();
            for (int sheetIndex = 0; sheetIndex < excelReader.getTotalSheet(); sheetIndex++) {
                excelReader.switchSheet(sheetIndex);
                String[] headers = excelReader.getHeaders();
                if (headers != null && headers.length > 0) {
                    int currentIndex = 1;
                    currentSheetName = excelReader.currentSheetName();
                    while (excelReader.hasNext()) {
                        currentRowIndex = ++currentIndex;
                        String[] columnValues = excelReader.getColumnValues();
                        boolean failInsert = insertQuickReply(orgId, columnValues, --currentRowIndex);
                        if (failInsert) {
                            uploadProgressModel.setRepeatCnt(++repeatCnt);
                        } else {
                            uploadProgressModel.setProgress(++progress);
                        }
                        uploadProgressModel.setPercent(BigDecimal.valueOf((uploadProgressModel.getProgress() + uploadProgressModel.getRepeatCnt()) * 100)
                                .divide(BigDecimal.valueOf(uploadProgressModel.getTotal()), 2, RoundingMode.HALF_UP).doubleValue());
                        LOGGER.info("total:{}, progress:{}, repeatCnt:{}, percent:{}",
                                uploadProgressModel.getTotal(), uploadProgressModel.getProgress(), uploadProgressModel.getRepeatCnt(), uploadProgressModel.getPercent());
                        redisTemplate.opsForValue().set(processKey, JSON.toJSONString(uploadProgressModel), 1, TimeUnit.DAYS);
                    }
                    uploadProgressModel.setPercent(100);
                    redisTemplate.opsForValue().set(processKey, JSON.toJSONString(uploadProgressModel), 1, TimeUnit.DAYS);
                }
            }
        } catch (Exception e) {
            LOGGER.error("导入业务类型失败,文件名:{},工作表:{},行数:{}", fileName, currentSheetName, currentRowIndex, e);
            String errorMsg = "导入异常";
            uploadProgressModel.setErrMsg(errorMsg);
            redisTemplate.opsForValue().set(processKey, JSON.toJSONString(uploadProgressModel), 1, TimeUnit.DAYS);
            redisTemplate.opsForValue().set(String.format(RedisKey.IMPORT_BUSINESS_TYPE_PROGRESS, orgId, uploadId),
                    JSON.toJSONString(Collections.singletonList(new UploadErrorModel(UploadErrorModel.UNKNOWN_TYPE, currentSheetName,
                            currentRowIndex, null, errorMsg))), 1, TimeUnit.DAYS);
        } finally {
            excelReader.close();
        }
    }

    /**
     * 入库
     * @param orgId
     * @param columnValues
     */
    private boolean insertQuickReply(Integer orgId, String[] columnValues, int currentRowIndex) {
        boolean repeatInsert = true;
        //业务单元名称
        String unitName = columnValues[0];
        BusinessUnitEntity sameNameUnit = businessUnitRepo.findByOrgIdAndName(orgId, unitName);
        if (sameNameUnit == null) {
            sameNameUnit = new BusinessUnitEntity();
            sameNameUnit.setName(unitName);
            sameNameUnit.setOrgId(orgId);
            sameNameUnit.setCreateTime(new Date());
            sameNameUnit.setUpdateTime(sameNameUnit.getCreateTime());
            businessUnitRepo.save(sameNameUnit);
            repeatInsert = false;
        }
        //二级
        BusinessTypeEntity typeEntity = null;
        int length = columnValues.length;
        if (length > 1) {
            String typeName2 = columnValues[1];
            if (StringUtils.isNotBlank(typeName2)) {
                typeEntity = businessTypeRepo.findByOrgIdAndUnitIdAndTitleAndParentTypeIsNull(orgId, sameNameUnit.getId(), typeName2);
                if (typeEntity == null) {
                    typeEntity = new BusinessTypeEntity();
                    typeEntity.setLevel(1);
                    typeEntity.setTitle(typeName2);
                    typeEntity.setOrgId(orgId);
                    typeEntity.setUnit(sameNameUnit);
                    typeEntity.setPosition(currentRowIndex);
                    typeEntity.setUpdateTime(new Date());
                    typeEntity.setCreateTime(typeEntity.getUpdateTime());
                    businessTypeRepo.save(typeEntity);
                    repeatInsert = false;
                }
            }
        }
        //三级
        BusinessTypeEntity parentEntity = typeEntity;
        if (length > 2) {
            String typeName3 = columnValues[2];
            if (StringUtils.isNotBlank(typeName3)) {
                typeEntity = businessTypeRepo.findByOrgIdAndParentTypeIdAndTitle(orgId, parentEntity.getId(), typeName3);
                if (typeEntity == null) {
                    typeEntity = new BusinessTypeEntity();
                    typeEntity.setLevel(2);
                    typeEntity.setTitle(typeName3);
                    typeEntity.setParentType(parentEntity);
                    typeEntity.setOrgId(orgId);
                    typeEntity.setUnit(sameNameUnit);
                    typeEntity.setPosition(currentRowIndex);
                    typeEntity.setUpdateTime(new Date());
                    typeEntity.setCreateTime(typeEntity.getUpdateTime());
                    businessTypeRepo.save(typeEntity);
                    repeatInsert = false;
                }
            }
        }
        if (length > 3) {
            //四级
            parentEntity = typeEntity;
            String typeName4 = columnValues[3];
            if (StringUtils.isNotBlank(typeName4)) {
                typeEntity = businessTypeRepo.findByOrgIdAndParentTypeIdAndTitle(orgId, parentEntity.getId(), typeName4);
                if (typeEntity == null) {
                    typeEntity = new BusinessTypeEntity();
                    typeEntity.setLevel(3);
                    typeEntity.setTitle(typeName4);
                    typeEntity.setParentType(parentEntity);
                    typeEntity.setOrgId(orgId);
                    typeEntity.setUnit(sameNameUnit);
                    typeEntity.setPosition(currentRowIndex);
                    typeEntity.setUpdateTime(new Date());
                    typeEntity.setCreateTime(typeEntity.getUpdateTime());
                    businessTypeRepo.save(typeEntity);
                    repeatInsert = false;
                }
            }
        }
        if (length > 4) {
            //五级
            parentEntity = typeEntity;
            String typeName5 = columnValues[4];
            if (StringUtils.isNotBlank(typeName5)) {
                typeEntity = businessTypeRepo.findByOrgIdAndParentTypeIdAndTitle(orgId, parentEntity.getId(), typeName5);
                if (typeEntity == null) {
                    typeEntity = new BusinessTypeEntity();
                    typeEntity.setLevel(4);
                    typeEntity.setTitle(typeName5);
                    typeEntity.setParentType(parentEntity);
                    typeEntity.setOrgId(orgId);
                    typeEntity.setUnit(sameNameUnit);
                    typeEntity.setPosition(currentRowIndex);
                    typeEntity.setUpdateTime(new Date());
                    typeEntity.setCreateTime(typeEntity.getUpdateTime());
                    businessTypeRepo.save(typeEntity);
                    repeatInsert = false;
                }
            }
        }
        return repeatInsert;
    }
}
