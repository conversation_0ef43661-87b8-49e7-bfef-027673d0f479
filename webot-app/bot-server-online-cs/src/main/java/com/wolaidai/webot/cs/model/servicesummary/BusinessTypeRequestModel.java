package com.wolaidai.webot.cs.model.servicesummary;

import com.wolaidai.webot.cs.model.BaseModel;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class BusinessTypeRequestModel extends BaseModel {

    private Integer status;
    private List<BusinessTypeInfo> typeInfoList = new ArrayList<>();
    private Set<Integer> updStatusIds = new HashSet<>();
    private Set<Integer> deleteIds = new HashSet<>();

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<BusinessTypeInfo> getTypeInfoList() {
        return typeInfoList;
    }

    public void setTypeInfoList(List<BusinessTypeInfo> typeInfoList) {
        this.typeInfoList = typeInfoList;
    }

    public Set<Integer> getUpdStatusIds() {
        return updStatusIds;
    }

    public void setUpdStatusIds(Set<Integer> updStatusIds) {
        this.updStatusIds = updStatusIds;
    }

    public Set<Integer> getDeleteIds() {
        return deleteIds;
    }

    public void setDeleteIds(Set<Integer> deleteIds) {
        this.deleteIds = deleteIds;
    }

    public static class BusinessTypeInfo {

        private String id;
        private Integer parentId;
        @NotBlank(message = "{NotBlank.businessType.name}")
        private String title;
        private Integer status;
        private List<BusinessTypeInfo> children = new ArrayList<>();

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Integer getParentId() {
            return parentId;
        }

        public void setParentId(Integer parentId) {
            this.parentId = parentId;
        }

        public String getTitle() {
            return title;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public List<BusinessTypeInfo> getChildren() {
            return children;
        }

        public void setChildren(List<BusinessTypeInfo> children) {
            this.children = children;
        }
    }

}
