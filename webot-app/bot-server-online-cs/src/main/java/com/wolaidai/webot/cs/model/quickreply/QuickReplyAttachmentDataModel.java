package com.wolaidai.webot.cs.model.quickreply;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "快捷回复附件返回数据")
public class QuickReplyAttachmentDataModel extends BaseModel {

    private Integer id;
    @ApiModelProperty(value = "分类名或附件描述")
    private String content;
    @ApiModelProperty(value = "类型，0:分类; 1：附件")
    private Integer type;
    private String fileName;
    private String fileType;
    private String fileUrl;
    @ApiModelProperty(value = "节点Id,id-type")
    private String nodeId;
    private String fileId;
    private List<QuickReplyAttachmentDataModel> children = new ArrayList<>();
    @JsonIgnore
    private Integer position;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public List<QuickReplyAttachmentDataModel> getChildren() {
        return children;
    }

    public void setChildren(List<QuickReplyAttachmentDataModel> children) {
        this.children = children;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }
}
