package com.wolaidai.webot.cs.controller.report;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.report.ServiceDataModel;
import com.wolaidai.webot.cs.service.ServiceDataReportService;
import com.wolaidai.webot.cs.service.TaskService;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.security.domain.UserDomain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "客服工作量统计数据接口")
@RestController
@RequestMapping("/report")
public class ServiceDataReportController extends BaseController {

    @Autowired
    private ServiceDataReportService serviceDataService;
    @Autowired
    private TaskService taskService;

    @GetMapping("/serviceData")
    @ApiOperation(value = "获取客服工作量统计数据", response = ServiceDataModel.class)
    public ResponseModel getServiceDataReport(ServiceDataModel model) {
        if (null == model.getBeginDate() || null == model.getEndDate() || model.getEndDate().before(model.getBeginDate())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "查询开始时间或结束时间不合法");
        }
        serviceDataService.loadData(getUser().getOrganizationId(), model);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/serviceData/export")
    @ApiOperation(value = "导出客服工作量统计数据")
    public ResponseModel exportServiceDataReport(ServiceDataModel model) {
        if (null == model.getBeginDate() || null == model.getEndDate() || model.getEndDate().before(model.getBeginDate())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "导出开始时间或结束时间不合法");
        }
        UserDomain user = getUser();
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.SERVICE_DATA_REPORT, TaskEntity.PORT_EXPORT, model, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }

}
