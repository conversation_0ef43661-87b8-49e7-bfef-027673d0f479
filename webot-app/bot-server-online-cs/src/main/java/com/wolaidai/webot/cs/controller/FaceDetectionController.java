package com.wolaidai.webot.cs.controller;

import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.service.ApiService;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.entity.face.FaceDetectionEntity;
import com.wolaidai.webot.data.mysql.repo.*;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.http.client.fluent.Request;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Api(tags = "人脸验证相关接口")
@RestController
@RequestMapping("/api/face")
public class FaceDetectionController extends BaseController {

    protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private StringRedisTemplate csStringRedisTemplate;

    @Autowired
    private FaceDetectionRepo faceDetectionRepo;

    @Autowired
    private ApiService apiService;

    @Autowired
    private UserStateRepo userStateRepo;

    
    @GetMapping("/link")
    @ApiOperation(value = "获取人脸验证链接")
    public ResponseModel getLink(String mobile,String source) {
        if(StringUtils.isEmpty(mobile) || StringUtils.isEmpty(source)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "mobile, source不能为空");
        }
        if(!Arrays.asList("telephone").contains(source)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "source非法，暂不支持该种类型");
        }
       
        Optional<FaceDetectionEntity> faceOption = faceDetectionRepo.findFirstBySourceAndMobileOrderByCreateTimeDesc(source, mobile);
        String errMsg = null;
        if(faceOption.isPresent()) {
            FaceDetectionEntity face = faceOption.get();
            Date now = new Date();
            long intervalMinutes = (now.getTime() - face.getCreateTime().getTime())/(1000 * 60);
            if (Objects.equals(face.getCode(), 0) && intervalMinutes < appPropertyConfig.getFaceLinkIntervalMinutes()) {
                errMsg = "已发起人脸验证操作，验证成功，请勿重复操作";
            } else if ((face.getCode()==null || (face.getCode()!=0 && face.getFailureTimes()<appPropertyConfig.getMaxFaceDetectFailureTimes()))
                && intervalMinutes < appPropertyConfig.getFaceTokenTimeoutMinutes()){
                errMsg = "已发起人脸验证操作，请勿重复操作";
            }
        }
        if(null != errMsg){
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, errMsg);
        }
        
        try {
            String url = apiService.getFaceDocument(mobile);
            if(null == url) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "用户底照不存在");
            }
            InputStream is = Request.Get(url).execute().returnContent().asStream();
            String token = UUID.randomUUID().toString();
            String fileDir = "cs/facedection/" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()) + "/" + token + "/";
            String file = fileDir + "origin_" + token + ".jpg";
            OssFileClient.putObject(appPropertyConfig.getOssBucketName(), file, is);
            String key = String.format(RedisKey.CS_FACE_DETECTION, token);
            csStringRedisTemplate.opsForHash().put(key, "failureTimes", "0");
            csStringRedisTemplate.opsForHash().put(key, "photo", file);
            csStringRedisTemplate.expire(key, Duration.ofMinutes(appPropertyConfig.getFaceTokenTimeoutMinutes()));
            FaceDetectionEntity fd = new FaceDetectionEntity();
            fd.setSource(source);
            fd.setMobile(mobile);
            fd.setToken(token);
            fd.setFileId(Base64.getEncoder().encodeToString(file.getBytes()));
            fd.setCreateTime(new Date());
            fd.setUpdateTime(fd.getCreateTime());
            faceDetectionRepo.save(fd);
            String faceLink = appPropertyConfig.getFaceDetectionUrl() + "?token=" + token;
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "获取人脸验证链接成功", faceLink);
        } catch (Exception e) {
            LOGGER.error("获取人脸验证链接失败, mobile:" + mobile + ", source:" + source, e);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "获取人脸验证链接失败，请联系管理员");
        }
    }


    @GetMapping("/list")
    @ApiOperation(value = "获取人脸验证记录列表")
    public ResponseModel getResultList(String mobile,String source) {
        if(StringUtils.isEmpty(mobile) || StringUtils.isEmpty(source)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "mobile, source不能为空");
        }
        if(!Arrays.asList("telephone").contains(source)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "source非法，暂不支持该种类型");
        }

        List<JSONObject> resultList = new ArrayList<>();
        List<FaceDetectionEntity> faceList = faceDetectionRepo.findByMobileOrderByCreateTimeDesc(mobile);
        if(CollectionUtils.isNotEmpty(faceList)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            faceList.stream().forEach(v -> {
                JSONObject faceInfo = new JSONObject();
                faceInfo.put("token", v.getToken());
                faceInfo.put("source", v.getSource());
                faceInfo.put("group", "");
                faceInfo.put("service_user", "");
                if(null != v.getServiceUser()) {
                    UserStateEntity user = userStateRepo.findByOrgIdAndEmail(v.getOrgId(), v.getServiceUser());
                    faceInfo.put("group", "online");
                    faceInfo.put("service_user", user.getNickName());
                }
                faceInfo.put("create_time", sdf.format(v.getCreateTime()));
                int count = Objects.equals(v.getCode(), 0) ? v.getFailureTimes()+1 : v.getFailureTimes();
                faceInfo.put("count", count);
                int code = 0;
                if(null == v.getCode()) {
                    code = 2;
                } else if (v.getCode()!=0){
                    code = 1;
                }
                faceInfo.put("code", code);
                List<JSONObject> histList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(v.getHistories())) {
                    v.getHistories().stream().forEach(h -> {
                        JSONObject history = new JSONObject();
                        history.put("verify_time",  sdf.format(h.getCreateTime()));
                        history.put("vendor", h.getVendor());
                        history.put("code", Objects.equals(h.getCode(), 0) ? 0 : 1);
                        history.put("msg",  Objects.equals(h.getCode(), 0) ? "" : h.getMsg());
                        histList.add(history);
                    });
                    histList.sort((v1, v2) -> v2.getString("verify_time").compareTo(v1.getString("verify_time")));
                }
                faceInfo.put("detail", histList);
                resultList.add(faceInfo);
            });
            resultList.sort((v1, v2) -> v2.getString("create_time").compareTo(v1.getString("create_time")));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "获取人脸验证记录成功", resultList);
    }
}
