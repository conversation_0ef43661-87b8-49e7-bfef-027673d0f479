package com.wolaidai.webot.cs.model.quickreply;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "快捷回复附件")
public class QuickReplyAttachmentAddModel {

    @NotNull(message = "{NotNull.quickReply.category}")
    @ApiModelProperty(value = "分类ID")
    private Integer categoryId;
    @NotNull(message = "{NotNull.quickReply.attachment}")
    @ApiModelProperty(value = "附件")
    private MultipartFile file;
    @ApiModelProperty(value = "附件类型,图片-PICTURE,视频-VIDEO,其他-OTHER")
    private String attachmentType;
    @NotBlank(message = "{NotBlank.quickReply.content}")
    @ApiModelProperty(value = "描述")
    private String remark;

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public MultipartFile getFile() {
        return file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }

    public String getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(String attachmentType) {
        this.attachmentType = attachmentType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
