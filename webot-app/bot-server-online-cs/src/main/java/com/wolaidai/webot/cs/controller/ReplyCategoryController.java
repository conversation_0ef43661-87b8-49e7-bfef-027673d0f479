package com.wolaidai.webot.cs.controller;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.replycategory.*;
import com.wolaidai.webot.data.mysql.entity.chat.QuickReplyEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ReplyCategoryEntity;
import com.wolaidai.webot.data.mysql.repo.QuickReplyRepo;
import com.wolaidai.webot.data.mysql.repo.ReplyCategoryRepo;
import com.wolaidai.webot.security.domain.UserDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "快捷回复分类接口")
@RestController
@RequestMapping("/replyCategory")
public class ReplyCategoryController extends BaseController {

    @Autowired
    private ReplyCategoryRepo replyCategoryRepo;

    @Autowired
    private QuickReplyRepo quickReplyRepo;

    @PostMapping("/personal")
    @ApiOperation(value = "创建个人快捷回复分类")
    public ResponseModel createPersonalQuickReplyCategory(@Valid @RequestBody CategoryAddModel model) {
        if (!boolTypeValue.contains(model.getType())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类类型值非法");
        }
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        String title = model.getTitle();
        ReplyCategoryEntity sameNameExist = replyCategoryRepo.findByOrgIdAndNameAndCreatorAndTypeAndGlobal(
                orgId, title, email, model.getType(), ReplyCategoryEntity.GLOBAL_N);
        //兄弟不能重名
        if (sameNameExist != null && (model.getParentCategoryId() == null && sameNameExist.getParentCategory() == null
                || sameNameExist.getParentCategory() != null && Objects.equals(model.getParentCategoryId(), sameNameExist.getParentCategory().getId()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名分类已存在");
        }
        int level = 1;
        ReplyCategoryEntity parentCategory = null;
        if (model.getParentCategoryId() != null) {
            parentCategory = replyCategoryRepo.findByIdAndOrgIdAndCreatorAndGlobal(model.getParentCategoryId(), orgId, email, ReplyCategoryEntity.GLOBAL_N);
            if (parentCategory == null) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "上级分类不存在");
            } else {
                if (parentCategory.getLevel() != null) {
                    level = parentCategory.getLevel() + 1;
                }
            }
        }
        if (level > 2) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类级数最多2级");
        }
        ReplyCategoryEntity entity = new ReplyCategoryEntity();
        entity.setName(title);
        entity.setOrgId(orgId);
        entity.setGlobal(ReplyCategoryEntity.GLOBAL_N);
        entity.setParentCategory(parentCategory);
        entity.setLevel(level);
        entity.setType(model.getType());
        entity.setCreator(email);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        replyCategoryRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/{id}/personal")
    @ApiOperation(value = "更新个人快捷回复分类")
    public ResponseModel updatePersonalQuickReplyCategory(@PathVariable Integer id, @RequestBody CategoryUpdModel model) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        ReplyCategoryEntity category =
                replyCategoryRepo.findByIdAndOrgIdAndCreatorAndGlobal(id, orgId, email, ReplyCategoryEntity.GLOBAL_N);
        if (category == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        ReplyCategoryEntity sameNameExist =
                replyCategoryRepo.findByOrgIdAndNameAndCreatorAndTypeAndGlobal(orgId, model.getTitle(), email, category.getType(), ReplyCategoryEntity.GLOBAL_N);
        //兄弟不能重名
        if (sameNameExist != null && !sameNameExist.getId().equals(id)
                && (category.getParentCategory() == null && sameNameExist.getParentCategory() == null
                || sameNameExist.getParentCategory() != null && Objects.equals(category.getParentCategory().getId(), sameNameExist.getParentCategory().getId()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名分类已存在");
        }
        category.setName(model.getTitle());
        category.setUpdateTime(new Date());
        replyCategoryRepo.save(category);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @DeleteMapping("/{id}/personal")
    @ApiOperation(value = "删除个人快捷回复分类")
    public ResponseModel deletePersonalQuickReplyCategory(@PathVariable Integer id) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        ReplyCategoryEntity category = replyCategoryRepo.findByIdAndOrgIdAndCreatorAndGlobal(id,
                orgId, email, ReplyCategoryEntity.GLOBAL_N);
        if (category == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        List<Integer> categoryIds = new ArrayList<>();
        //目前只支持两级,无需递归
        if (CollectionUtils.isNotEmpty(category.getChildCategories())) {
            categoryIds.addAll(category.getChildCategories().stream().map(ReplyCategoryEntity :: getId).collect(Collectors.toList()));
        }
        categoryIds.add(id);
        for (Integer categoryId : categoryIds) {
            //删除其下快捷回复
            quickReplyRepo.deleteByOrgIdAndCategoryIdAndCreatorAndGlobal(orgId, categoryId, email, QuickReplyEntity.GLOBAL_N);
            //删除分类
            replyCategoryRepo.deleteById(categoryId);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/global")
    @ApiOperation(value = "创建公共快捷回复分类")
    public ResponseModel createGlobalQuickReplyCategory(@Valid @RequestBody CategoryAddModel model) {
        if (!boolTypeValue.contains(model.getType())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类类型值非法");
        }
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String title = model.getTitle();
        ReplyCategoryEntity sameNameExist = replyCategoryRepo.findByOrgIdAndNameAndTypeAndGlobal(
                orgId, model.getTitle(), model.getType(), ReplyCategoryEntity.GLOBAL_Y);
        //兄弟不能重名
        if (sameNameExist != null && (model.getParentCategoryId() == null && sameNameExist.getParentCategory() == null
                || sameNameExist.getParentCategory() != null && Objects.equals(model.getParentCategoryId(), sameNameExist.getParentCategory().getId()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名分类已存在");
        }
        int level = 1;
        ReplyCategoryEntity parentCategory = null;
        if (model.getParentCategoryId() != null) {
            parentCategory = replyCategoryRepo.findByIdAndOrgIdAndGlobal(model.getParentCategoryId(), orgId, ReplyCategoryEntity.GLOBAL_Y);
            if (parentCategory == null) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "上级分类不存在");
            } else {
                if (parentCategory.getLevel() != null) {
                    level = parentCategory.getLevel() + 1;
                }
            }
        }
        if (level > 2) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类级数最多2级");
        }
        ReplyCategoryEntity entity = new ReplyCategoryEntity();
        entity.setName(title);
        entity.setOrgId(orgId);
        entity.setGlobal(ReplyCategoryEntity.GLOBAL_Y);
        entity.setParentCategory(parentCategory);
        entity.setLevel(level);
        entity.setType(model.getType());
        entity.setCreator(user.getEmail());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        replyCategoryRepo.save(entity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/{id}/global")
    @ApiOperation(value = "更新公共快捷回复分类")
    public ResponseModel updateGlobalQuickReplyCategory(@PathVariable Integer id, @RequestBody CategoryUpdModel model) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        ReplyCategoryEntity category = replyCategoryRepo.findByIdAndOrgIdAndGlobal(id, orgId, ReplyCategoryEntity.GLOBAL_Y);
        if (category == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        ReplyCategoryEntity sameNameExist = replyCategoryRepo.findByOrgIdAndNameAndTypeAndGlobal(
                orgId, model.getTitle(), category.getType(), ReplyCategoryEntity.GLOBAL_Y);
        //兄弟不能重名
        if (sameNameExist != null && !sameNameExist.getId().equals(id)
                && (category.getParentCategory() == null && sameNameExist.getParentCategory() == null
                || sameNameExist.getParentCategory() != null && Objects.equals(category.getParentCategory().getId(), sameNameExist.getParentCategory().getId()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名分类已存在");
        }
        category.setName(model.getTitle());
        category.setUpdateTime(new Date());
        replyCategoryRepo.save(category);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @DeleteMapping("/{id}/global")
    @ApiOperation(value = "删除公共快捷回复分类")
    public ResponseModel deleteGlobalQuickReplyCategory(@PathVariable Integer id) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        ReplyCategoryEntity category = replyCategoryRepo.findByIdAndOrgIdAndGlobal(id, orgId, ReplyCategoryEntity.GLOBAL_Y);
        if (category == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        List<Integer> categoryIds = new ArrayList<>();
        categoryIds.add(id);
        //目前只支持两级,无需递归
        if (CollectionUtils.isNotEmpty(category.getChildCategories())) {
            categoryIds.addAll(category.getChildCategories().stream().map(ReplyCategoryEntity::getId).collect(Collectors.toList()));
        }
        //删除其下快捷回复
        quickReplyRepo.deleteByOrgIdAndCategoryIdsAndGlobal(orgId, categoryIds, QuickReplyEntity.GLOBAL_Y);
        //删除分类
        replyCategoryRepo.deleteAllById(categoryIds);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/{id}/sort")
    @ApiOperation(value = "排序个人快捷回复分类")
    public ResponseModel sortQuickReplyCategory(@PathVariable Integer id, @RequestBody GlobalCategorySortModel model) {
        if (!boolTypeValue.contains(model.getType())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类类型非法");
        }
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        Integer parentCategoryId = model.getParentCategoryId();
        ReplyCategoryEntity category = replyCategoryRepo.findByIdAndOrgIdAndGlobal(id, orgId, ReplyCategoryEntity.GLOBAL_N);
        if (category != null) {
            if (parentCategoryId == null) { //意味自己成为根节点
                category.setParentCategory(null);
                List<ReplyCategoryEntity> parentCategoryIsNullList = replyCategoryRepo
                        .findByOrgIdAndCreatorAndTypeAndGlobalAndParentCategoryIsNull(orgId, user.getEmail(), model.getType(), ReplyCategoryEntity.GLOBAL_N);
                for (ReplyCategoryEntity categoryEntity : parentCategoryIsNullList) {
                    Integer position = model.getSortMap().get(categoryEntity.getId());
                    if (position != null) {
                        categoryEntity.setPosition(position);
                        categoryEntity.setUpdateTime(new Date());
                        replyCategoryRepo.save(categoryEntity);
                    }
                }
            } else {
                ReplyCategoryEntity parentCategoryEntity = replyCategoryRepo.findByIdAndOrgIdAndGlobal(parentCategoryId, orgId, ReplyCategoryEntity.GLOBAL_N);
                if (parentCategoryEntity == null) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "上级分类不存在");
                }
                if (parentCategoryEntity.getLevel() > 1) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类级数最多2级");
                }
                for (ReplyCategoryEntity childCategory : parentCategoryEntity.getChildCategories()) {
                    if (childCategory.getName().equals(category.getName()) && !childCategory.getId().equals(category.getId())) {
                        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名分类已存在");
                    }
                    childCategory.setPosition(model.getSortMap().get(childCategory.getId()));
                }
                category.setParentCategory(parentCategoryEntity);
            }
            category.setPosition(model.getSortMap().get(category.getId()));
            category.setUpdateTime(new Date());
            replyCategoryRepo.save(category);
        } else {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PutMapping("/{id}/global/sort")
    @ApiOperation(value = "排序公共快捷回复分类")
    public ResponseModel sortGlobalQuickReplyCategory(@PathVariable Integer id, @RequestBody GlobalCategorySortModel model) {
        if (!boolTypeValue.contains(model.getType())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类类型非法");
        }
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        Integer parentCategoryId = model.getParentCategoryId();
        ReplyCategoryEntity category = replyCategoryRepo.findByIdAndOrgIdAndGlobal(id, orgId, ReplyCategoryEntity.GLOBAL_Y);
        if (category != null) {
            if (parentCategoryId == null) { //意味自己成为根节点
                category.setParentCategory(null);
                List<ReplyCategoryEntity> parentCategoryIsNullList = replyCategoryRepo
                        .findByOrgIdAndTypeAndGlobalAndParentCategoryIsNull(orgId, model.getType(), ReplyCategoryEntity.GLOBAL_Y);
                for (ReplyCategoryEntity categoryEntity : parentCategoryIsNullList) {
                    Integer position = model.getSortMap().get(categoryEntity.getId());
                    if (position != null) {
                        categoryEntity.setPosition(position);
                        categoryEntity.setUpdateTime(new Date());
                        replyCategoryRepo.save(categoryEntity);
                    }
                }
            } else {
                ReplyCategoryEntity parentCategoryEntity = replyCategoryRepo.findByIdAndOrgIdAndGlobal(parentCategoryId, orgId, ReplyCategoryEntity.GLOBAL_Y);
                if (parentCategoryEntity == null) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "上级分类不存在");
                }
                if (parentCategoryEntity.getLevel() > 1) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类级数最多2级");
                }
                for (ReplyCategoryEntity childCategory : parentCategoryEntity.getChildCategories()) {
                    if (childCategory.getName().equals(category.getName()) && !childCategory.getId().equals(category.getId())) {
                        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "同名分类已存在");
                    }
                    childCategory.setPosition(model.getSortMap().get(childCategory.getId()));
                }
                category.setParentCategory(parentCategoryEntity);
            }
            category.setPosition(model.getSortMap().get(category.getId()));
            category.setUpdateTime(new Date());
            replyCategoryRepo.save(category);
        } else {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "分类不存在");
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @GetMapping
    @ApiOperation(value = "查询个人快捷回复分类", response = CategorySearchModel.class)
    public ResponseModel searchQuickReplyCategory(CategorySearchModel model) {
        if (!boolTypeValue.contains(model.getSearchType())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "查询类型非法");
        }
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        List<ReplyCategoryEntity> replyCategoryEntityList = replyCategoryRepo
                .findByOrgIdAndCreatorAndTypeAndGlobalAndParentCategoryIsNullOrderByPositionAsc(orgId,
                        user.getEmail(), model.getSearchType(), ReplyCategoryEntity.GLOBAL_N);
        for (ReplyCategoryEntity categoryEntity : replyCategoryEntityList) {
            Integer categoryId = categoryEntity.getId();
            //一级-分类
            CategoryRowModel rowModel1 = new CategoryRowModel();
            rowModel1.setId(categoryId);
            rowModel1.setTitle(categoryEntity.getName());
            rowModel1.setLevel(categoryEntity.getLevel());
            rowModel1.setPosition(categoryEntity.getPosition());
            if (CollectionUtils.isNotEmpty(categoryEntity.getChildCategories())) {
                for (ReplyCategoryEntity childCategory : categoryEntity.getChildCategories()) {
                    //二级-分类
                    CategoryRowModel rowModel2 = new CategoryRowModel();
                    rowModel2.setId(childCategory.getId());
                    rowModel2.setTitle(childCategory.getName());
                    rowModel2.setLevel(childCategory.getLevel());
                    rowModel2.setPosition(childCategory.getPosition());
                    //二级下的节点排序
                    rowModel2.setChildren(rowModel2.getChildren().stream()
                            .sorted(Comparator.comparing(CategoryRowModel :: getPosition)).collect(Collectors.toList()));
                    rowModel1.getChildren().add(rowModel2);
                }
            }
            //一级下的节点排序
            rowModel1.setChildren(rowModel1.getChildren().stream()
                    .sorted(Comparator.comparing(CategoryRowModel :: getPosition)).collect(Collectors.toList()));
            model.getList().add(rowModel1);
        }
        //根节点排序
        model.getList().stream()
                .sorted(Comparator.comparing(CategoryRowModel :: getPosition)).collect(Collectors.toList());
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/global")
    @ApiOperation(value = "查询公共快捷回复分类", response = CategorySearchModel.class)
    public ResponseModel searchGlobalQuickReplyCategory(CategorySearchModel model) {
        if (!boolTypeValue.contains(model.getSearchType())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "查询类型非法");
        }
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        List<ReplyCategoryEntity> replyCategoryEntityList = replyCategoryRepo
                .findByOrgIdAndTypeAndGlobalAndParentCategoryIsNullOrderByPositionAsc(orgId, model.getSearchType(), ReplyCategoryEntity.GLOBAL_Y);
        for (ReplyCategoryEntity categoryEntity : replyCategoryEntityList) {
            Integer categoryId = categoryEntity.getId();
            //一级-分类
            CategoryRowModel rowModel1 = new CategoryRowModel();
            rowModel1.setId(categoryId);
            rowModel1.setTitle(categoryEntity.getName());
            rowModel1.setLevel(categoryEntity.getLevel());
            rowModel1.setPosition(categoryEntity.getPosition());
            if (CollectionUtils.isNotEmpty(categoryEntity.getChildCategories())) {
                for (ReplyCategoryEntity childCategory : categoryEntity.getChildCategories()) {
                    //二级-分类
                    CategoryRowModel rowModel2 = new CategoryRowModel();
                    rowModel2.setId(childCategory.getId());
                    rowModel2.setTitle(childCategory.getName());
                    rowModel2.setLevel(childCategory.getLevel());
                    rowModel2.setPosition(childCategory.getPosition());
                    //二级下的节点排序
                    rowModel2.setChildren(rowModel2.getChildren().stream()
                            .sorted(Comparator.comparing(CategoryRowModel :: getPosition)).collect(Collectors.toList()));
                    rowModel1.getChildren().add(rowModel2);
                }
            }
            //一级下的节点排序
            rowModel1.setChildren(rowModel1.getChildren().stream()
                    .sorted(Comparator.comparing(CategoryRowModel :: getPosition)).collect(Collectors.toList()));
            model.getList().add(rowModel1);
        }
        //根节点排序
        model.getList().stream()
                .sorted(Comparator.comparing(CategoryRowModel :: getPosition)).collect(Collectors.toList());
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }
}
