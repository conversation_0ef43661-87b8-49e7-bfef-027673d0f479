package com.wolaidai.webot.cs.model.report;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;

import java.math.BigDecimal;

@ApiModel(description = "会话统计列表数据")
public class SessionReportResModel extends BaseModel {
    private String date;
    private String bussiness;
    private String time;
    private Long totalCount;
    private Long botCount;
    private Long manualCount;
    private Long answerCount;
    private Long quitCount;
    private Long waitCount;
    private BigDecimal connectRate;
    private BigDecimal shuntRate;
    private Long sessionSecond;
    private Long avgSessionSecond;
    private Long waitSecond;
    private Long avgWaitSecond;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getBussiness() {
        return bussiness;
    }

    public void setBussiness(String bussiness) {
        this.bussiness = bussiness;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Long getBotCount() {
        return botCount;
    }

    public void setBotCount(Long botCount) {
        this.botCount = botCount;
    }

    public Long getManualCount() {
        return manualCount;
    }

    public void setManualCount(Long manualCount) {
        this.manualCount = manualCount;
    }

    public Long getAnswerCount() {
        return answerCount;
    }

    public void setAnswerCount(Long answerCount) {
        this.answerCount = answerCount;
    }

    public Long getQuitCount() {
        return quitCount;
    }

    public void setQuitCount(Long quitCount) {
        this.quitCount = quitCount;
    }

    public Long getWaitCount() {
        return waitCount;
    }

    public void setWaitCount(Long waitCount) {
        this.waitCount = waitCount;
    }

    public BigDecimal getConnectRate() {
        return connectRate;
    }

    public void setConnectRate(BigDecimal connectRate) {
        this.connectRate = connectRate;
    }

    public BigDecimal getShuntRate() {
        return shuntRate;
    }

    public void setShuntRate(BigDecimal shuntRate) {
        this.shuntRate = shuntRate;
    }

    public Long getSessionSecond() {
        return sessionSecond;
    }

    public void setSessionSecond(Long sessionSecond) {
        this.sessionSecond = sessionSecond;
    }

    public Long getAvgSessionSecond() {
        return avgSessionSecond;
    }

    public void setAvgSessionSecond(Long avgSessionSecond) {
        this.avgSessionSecond = avgSessionSecond;
    }

    public Long getWaitSecond() {
        return waitSecond;
    }

    public void setWaitSecond(Long waitSecond) {
        this.waitSecond = waitSecond;
    }

    public Long getAvgWaitSecond() {
        return avgWaitSecond;
    }

    public void setAvgWaitSecond(Long avgWaitSecond) {
        this.avgWaitSecond = avgWaitSecond;
    }
}
