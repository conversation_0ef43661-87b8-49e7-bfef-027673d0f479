package com.wolaidai.webot.cs.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.model.OSSObject;
import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.repo.TaskRepo;

public class CommonUtil {
    private final static TaskRepo taskRepo;
    static {
        taskRepo = SpringUtils.getApplicationContext().getBean(TaskRepo.class);
    }

    public static String getClientTypeName(Integer clientTypeId) {
        if (Objects.equals(clientTypeId, 1)) {
            return "H5";
        } else if (Objects.equals(clientTypeId, 2)) {
            return "微信";
        } else if (Objects.equals(clientTypeId, 3)) {
            return "企业微信";
        }
        return "未知";
    }
    
    public static String getStateName(Integer state) {
        if (Objects.equals(UserStateEntity.STATE_LOGIN, state)) {
            return "上线";
        } else if (Objects.equals(UserStateEntity.STATE_ONLINE, state)) {
            return "在线";
        } else if (Objects.equals(UserStateEntity.STATE_BUSY, state)) {
            return "忙碌";
        } else if (Objects.equals(UserStateEntity.STATE_RESTING, state)) {
            return "小休";
        } else if (Objects.equals(UserStateEntity.STATE_LEAVE, state)) {
            return "离开";
        } else if (Objects.equals(UserStateEntity.STATE_STUDYING, state)) {
            return "培训";
        } else if (Objects.equals(UserStateEntity.STATE_EATING, state)) {
            return "用餐";
        } else if (Objects.equals(UserStateEntity.STATE_OFFLINE, state)) {
            return "下线";
        }
        return "未知";
    }

    public static boolean excelHeaderCheck(Object[] standHeaders, Object[] targetHeaders) {
        return Arrays.equals(standHeaders, targetHeaders);
    }

    public static void saveTaskProgress(TaskEntity task, int progress) {
        if (null != task) {
            if (progress == -1) {
                task.setStatus(TaskEntity.FAIL_STATUS);
            } else {
                task.setProgress(Math.min(progress, 100));
                if (task.getProgress() == 100) {
                    task.setStatus(TaskEntity.SUCCESS_STATUS);
                }
            }
            task.setUpdateTime(new Date());
            taskRepo.save(task);
        }
    }

    public static String escapeHtml(String content, String messageType) {
        content = Jsoup.clean(content, Whitelist.none());
        if("text".equals(messageType)){
            return StringUtils.substring(content,0,100);
        } else if ("image".equals(messageType)) {
            return "[图片]";
        } else if ("voice".equals(messageType)) {
            return "[语音]";
        } else if ("video".equals(messageType)) {
            return "[视频]";
        } else if ("file".equals(messageType)) {
            return "[文件]";
        } else if ("recall".equals(messageType)) {
            return "你撤回了一条消息";
        }
        return StringUtils.substring(content,0,100);
    }

    public static String saveImportFileToOss(String taskType, File f, String ossBucketName) throws FileNotFoundException {
        String fileDir = "cs/" + "import/" + taskType + "/" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()) + "/";
        String file = fileDir + UUID.randomUUID() + ".xlsx";
        OssFileClient.putObject(ossBucketName, file, new FileInputStream(f));
        return Base64.getEncoder().encodeToString(file.getBytes());
    }

    public static File getImportFileFromOss(TaskEntity task, String ossBucketName) throws IOException {
        String decodeId = new String(Base64.getDecoder().decode(task.getFileId()));
        OSSObject ossObject = OssFileClient.getObject(ossBucketName, decodeId);
        String name = task.getName();
        File tmpFile = File.createTempFile(UUID.randomUUID().toString(), name.lastIndexOf('.') > -1 ? name.substring(name.lastIndexOf('.')) : null);
        byte[] data = IOUtils.toByteArray(ossObject.getObjectContent());
        FileOutputStream fos = new FileOutputStream(tmpFile);
        fos.write(data);
        fos.close();
        return tmpFile;
    }


    public static String maskPhoneNum(String phoneNum) {
        if(StringUtils.isNotBlank(phoneNum) && phoneNum.length() == 11) {
            String maskedNum = phoneNum.substring(0, 4) + "*****" + phoneNum.substring(9);
            return maskedNum;
        }
        return phoneNum;
    }

    public static String maskIdNum(String idNum) {
        if(StringUtils.isNotBlank(idNum) && idNum.length() == 18) {
            String maskedNum = idNum.substring(0, 6) + "********" +  idNum.substring(14);
            return maskedNum;
        }
        return idNum;
    }

    public static String processUserSensitiveInfo(String content, Boolean hisFlag) {
        if (StringUtils.isNotBlank(content) && content.length() > 10) {
            if(content.length()>17 && hisFlag) {
                content = CommonUtil.maskIdNumbers(content);
            }
            if(content.length()>15) {
                content = CommonUtil.maskCardNumbers(content);
            }
            if(hisFlag) {
                content = CommonUtil.maskPhoneNumbers(content);
            }
        }
        return content;
    }

    public static String maskPhoneNumbers(String info) {
        String regex = "(?<!\\d)(1[3-9]\\d{9})(?!\\d)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(info);
        StringBuffer sb = new StringBuffer();

        while (matcher.find() && matcher.group().length()==11) {
            String matchedNumber = matcher.group();
            String maskedNumber = matchedNumber.substring(0, 4) + "*****" + matchedNumber.substring(9);
            matcher.appendReplacement(sb, maskedNumber);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String maskCardNumbers(String info) {
        // 银行卡号（16位或19位）
        String regex = "(?<!\\d)([1-9])(\\d{3})(\\d{8}||\\d{11})(\\d{4})(?!\\d)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(info);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String matchedNumber = matcher.group();
            String mask = "***********";
            if(matchedNumber.length()==16) {
                mask = "********";
            }

            String maskedNumber = matcher.group(1) + matcher.group(2) + mask + matcher.group(4);
            matcher.appendReplacement(sb, maskedNumber);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String maskIdNumbers(String info) {
        String regex = "(?<!\\d)[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}([0-9Xx])(?!\\d)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(info);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String matchedNumber = matcher.group();
            String maskedNumber = matchedNumber.substring(0, 6) + "********" + matchedNumber.substring(14);
            matcher.appendReplacement(sb, maskedNumber);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static JSONObject maskInfo(JSONObject info) {
        if(info == null || info.isEmpty() || !info.containsKey("customers")) {
            return info;
        }
        JSONArray customers = info.getJSONArray("customers");
        if(customers.size() > 0) {
            for(int i = 0; i < customers.size(); i++) {
                JSONObject customer = customers.getJSONObject(i);
                if(customer.containsKey("mobile")) {
                    customer.put("mobile", maskPhoneNum(customer.getString("mobile")));
                }
                if(customer.containsKey("cnid")) {
                    customer.put("cnid", maskIdNum(customer.getString("cnid")));
                }
            }
        }
        return info;
    }
}
