package com.wolaidai.webot.cs.model.sessionlist.personal;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "个人聊天历史记录")
public class PersonalChatHistoryRowModel extends BaseModel {

    @ApiModelProperty(value = "客户端ID")
    private String clientId;
    @ApiModelProperty(value = "聊天类型,[event,text,voice,image,video,file]")
    private String type;
    @ApiModelProperty(value = "来源类型,[user,manual,bot]")
    private String sender;
    @ApiModelProperty(value = "是否撤回")
    private boolean recall;
    @ApiModelProperty(value = "聊天内容")
    private String content;
    @ApiModelProperty(value = "消息ID")
    private String msgId;
    @ApiModelProperty(value = "发送时间")
    private Date date;
    @ApiModelProperty(value = "客服信息")
    private JSONObject manual;
    @ApiModelProperty(value = "媒体信息")
    private JSONObject media;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public boolean isRecall() {
        return recall;
    }

    public void setRecall(boolean recall) {
        this.recall = recall;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public JSONObject getManual() {
        return manual;
    }

    public void setManual(JSONObject manual) {
        this.manual = manual;
    }

    public JSONObject getMedia() {
        return media;
    }

    public void setMedia(JSONObject media) {
        this.media = media;
    }
}
