package com.wolaidai.webot.cs.model.servicesummary;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;

import java.util.ArrayList;
import java.util.List;

public class BusinessUnitSearchModel extends BaseModel {

    private Integer status;
    private List<BusinessUnitRowModel> list = new ArrayList<>();

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<BusinessUnitRowModel> getList() {
        return list;
    }

    public void setList(List<BusinessUnitRowModel> list) {
        this.list = list;
    }

    public static class BusinessUnitRowModel {

        public BusinessUnitRowModel() {
        }

        public BusinessUnitRowModel(BusinessUnitEntity entity) {
            this.id = entity.getId();
            this.name = entity.getName();
            this.status = entity.getStatus();
        }

        private Integer id;
        private String name;
        private Integer status;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }
}
