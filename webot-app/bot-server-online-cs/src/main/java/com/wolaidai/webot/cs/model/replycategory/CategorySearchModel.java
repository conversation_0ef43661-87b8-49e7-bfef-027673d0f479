package com.wolaidai.webot.cs.model.replycategory;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "快捷回复分类查询")
public class CategorySearchModel extends BaseModel {

    @ApiModelProperty(value = "查询类型, 0-文本, 1-附件")
    private Integer searchType;
    private List<CategoryRowModel> list = new ArrayList<>();

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }

    public List<CategoryRowModel> getList() {
        return list;
    }

    public void setList(List<CategoryRowModel> list) {
        this.list = list;
    }
}
