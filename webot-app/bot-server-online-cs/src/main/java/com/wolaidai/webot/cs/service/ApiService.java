package com.wolaidai.webot.cs.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class ApiService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppPropertyConfig appPropertyConfig;

    public JSONObject getCustomerInfoByMobile(String mobile) {
        if(StringUtils.isAnyBlank(appPropertyConfig.getCustomerInfoUrl(),mobile)){
            return null;
        }
        try {
            return JSON.parseObject(HttpClientUtil.post(appPropertyConfig.getCustomerInfoUrl(), new JSONObject().fluentPut("mobile", mobile).toString()));
        } catch (Exception e) {
            LOGGER.error("call getCustomerInfoByMobile error", e);
            return null;
        }
    }

    public JSONObject getIpInfo(String ip) {
        if(StringUtils.isAnyBlank(appPropertyConfig.getIpInfoUrl(),ip)){
            return null;
        }
        try {
            return JSON.parseObject(HttpClientUtil.post(appPropertyConfig.getIpInfoUrl(), new JSONObject().fluentPut("ip", ip).fluentPut("apiOperator", "WELAB-AI").toString()));
        } catch (Exception e) {
            LOGGER.error("call getIpInfo error", e);
            return null;
        }
    }

    public String getFaceDocument(String mobile) {
        if(StringUtils.isAnyBlank(appPropertyConfig.getFaceDocumentsUrl(),mobile)){
            return null;
        }
        try {
            String response = HttpClientUtil.get(appPropertyConfig.getFaceDocumentsUrl() + "?mobile=" + mobile);
            if(StringUtils.isBlank(response)){
                return null;
            }
            JSONObject responseJson = JSON.parseObject(response);
            JSONArray data = responseJson.getJSONArray("data");
            if(!CollectionUtils.isEmpty(data)){
                JSONObject documentData = data.getJSONObject(0);
                return documentData.getString("url");
            }
        } catch (Exception e) {
            LOGGER.error("call getFaceDocument error", e);
        }
        return null;
    }

}
