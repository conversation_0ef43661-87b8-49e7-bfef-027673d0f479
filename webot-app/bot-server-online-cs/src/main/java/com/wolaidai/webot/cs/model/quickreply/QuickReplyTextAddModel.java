package com.wolaidai.webot.cs.model.quickreply;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "快捷回复文本")
public class QuickReplyTextAddModel extends BaseModel {

    @NotNull(message = "{NotNull.quickReply.category}")
    @ApiModelProperty(value = "分类ID")
    private Integer categoryId;
    @NotBlank(message = "{NotBlack.quickReply.content}")
    @ApiModelProperty(value = "内容")
    private String content;

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

}
