package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.chat.ServiceSummaryEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "服务小结详情")
public class ServiceSummaryDetailModel extends BaseModel {
    @ApiModelProperty(value = "服务小结内容")
    private String remark;
    @ApiModelProperty(value = "业务单元id")
    private Integer unitId;
    @ApiModelProperty(value = "业务单元名称")
    private String unitName;
    @ApiModelProperty(value = "业务类型id")
    private Integer typeId;
    @ApiModelProperty(value = "业务类型名称")
    private String typeName;
    @ApiModelProperty(value = "业务单元key")
    private String uid;
    @ApiModelProperty(value = "业务类型key")
    private String tid;
    @ApiModelProperty(value = "AI小结备注")
    private String aiRemark;

    public ServiceSummaryDetailModel(ServiceSummaryEntity ss) {
        this.remark = ss.getRemark();
        this.aiRemark = ss.getAiRemark();
        BusinessUnitEntity unit = ss.getUnit();
        if (null != unit) {
            this.unitId = unit.getId();
            this.unitName = unit.getName();
            this.uid = "u_" + unit.getId();
        }
        BusinessTypeEntity type = ss.getType();
        if (null != type) {
            this.typeId = type.getId();
            this.tid = "t_" + type.getId();

            String summaryInfo = type.getTitle();
            while(type.getParentType()!=null) {
                type = type.getParentType();
                summaryInfo = type.getTitle() + "#" + summaryInfo;
            }
            this.typeName = summaryInfo;
        }

        



    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getAiRemark() {
        return aiRemark;
    }

    public void setAiRemark(String aiRemark) {
        this.aiRemark = aiRemark;
    }
}
