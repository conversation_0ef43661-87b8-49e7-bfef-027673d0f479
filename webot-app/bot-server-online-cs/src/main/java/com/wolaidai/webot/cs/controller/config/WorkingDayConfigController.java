package com.wolaidai.webot.cs.controller.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.enums.AuditAction;
import com.wolaidai.webot.data.mysql.enums.AuditModule;
import com.wolaidai.webot.data.redis.constant.RedisKey;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "上下班时间设置接口")
@RestController
@RequestMapping("/workingday/config")
public class WorkingDayConfigController extends BaseController {
    @Autowired
    private ConfigService configService;

    @GetMapping
    @ApiOperation(value = "查询上下班时间设置")
    public ResponseModel getWorkTimeConfig() {
        Integer orgId = getUser().getOrganizationId();
        String config = configService.read(true, orgId, String.format(RedisKey.CS_WORKTIME_CONFIG, orgId), null, CommonConfigEntity.TYPE_WORKTIME_CONFIG);
        if (StringUtils.isNotBlank(config)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", JSONObject.parse(config));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功");
    }

    @PostMapping
    @ApiOperation(value = "更新上下班时间设置")
    public ResponseModel updateWorkTimeConfig(@RequestBody JSONObject data) {
        configService.save(getUser().getOrganizationId(), data.toString(), CommonConfigEntity.TYPE_WORKTIME_CONFIG);
        auditLog(AuditAction.UPDATE, AuditModule.WORKTIME_CONFIG, null, "更新节假日设置");
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }
}
