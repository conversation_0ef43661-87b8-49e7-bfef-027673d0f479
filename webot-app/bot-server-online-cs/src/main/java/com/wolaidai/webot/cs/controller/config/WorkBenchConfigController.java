package com.wolaidai.webot.cs.controller.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.enums.AuditAction;
import com.wolaidai.webot.data.mysql.enums.AuditModule;
import com.wolaidai.webot.data.redis.constant.RedisKey;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "工作台提醒设置接口")
@RestController
@RequestMapping("/workbench/config")
public class WorkBenchConfigController extends BaseController {
    @Autowired
    private ConfigService configService;

    @GetMapping
    @ApiOperation(value = "查询工作台提醒设置")
    public ResponseModel getWorkBenchConfig(String configType) {
        Integer orgId = getUser().getOrganizationId();
        if (StringUtils.isBlank(configType)) {
            JSONObject data = new JSONObject();
            JSONObject statusConfig = configService.readData(true, orgId, String.format(RedisKey.WORKBENCH_CONFIG_STATUS, orgId), CommonConfigEntity.TYPE_WORKBENCH_CONFIG_STATUS);
            statusConfig.forEach((k, v) -> data.put(k, new JSONObject().fluentPut("status", Boolean.valueOf(String.valueOf(v)))));
            JSONObject config = configService.readData(true, orgId, String.format(RedisKey.WORKBENCH_CONFIG, orgId), CommonConfigEntity.TYPE_WORKBENCH_CONFIG);
            config.forEach((k, _v) -> {
                String v = String.valueOf(_v);
                if (null != _v && StringUtils.isNotBlank(v)) {
                    JSONObject configJson = data.getJSONObject(k);
                    if (configJson == null) {
                        data.put(k, new JSONObject().fluentPut("status", false).fluentPut("config", JSON.parse(v)));
                    } else {
                        configJson.put("config", JSON.parse(v));
                    }
                }
            });
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", data);
        }
        String status = configService.read(true, orgId, String.format(RedisKey.WORKBENCH_CONFIG_STATUS, orgId), configType, CommonConfigEntity.TYPE_WORKBENCH_CONFIG_STATUS, configType);
        String config = configService.read(true, orgId, String.format(RedisKey.WORKBENCH_CONFIG, orgId), configType, CommonConfigEntity.TYPE_WORKBENCH_CONFIG, configType);
        if (null == status && null == config) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功");
        }
        JSONObject data = new JSONObject();
        data.put("status", Boolean.valueOf(status));
        if (StringUtils.isNotBlank(config)) {
            data.put("config", JSON.parse(config));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", data);
    }

    @PostMapping("/status/{status}")
    @ApiOperation(value = "设置工作台提醒状态")
    public ResponseModel updateWorkBenchStatus(@RequestParam String configType, @PathVariable String status) {
        configService.save(getUser().getOrganizationId(), status, CommonConfigEntity.TYPE_WORKBENCH_CONFIG_STATUS, configType);
        auditLog(AuditAction.UPDATE, AuditModule.WORKBENCH_CONFIG, configType + ":" + status, "更新工作台提醒状态");
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping
    @ApiOperation(value = "更新工作台提醒设置")
    public ResponseModel updateWorkBenchConfig(@RequestParam String configType, @RequestBody JSONObject data) {
        configService.save(getUser().getOrganizationId(), data.toString(), CommonConfigEntity.TYPE_WORKBENCH_CONFIG, configType);
        auditLog(AuditAction.UPDATE, AuditModule.WORKBENCH_CONFIG, configType, "更新工作台提醒设置");
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }
}
