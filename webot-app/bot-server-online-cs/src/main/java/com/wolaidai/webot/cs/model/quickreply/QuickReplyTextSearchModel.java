package com.wolaidai.webot.cs.model.quickreply;

import com.wolaidai.webot.cs.model.PageableModel;

import java.util.ArrayList;
import java.util.List;

public class QuickReplyTextSearchModel extends PageableModel {

    private String key;
    private Integer categoryId = -1;
    private List<QuickReplyTextRowModel> list = new ArrayList<>();

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public List<QuickReplyTextRowModel> getList() {
        return list;
    }

    public void setList(List<QuickReplyTextRowModel> list) {
        this.list = list;
    }
}
