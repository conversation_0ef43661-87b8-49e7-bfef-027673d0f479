package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "聊天记录")
public class ChatHistoryModel extends BaseModel {
    @ApiModelProperty(value = "聊天类型,[event,text,voice,image]")
    private String chatType;
    @ApiModelProperty(value = "聊天内容")
    private String content;
    @ApiModelProperty(value = "发送者")
    private String sender;
    @ApiModelProperty(value = "消息ID")
    private String msgId;
    @ApiModelProperty(value = "发送时间")
    private Date sendTime;

    public String getChatType() {
        return chatType;
    }

    public void setChatType(String chatType) {
        this.chatType = chatType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }
}
