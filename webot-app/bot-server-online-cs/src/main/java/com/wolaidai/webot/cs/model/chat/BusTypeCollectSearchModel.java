package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class BusTypeCollectSearchModel extends BaseModel {

    private List<BusinessTypeInfoRow> rowList = new ArrayList<>();

    public List<BusinessTypeInfoRow> getRowList() {
        return rowList;
    }

    public void setRowList(List<BusinessTypeInfoRow> rowList) {
        this.rowList = rowList;
    }

    public static class BusinessTypeInfoRow {

        private String summaryKey;
        private String pathName;
        private Integer status;

        public String getSummaryKey() {
            return summaryKey;
        }

        public void setSummaryKey(String summaryKey) {
            this.summaryKey = summaryKey;
        }

        public String getPathName() {
            return pathName;
        }

        public void setPathName(String pathName) {
            this.pathName = pathName;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public BusinessTypeInfoRow() {
        }

        public BusinessTypeInfoRow(BusinessUnitEntity unitEntity, BusinessTypeEntity typeEntity) {
            if (unitEntity != null) {
                this.pathName = unitEntity.getName();
                this.summaryKey = "u_" + unitEntity.getId();
                this.status = unitEntity.getStatus();
            }
            if (typeEntity != null) {
                this.pathName = StringUtils.isBlank(pathName) ? typeEntity.fullPathTitle() : pathName.concat("#" + typeEntity.fullPathTitle());
                this.summaryKey = "t_" + typeEntity.getId();
                //业务单元关闭未联动禁用类型,这里做展示处理
                if (!BusinessUnitEntity.INACTIVE_STATUS.equals(unitEntity.getStatus())) {
                    this.status = typeEntity.getStatus();
                }
            }
        }

    }
}
