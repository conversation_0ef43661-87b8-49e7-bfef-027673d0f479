package com.wolaidai.webot.cs.model.userstate;

import com.wolaidai.webot.cs.model.BaseModel;

public class MaxReceptionUpdateModel extends BaseModel {

    private String email;
    private Integer maxReception;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getMaxReception() {
        return maxReception;
    }

    public void setMaxReception(Integer maxReception) {
        this.maxReception = maxReception;
    }

}
