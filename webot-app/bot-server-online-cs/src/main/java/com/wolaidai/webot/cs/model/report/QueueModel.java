package com.wolaidai.webot.cs.model.report;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.model.PageableModel;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import io.swagger.annotations.ApiModelProperty;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class QueueModel extends PageableModel {

    private Date startTime;
    private Date endTime;
    @ApiModelProperty(value = "客户uuid")
    private String uuid;
    @ApiModelProperty(value = "客户userId")
    private Integer userId;
    private List<QueueInfoRow> infoRowList = new ArrayList<>();

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public List<QueueInfoRow> getInfoRowList() {
        return infoRowList;
    }

    public void setInfoRowList(List<QueueInfoRow> infoRowList) {
        this.infoRowList = infoRowList;
    }

    public static class QueueInfoRow {
        @ApiModelProperty(value = "数据日期")
        private String date;
        @ApiModelProperty(value = "客户端类型ID:1-H5/2-微信/3-企业微信")
        private Integer clientTypeId;
        private String customerName;
        private String mobile;
        @ApiModelProperty(value = "业务类型")
        private String businessName = "";
        @ApiModelProperty(value = "请求时间")
        private String reqTime;
        @ApiModelProperty(value = "放弃时间")
        private String giveUpTime;
        @ApiModelProperty(value = "等待秒数")
        private String waitSeconds;
        @ApiModelProperty(value = "客户uuid")
        private String uuid;
        @ApiModelProperty(value = "客户userId")
        private Integer userId;

        public QueueInfoRow (SimpleDateFormat sdf, QueueListEntity entity) {
            this.date = sdf.format(entity.getCreateTime());
            this.clientTypeId = entity.getClientTypeId();
            this.customerName = entity.getCustomerName();
            JSONObject customerDetail = entity.getCustomerDetail();
            if (customerDetail != null) {
                JSONArray customers = customerDetail.getJSONArray("customers");
                if (customers != null && customers.size() > 0) {
                    JSONObject customer = customers.getJSONObject(0);
                    this.mobile = CommonUtil.maskPhoneNum(customer.getString("mobile"));
                    this.uuid = customer.getString("uuid");
                    this.userId = customer.getInteger("userId");
                }
            }
            this.businessName = entity.getBusinessName();
            this.reqTime = DateUtil.formatDateTime(entity.getCreateTime(), "");
            this.giveUpTime = DateUtil.formatDateTime(entity.getUpdateTime(), "");
            this.waitSeconds = DateUtil.formatSecondsTime(entity.getWaitSecond(), "");
        }

        public QueueInfoRow() {
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public Integer getClientTypeId() {
            return clientTypeId;
        }

        public void setClientTypeId(Integer clientTypeId) {
            this.clientTypeId = clientTypeId;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getBusinessName() {
            return businessName;
        }

        public void setBusinessName(String businessName) {
            this.businessName = businessName;
        }

        public String getReqTime() {
            return reqTime;
        }

        public void setReqTime(String reqTime) {
            this.reqTime = reqTime;
        }

        public String getGiveUpTime() {
            return giveUpTime;
        }

        public void setGiveUpTime(String giveUpTime) {
            this.giveUpTime = giveUpTime;
        }

        public String getWaitSeconds() {
            return waitSeconds;
        }

        public void setWaitSeconds(String waitSeconds) {
            this.waitSeconds = waitSeconds;
        }

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public Integer getUserId() {
            return userId;
        }

        public void setUserId(Integer userId) {
            this.userId = userId;
        }

        
    }

}
