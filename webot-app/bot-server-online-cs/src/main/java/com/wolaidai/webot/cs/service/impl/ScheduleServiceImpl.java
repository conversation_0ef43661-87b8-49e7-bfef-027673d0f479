package com.wolaidai.webot.cs.service.impl;

import com.wolaidai.webot.cs.service.*;
import com.wolaidai.webot.cs.task.RunnableTask;
import com.wolaidai.webot.cs.util.SpringUtils;
import org.redisson.api.CronSchedule;
import org.redisson.api.RScheduledExecutorService;
import org.redisson.api.RedissonClient;
import org.redisson.api.WorkerOptions;
import org.springframework.stereotype.Service;

import java.io.Serializable;

@Service
public class ScheduleServiceImpl implements ScheduleService, Serializable {
    @Override
    public void startTaskSchedule() {
        RScheduledExecutorService executorService = SpringUtils.getApplicationContext().getBean(RedissonClient.class).getExecutorService("service:task:cs:init");
        executorService.getTaskIds().forEach(v->executorService.cancelTask(v));
        executorService.delete();
        executorService.registerWorkers(WorkerOptions.defaults());
        executorService.execute((RunnableTask) ()->SpringUtils.getApplicationContext().getBean(CheckTaskService.class).checkTask());
        executorService.schedule((RunnableTask)() -> SpringUtils.getApplicationContext().getBean(QueueListService.class).checkQueueList(), CronSchedule.of("0/10 * 8-22 * * ?"));
        executorService.schedule((RunnableTask) () -> SpringUtils.getApplicationContext().getBean(ReportHistoryService.class).initReportRecord(), CronSchedule.of("0 50 23 * * ?"));
        executorService.schedule((RunnableTask) () -> SpringUtils.getApplicationContext().getBean(ReportHistoryService.class).generateReport(), CronSchedule.of("0 10 0 * * ?"));
        executorService.schedule((RunnableTask) () -> SpringUtils.getApplicationContext().getBean(ServiceSummaryService.class).checkSummaryTarget(), CronSchedule.of("0 0/30 8-22 * * ?"));
    }
}