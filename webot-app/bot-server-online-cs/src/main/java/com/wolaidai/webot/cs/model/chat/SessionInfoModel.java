package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "会话信息")
public class SessionInfoModel extends BaseModel {
    private Integer id;
    @ApiModelProperty(value = "会话唯一标识")
    private String sessionKey;
    @ApiModelProperty(value = "业务名")
    private String businessName;
    @ApiModelProperty(value = "客户姓名")
    private String customerName;
    @ApiModelProperty(value = "客户类型,0:普通用户；1：VIP用户")
    private Integer customerType;
    @ApiModelProperty(value = "客户端类型ID，1:H5;2:微信;3:企业微信")
    private Integer clientTypeId;
    @ApiModelProperty(value = "客户类型名称")
    private String clientTypeName;
    @ApiModelProperty(value = "是否被标星，0：未标记，1：被标记")
    private Integer mark;
    @ApiModelProperty(value = "未读消息数")
    private Integer unreadMsgCount;
    @ApiModelProperty(value = "客户最早回复消息时间")
    private Date customerFirstReplyTime;
    @ApiModelProperty(value = "客户最新回复消息时间")
    private Date customerLastReplyTime;
    @ApiModelProperty(value = "最后消息")
    private String lastMsg;
    @ApiModelProperty(value = "最后消息时间")
    private Date lastMsgTime;
    @ApiModelProperty(value = "创建时间")
    private Long createTimeMs;
    @ApiModelProperty(value = "离线时间")
    private Long offlineTimeMs;
    @ApiModelProperty(value = "是否已小结")
    private boolean hasServiceSummary;
    @ApiModelProperty(value = "是否客服首响超时")
    private boolean hasFirstReplyTimeout;
    @ApiModelProperty(value = "是否会话响应超时")
    private boolean hasSessionReplyTimeout;

    public SessionInfoModel(SessionListEntity s) {
        this.id = s.getId();
        this.sessionKey = s.getSessionKey();
        this.businessName = s.getBusinessName();
        this.customerName = s.getCustomerName();
        this.customerType = s.getCustomerType();
        this.clientTypeId = s.getClientTypeId();
        this.clientTypeName = CommonUtil.getClientTypeName(this.clientTypeId);
        this.mark = s.getMark();
        this.unreadMsgCount = s.getUnreadMsgCount();
        this.customerFirstReplyTime = s.getCustomerFirstReplyTime();
        this.customerLastReplyTime = s.getCustomerLastReplyTime();
        this.lastMsg = s.getLastMsg();
        this.lastMsgTime = s.getLastMsgTime();
        this.createTimeMs = s.getCreateTime().getTime();
        if(s.getOfflineTime()!=null){
            this.offlineTimeMs = s.getOfflineTime().getTime();
        }
        if(s.getServiceSummary()!=null && s.getServiceSummary().getUnit() != null){
            hasServiceSummary = true;
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public String getClientTypeName() {
        return clientTypeName;
    }

    public void setClientTypeName(String clientTypeName) {
        this.clientTypeName = clientTypeName;
    }

    public Integer getMark() {
        return mark;
    }

    public void setMark(Integer mark) {
        this.mark = mark;
    }

    public Integer getUnreadMsgCount() {
        return unreadMsgCount;
    }

    public void setUnreadMsgCount(Integer unreadMsgCount) {
        this.unreadMsgCount = unreadMsgCount;
    }

    public Date getCustomerFirstReplyTime() {
        return customerFirstReplyTime;
    }

    public void setCustomerFirstReplyTime(Date customerFirstReplyTime) {
        this.customerFirstReplyTime = customerFirstReplyTime;
    }

    public Date getCustomerLastReplyTime() {
        return customerLastReplyTime;
    }

    public void setCustomerLastReplyTime(Date customerLastReplyTime) {
        this.customerLastReplyTime = customerLastReplyTime;
    }

    public String getLastMsg() {
        return lastMsg;
    }

    public void setLastMsg(String lastMsg) {
        this.lastMsg = lastMsg;
    }

    public Date getLastMsgTime() {
        return lastMsgTime;
    }

    public void setLastMsgTime(Date lastMsgTime) {
        this.lastMsgTime = lastMsgTime;
    }

    public Long getCreateTimeMs() {
        return createTimeMs;
    }

    public void setCreateTimeMs(Long createTimeMs) {
        this.createTimeMs = createTimeMs;
    }

    public Long getOfflineTimeMs() {
        return offlineTimeMs;
    }

    public void setOfflineTimeMs(Long offlineTimeMs) {
        this.offlineTimeMs = offlineTimeMs;
    }

    public boolean isHasServiceSummary() {
        return hasServiceSummary;
    }

    public void setHasServiceSummary(boolean hasServiceSummary) {
        this.hasServiceSummary = hasServiceSummary;
    }

    public boolean isHasFirstReplyTimeout() {
        return hasFirstReplyTimeout;
    }

    public void setHasFirstReplyTimeout(boolean hasFirstReplyTimeout) {
        this.hasFirstReplyTimeout = hasFirstReplyTimeout;
    }

    public boolean isHasSessionReplyTimeout() {
        return hasSessionReplyTimeout;
    }

    public void setHasSessionReplyTimeout(boolean hasSessionReplyTimeout) {
        this.hasSessionReplyTimeout = hasSessionReplyTimeout;
    }
}
