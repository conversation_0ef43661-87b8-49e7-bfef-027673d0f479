package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "排队信息")
public class LineInfoModel extends BaseModel {
    private Integer id;
    @ApiModelProperty(value = "业务名")
    private String businessName;
    @ApiModelProperty(value = "客户姓名")
    private String customerName;
    @ApiModelProperty(value = "客户类型,0:普通用户；1：VIP用户")
    private Integer customerType;
    @ApiModelProperty(value = "客户端类型ID，1:H5;2:微信;3:企业微信")
    private Integer clientTypeId;
    @ApiModelProperty(value = "客户类型名称")
    private String clientTypeName;
    @ApiModelProperty(value = "排队等待时间")
    private Long waitSecond;
    @ApiModelProperty(value = "最后一条消息")
    private String lastMsg;
    @ApiModelProperty(value = "最后消息时间")
    private Date lastMsgTime;
    private Long createTimeMs;
    
    public LineInfoModel(QueueListEntity q) {
        this.id = q.getId();
        this.businessName = q.getBusinessName();
        this.customerName = q.getCustomerName();
        this.customerType = q.getCustomerType();
        this.clientTypeId = q.getClientTypeId();
        this.clientTypeName = CommonUtil.getClientTypeName(this.clientTypeId);
        this.waitSecond = q.getWaitSecond();
        this.lastMsg = q.getLastMsg();
        this.lastMsgTime = q.getLastMsgTime();
        Date createTime = q.getCreateTime();
        if (null != createTime) {
            this.createTimeMs = createTime.getTime();
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public String getClientTypeName() {
        return clientTypeName;
    }

    public void setClientTypeName(String clientTypeName) {
        this.clientTypeName = clientTypeName;
    }

    public Long getWaitSecond() {
        return waitSecond;
    }

    public void setWaitSecond(Long waitSecond) {
        this.waitSecond = waitSecond;
    }

    public String getLastMsg() {
        return lastMsg;
    }

    public void setLastMsg(String lastMsg) {
        this.lastMsg = lastMsg;
    }

    public Date getLastMsgTime() {
        return lastMsgTime;
    }

    public void setLastMsgTime(Date lastMsgTime) {
        this.lastMsgTime = lastMsgTime;
    }

    public Long getCreateTimeMs() {
        return createTimeMs;
    }

    public void setCreateTimeMs(Long createTimeMs) {
        this.createTimeMs = createTimeMs;
    }
}
