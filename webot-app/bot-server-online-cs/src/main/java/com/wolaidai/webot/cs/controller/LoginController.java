package com.wolaidai.webot.cs.controller;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/auth")
public class LoginController extends BaseController {

    @RequestMapping("/anonymous")
    public ResponseModel anonymous() {
        LOGGER.info("anonymous from url: " + urlPathHelper.getOriginatingServletPath(request));
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_ANONYMOUS, "未登录");
    }

    @RequestMapping("/accessDenied")
    public ResponseModel accessDenied() {
        LOGGER.info("accessDenied from url: " + urlPathHelper.getOriginatingServletPath(request));
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_ACCESS_DENIED, "拒绝访问");
    }

}
