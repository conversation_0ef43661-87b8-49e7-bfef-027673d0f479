package com.wolaidai.webot.cs.controller;


import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.chat.InnerChatSearchModel;
import com.wolaidai.webot.cs.model.innerchat.InnerChatSummaryModel;
import com.wolaidai.webot.cs.model.innerchat.InnerChatUserModel;
import com.wolaidai.webot.cs.model.innerchat.InnerServiceUserModel;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.mysql.repo.chat.ChatRecordRepo;
import com.wolaidai.webot.data.mysql.repo.chat.ChatRoomRepo;
import com.wolaidai.webot.data.mysql.repo.chat.ChatSessionRepo;
import com.wolaidai.webot.data.mysql.repo.chat.ComplexChatRecordRepo;
import com.wolaidai.webot.security.domain.UserDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.Collator;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Api(tags = "内部聊天相关接口")
@RestController
@RequestMapping("/inner")
public class InnerChatController extends BaseController {

    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    private ChatRoomRepo chatRoomRepo;
    @Autowired
    private ComplexChatRecordRepo complexChatRecordRepo;
    @Autowired
    private ChatSessionRepo chatSessionRepo;
    @Autowired
    private ChatRecordRepo chatRecordRepo;

    @GetMapping(value = "/summary")
    @ApiOperation(value = "内部聊天概要查询", response = InnerChatSummaryModel.class)
    public ResponseModel getInnerChatSummary(InnerChatSummaryModel summaryModel) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        UserStateEntity userState = userStateRepo.findByOrgIdAndEmail(orgId, email);
        if (userState == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "未授权访问");
        }
        Comparator comparator = Collator.getInstance(Locale.CHINA);
        List<ChatSessionEntity> chatSessionEntities = chatSessionRepo.findByOrgIdAndUserId(orgId, userState.getId());
        for (ChatSessionEntity sessionEntity : chatSessionEntities) {
            InnerChatSummaryModel.ChatSessionRow chatSessionRow = new InnerChatSummaryModel.ChatSessionRow(email, sessionEntity, comparator);
            if (sessionEntity.getRoom() != null) { //群聊要处理特别最后一条消息
                ChatRecordEntity recordEntity = chatRecordRepo.findFirstBySessionIdAndEmail(orgId, email, sessionEntity.getId());
                if (recordEntity != null) {
                    chatSessionRow.setLastMsg(CommonUtil.escapeHtml(recordEntity.getContent(), recordEntity.getType()));
                    chatSessionRow.setLastMsgTime(recordEntity.getCreateTime());
                    chatSessionRow.setLastMsgSender(recordEntity.getManual().getString("nickName"));
                }
            }
            if (ChatMembersEntity.STATUS_ACTIVE.equals(chatSessionRow.getStatus())) { //激活的单聊会话、群聊才展示
                summaryModel.getRowList().add(chatSessionRow);
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", summaryModel);
    }

    @GetMapping(value = "/history/{sessionKey}")
    @ApiOperation(value = "内部聊天记录查询", response = InnerChatSearchModel.class)
    public ResponseModel getInnerChatHistory(@PathVariable Integer sessionKey, InnerChatSearchModel searchModel) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        ChatSessionEntity sessionEntity = chatSessionRepo.findById(sessionKey).orElse(null);
        if (sessionEntity == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        String filterEmail = sessionEntity.getRoom() != null ? email : null;
        Date date = searchModel.getDate() != null ? new Date(searchModel.getDate()) : new Date();
        int pageSize = null != searchModel.getPageSize() ? Math.min(100, searchModel.getPageSize()) : 30;
        Page<ChatRecordEntity> chatRecordEntities = complexChatRecordRepo.findByOrgIdAndSessionId(orgId, filterEmail, sessionKey, date, Pageable.ofSize(pageSize));
        for (ChatRecordEntity record : chatRecordEntities) {
            searchModel.getRowList().add(new InnerChatSearchModel.ChatRecordRow(email, record));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", searchModel);
    }

    @GetMapping(value = "/serviceUser")
    @ApiOperation(value = "客服列表", response = InnerChatUserModel.class)
    public ResponseModel getInnerServiceUser(InnerChatUserModel userModel) {
        List<UserStateEntity> userStateEntities = userStateRepo.findByOrgIdAndUserTypeOrderByNickName(getUser().getOrganizationId(), UserStateEntity.TYPE_NORMAL);
        if (CollectionUtils.isNotEmpty(userStateEntities)) {
            Integer roomId = userModel.getRoomId();
            if (roomId != null) { //排除已在群里的
                ChatRoomEntity chatRoomEntity = chatRoomRepo.findById(roomId).orElse(null);
                if (chatRoomEntity == null) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "获取群聊信息失败");
                }
                userStateEntities.removeAll(chatRoomEntity.getRoomUsers().stream().map(ChatRoomUsersEntity::getUser).collect(Collectors.toSet()));
            }
            //剔除自己
            userStateEntities.removeIf(u -> u.getEmail().equals(getUser().getEmail()));
            for (UserStateEntity userState : userStateEntities) {
                userModel.getMembers().add(new InnerServiceUserModel(userState));
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", userModel);
    }
}
