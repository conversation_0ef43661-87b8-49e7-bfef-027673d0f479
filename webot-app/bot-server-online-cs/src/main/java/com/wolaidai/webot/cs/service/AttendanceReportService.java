package com.wolaidai.webot.cs.service;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.wolaidai.webot.data.mysql.entity.report.AttendanceReportEntity;

public interface AttendanceReportService extends CommonReportService, TaskUnit {

    List<AttendanceReportEntity> generateAttendance(Integer orgId, Set<String> emails, Date startTime, Date endTime);

    void loadData(Integer orgId, Object model);
}
