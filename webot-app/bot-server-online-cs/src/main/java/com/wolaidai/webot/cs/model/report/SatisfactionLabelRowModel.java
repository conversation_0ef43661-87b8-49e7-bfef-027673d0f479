package com.wolaidai.webot.cs.model.report;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.elastic.entity.SatisfactionLabelReportEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "满意度标签统计结果")
public class SatisfactionLabelRowModel extends BaseModel {
    @ApiModelProperty(value = "标签")
    private String label;
    @ApiModelProperty(value = "标签数量")
    private Integer labelCount;
    @ApiModelProperty(value = "详细数据")
    private List<Pair> data = new ArrayList<>();

    public static class Pair {
        public String email;
        public String name = "";
        public int count;

        public Pair(String email, String name, int count) {
            this.email = email;
            this.name = name;
            this.count = count;
        }
    }

    public SatisfactionLabelRowModel(Integer orgId, SatisfactionLabelReportEntity sl, HashMap<String, UserInfo> infoMap) {
        this.label = sl.getLabel();
        this.labelCount = sl.getLabelCount();
        if (null != sl.getData()) {
            for (Map.Entry<String, Integer> entry : sl.getData().entrySet()) {
                UserInfo info = infoMap.get(orgId + entry.getKey());
                UserExtraField uef = null;
                if (null != info && null != (uef = info.getUserExtraField())) {
                    data.add(new Pair(entry.getKey(), uef.getNickName(), entry.getValue()));
                }
            }
        }
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getLabelCount() {
        return labelCount;
    }

    public void setLabelCount(Integer labelCount) {
        this.labelCount = labelCount;
    }

    public List<Pair> getData() {
        return data;
    }

    public void setData(List<Pair> data) {
        this.data = data;
    }

}
