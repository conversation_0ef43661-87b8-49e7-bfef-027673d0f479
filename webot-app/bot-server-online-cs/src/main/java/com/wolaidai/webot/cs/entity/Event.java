package com.wolaidai.webot.cs.entity;

import com.alibaba.fastjson.JSONObject;

import java.util.Date;

public class Event {
    //客户端发送的事件
    public static final String INQUEUE_KEY = "INQUEUE";//申请排队
    public static final String DEQUEUE_KEY = "DEQUEUE";//取消排队
    public static final String BEGIN_SESSION_KEY = "BEGIN_SESSION";//开始会话
    public static final String END_SESSION_KEY = "END_SESSION";//结束会话
    public static final String TRANS_SUCCESS_KEY = "TRANS_SUCCESS";//转接成功
    public static final String TRANS_REJECT_KEY = "TRANS_REJECT";//转接拒绝
    public static final String TRANS_TIMEOUT_KEY = "TRANS_TIMEOUT";//转接超时
    public static final String SERVICE_OFFLINE_KEY = "SERVICE_OFFLINE";//客服离线

    //服务端发送的事件
    public static final String QUEUING_KEY = "QUEUING";//开始排队
    public static final String QUEUE_TIMEOUT_KEY = "QUEUE_TIMEOUT";//排队超时
    public static final String QUEUE_DONE_KEY = "QUEUE_DONE";//已叫号
    public static final String CLOSE_SESSION_KEY = "CLOSE_SESSION";//结束会话
    public static final String TRANS_SESSION_KEY = "TRANS_SESSION";//转接会话
    public static final String USER_STATE_KEY = "USER_STATE";//用户状态更改
    public static final String SUMMARY_NOTIFY_KEY = "SUMMARY_NOTIFY";//提醒客服服务小结进度


    private String eventId;
    private String eventKey;
    private Integer orgId;
    private JSONObject content;
    private Date eventTime;

    public Event() {
    }

    public Event(String eventId, String eventKey, Integer orgId, JSONObject content, Date eventTime) {
        this.eventId = eventId;
        this.eventKey = eventKey;
        this.orgId = orgId;
        this.content = content;
        this.eventTime = eventTime;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEventKey() {
        return eventKey;
    }

    public void setEventKey(String eventKey) {
        this.eventKey = eventKey;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public JSONObject getContent() {
        return content;
    }

    public void setContent(JSONObject content) {
        this.content = content;
    }

    public Date getEventTime() {
        return eventTime;
    }

    public void setEventTime(Date eventTime) {
        this.eventTime = eventTime;
    }

}
