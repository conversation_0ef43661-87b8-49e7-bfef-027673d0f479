package com.wolaidai.webot.cs.controller;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.business.SearchBusinessTypeModel;
import com.wolaidai.webot.data.mysql.entity.common.SkillGroupEntity;
import com.wolaidai.webot.data.mysql.repo.SkillGroupRepo;
import com.wolaidai.webot.security.domain.UserDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "业务分组接口")
@RestController
@RequestMapping("/business")
public class BusinessGroupController extends BaseController {

    @Autowired
    private SkillGroupRepo skillGroupRepo;

    @GetMapping("/type")
    @ApiOperation(value = "获取业务类型", response = SearchBusinessTypeModel.class)
    public ResponseModel searchBusinessType(SearchBusinessTypeModel typeModel) {
        UserDomain user = getUser();
        List<SkillGroupEntity> groupEntities = skillGroupRepo.findByOrgId(user.getOrganizationId());
        for (SkillGroupEntity entity : groupEntities) {
            SearchBusinessTypeModel.BusinessTypeRow row = new SearchBusinessTypeModel.BusinessTypeRow();
            row.setBusinessId(entity.getId());
            row.setBusinessName(entity.getName());
            typeModel.getTypeRowList().add(row);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", typeModel);
    }

}
