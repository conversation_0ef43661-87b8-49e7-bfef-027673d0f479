package com.wolaidai.webot.cs.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.entity.Event;
import com.wolaidai.webot.cs.entity.EventProcessResult;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import com.wolaidai.webot.cs.service.MessageProcessService;
import com.wolaidai.webot.cs.service.MessageReceiverService;
import com.wolaidai.webot.data.mysql.entity.event.MessageEventEntity;
import com.wolaidai.webot.data.mysql.repo.MessageEventRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
public class MessageReceiverServiceImpl implements MessageReceiverService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private MessageEventRepo messageEventRepo;

    @Autowired
    private MessageProcessService messageProcessService;

    @Autowired
    private ThreadPoolTaskExecutor threadPool;

    @Autowired
    private CustomerInfoEncService customerInfoEncService;

    @Override
    @Async
    public void getMsg() {
        String msg;
        while(true){
            try {
                msg = redisTemplate.opsForList().leftPop(RedisKey.EVENT_CLIENT_KEY, 10, TimeUnit.SECONDS);
                if(msg==null){
                    continue;
                }
                Event event = JSONObject.parseObject(msg, Event.class);
                MessageEventEntity messageEvent = new MessageEventEntity();
                messageEvent.setEventId(event.getEventId());
                messageEvent.setContent(processCustomerInfo(event));
                messageEvent.setOrgId(event.getOrgId());
                messageEvent.setEventKey(event.getEventKey());
                messageEvent.setEventTime(event.getEventTime());
                messageEvent.setCreateTime(new Date());
                messageEventRepo.save(messageEvent);
                threadPool.execute(()->{
                    long start = System.currentTimeMillis();
                    LOGGER.info("开始执行消息处理,eventId:{},eventKey:{},activeCount:{},queueCount:{}",messageEvent.getEventId(),messageEvent.getEventKey(),threadPool.getActiveCount(),threadPool.getThreadPoolExecutor().getQueue().size());
                    EventProcessResult result = messageProcessService.process(event);
                    messageEvent.setStatus(MessageEventEntity.FINISH_STATUS);
                    if(!result.isSuccess()) {
                        messageEvent.setErrorMsg(result.getErrorMsg());
                    }
                    messageEvent.setUpdateTime(new Date());
                    long cost = System.currentTimeMillis()-start;
                    messageEvent.setCost(cost);
                    messageEvent.setDuration(messageEvent.getUpdateTime().getTime()-messageEvent.getCreateTime().getTime());
                    messageEventRepo.save(messageEvent);
                    LOGGER.info("结束执行消息处理,eventId:{},eventKey:{},cost:{}ms",messageEvent.getEventId(),messageEvent.getEventKey(),cost);
                });
            }catch (Exception e){
                LOGGER.error("获取消息异常",e);
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException ex) {
                }
            }
        }
    }

    /**
     * 处理用户敏感信息，只有cs接收到INQUEUE的事件时有用户敏感数据需要做加密
     * @param event
     * @return
     */
    private JSONObject processCustomerInfo(Event event) {
        if(!Event.INQUEUE_KEY.equals(event.getEventKey()) || event.getContent()==null){
            return event.getContent();
        }
        JSONObject content = JSONObject.parseObject(event.getContent().toJSONString());
        try {
            String account = content.getString("account");
            String encAccount = customerInfoEncService.encryptMobile(account);
            content.put("account",encAccount);

            JSONObject customerDetail = content.getJSONObject("customerDetail");
            if(customerDetail != null && customerDetail.containsKey("customers")){
                JSONArray customers = customerDetail.getJSONArray("customers");
                customerInfoEncService.encryptCustomerInfo(customers);
            }
        } catch (Exception e) {
            LOGGER.error("处理用户敏感信息异常, eventId: {}", event.getEventId(), e);
        }
        return content;
    }

}
