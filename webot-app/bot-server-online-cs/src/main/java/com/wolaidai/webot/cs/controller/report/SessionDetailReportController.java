package com.wolaidai.webot.cs.controller.report;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.report.SessionModel;
import com.wolaidai.webot.cs.service.SessionDetailReportService;
import com.wolaidai.webot.cs.service.TaskService;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.security.domain.UserDomain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "会话明细统计数据接口")
@RestController
@RequestMapping("/report")
public class SessionDetailReportController extends BaseController {

    @Autowired
    private SessionDetailReportService sessionDetailService;
    @Autowired
    private TaskService taskService;

    @GetMapping("/sessionDetail")
    @ApiOperation(value = "获取会话统计数据", response = SessionModel.class)
    public ResponseModel getStatisticsSession(SessionModel model) {
        if (null == model.getBeginDate() || null == model.getEndDate() || model.getEndDate().before(model.getBeginDate())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "查询开始时间或结束时间不合法");
        }
        sessionDetailService.loadData(getUser().getOrganizationId(), model);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/sessionDetail/export")
    @ApiOperation(value = "导出会话统计数据")
    public ResponseModel exportStatisticsSession(SessionModel model) {
        if (null == model.getBeginDate() || null == model.getEndDate() || model.getEndDate().before(model.getBeginDate())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "导出开始时间或结束时间不合法");
        }
        model.setPageNumber(-1);
        UserDomain user = getUser();
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.SESSION_DETAIL_REPORT, TaskEntity.PORT_EXPORT, model, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }
}
