package com.wolaidai.webot.cs.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionTransferListEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.model.UserStateDuration;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.TaskRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateHistoryRepo;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;

public class ReportUtil {
    private final static UserStateHistoryRepo userStateHistoryRepo;
    private final static UserRepo userRepo;
    private final static SessionListRepo sessionListRepo;
    private final static TaskRepo taskRepo;
    static {
        userStateHistoryRepo = SpringUtils.getApplicationContext().getBean(UserStateHistoryRepo.class);
        userRepo = SpringUtils.getApplicationContext().getBean(UserRepo.class);
        sessionListRepo = SpringUtils.getApplicationContext().getBean(SessionListRepo.class);
        taskRepo = SpringUtils.getApplicationContext().getBean(TaskRepo.class);
    }

    public static long getSessionDurationSeconds(String email, SessionListEntity session) {
        List<SessionTransferListEntity> list = session.getSessionTransferList().stream().filter(i -> Objects.equals(i.getStatus(), SessionTransferListEntity.STATUS_OK)).collect(Collectors.toList());
        list.sort(Comparator.comparing(SessionTransferListEntity::getUpdateTime));
        Date start = null;
        if (email.equalsIgnoreCase(session.getServiceUser())) {
            start = session.getCreateTime();
        }
        Date end = null;
        long duration = 0;
        for (SessionTransferListEntity st : list) {
            if (email.equalsIgnoreCase(st.getFromServiceUser())) {
                if (null != start && st.getUpdateTime().after(start)) {
                    duration += (st.getUpdateTime().getTime() - start.getTime());
                }
                end = st.getUpdateTime();
                start = null;
            }
            if (email.equalsIgnoreCase(st.getToServiceUser())) {
                start = st.getUpdateTime();
                end = null;
            }
        }
        if (email.equalsIgnoreCase(session.getLastServiceUser())) {
            end = session.getOfflineTime();
        }
        if (null != start && null != end && end.after(start)) {
            return (end.getTime() - start.getTime() + duration) / 1000;
        }
        return duration / 1000;
    }

    public static float getPercent(Number zi, Number mu) {
        if (null == zi || null == mu || mu.intValue() < 1) {
            return 0;
        }
        return BigDecimal.valueOf(zi.floatValue() * 100).divide(BigDecimal.valueOf(mu.floatValue()), 2, RoundingMode.HALF_UP).floatValue();
    }

    public static HashMap<Integer, HashMap<String, ArrayList<UserStateDuration>>> getUserStateDuration(Integer _orgId, Collection<String> emails, Date startTime, Date endTime) {
        List<UserStateDuration> list = null;
        if (null != _orgId && null != emails && emails.size() > 0) {
            list = userStateHistoryRepo.find(_orgId, emails, startTime, endTime);
        } else if (null != _orgId) {
            list = userStateHistoryRepo.find(_orgId, startTime, endTime);
        } else {
            list = userStateHistoryRepo.find(startTime, endTime);
        }
        HashMap<Integer, HashMap<String, ArrayList<UserStateDuration>>> map = new HashMap<>();
        for (UserStateDuration d : list) {
            Integer orgId = d.getOrgId();
            HashMap<String, ArrayList<UserStateDuration>> m = map.get(orgId);
            if (null == m) {
                m = new HashMap<>();
                map.put(orgId, m);
            }
            String email = d.getEmail();
            ArrayList<UserStateDuration> l = m.get(email);
            if (null == l) {
                l = new ArrayList<>();
                m.put(email, l);
            }
            l.add(d);
        }
        return map;
    }

    public static List<UserInfo> getUserInfoList(Integer orgId, Integer prodId, HashMap<String, UserInfo> infoMap, Set<String> emails) {
        List<UserInfo> list = new ArrayList<>();
        HashSet<String> set = new HashSet<>();
        if (null != emails && emails.size() > 0) {
            for (String email : emails) {
                String key = orgId + email;
                UserInfo info = infoMap.get(key);
                if (null == info) {
                    set.add(email);
                } else {
                    list.add(info);
                }
            }
        }
        if (set.size() > 0) {
            List<UserInfo> l = userRepo.find(orgId, prodId, set);
            if (l.size() > 0) {
                for (UserInfo info : l) {
                    infoMap.put(orgId + info.getEmail(), info);
                }
                list.addAll(l);
            }
        }
        return list;
    }

    public static List<SessionListEntity> getSessionList(boolean matchLastServiceUser, Integer orgId, Collection<String> emails, Date startTime, Date endTime) {
        List<SessionListEntity> list = null;
        if (null != orgId && null != emails && emails.size() > 0) {
            if (matchLastServiceUser) {
                list = sessionListRepo.findByOrgIdAndStatusAndLastServiceUserInAndCreateTimeGreaterThanEqualAndCreateTimeLessThanOrderByCreateTimeAsc(orgId, SessionListEntity.STATUS_OFFLINE, emails, startTime, endTime);
            } else {
                list = sessionListRepo.findByOrgIdAndStatusAndServiceUserInAndCreateTime(orgId, SessionListEntity.STATUS_OFFLINE, emails, startTime, endTime);
            }
        } else if (null != orgId) {
            list = sessionListRepo.findByOrgIdAndStatusAndCreateTimeGreaterThanEqualAndCreateTimeLessThanOrderByCreateTimeAsc(orgId, SessionListEntity.STATUS_OFFLINE, startTime, endTime);
        } else {
            list = sessionListRepo.findByStatusAndCreateTimeGreaterThanEqualAndCreateTimeLessThanOrderByCreateTimeAsc(SessionListEntity.STATUS_OFFLINE, startTime, endTime);
        }
        return list;
    }

    public static XSSFWorkbook convertToExcel(Map<String, JSONArray> data, Map<String, String> headerMap) {
        Collection<String> headers = headerMap.values();
        Collection<String> keys = headerMap.keySet();
        XSSFWorkbook sheets = new XSSFWorkbook();
        XSSFCellStyle style = sheets.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        if (null != data && data.size() > 0) {
            for (Map.Entry<String, JSONArray> entry : data.entrySet()) {
                XSSFSheet sheet = sheets.createSheet(entry.getKey());
                sheet.setDefaultColumnWidth(18);
                XSSFRow row = sheet.createRow(0);
                row.setRowStyle(style);
                int currentCell = 0;
                for (String header : headers) {
                    row.createCell(currentCell++).setCellValue(header);
                }
                JSONArray arr = entry.getValue();
                int rows = arr.size();
                for (int i = 1; i <= rows; i++) {
                    row = sheet.createRow(i);
                    row.setRowStyle(style);
                    currentCell = 0;
                    JSONObject json = arr.getJSONObject(i - 1);
                    for (String key : keys) {
                        row.createCell(currentCell++).setCellValue(String.valueOf(json.getOrDefault(key, "")));
                    }
                }
            }
        } else {
            XSSFSheet sheet = sheets.createSheet("Sheet1");
            sheet.setDefaultColumnWidth(18);
            XSSFRow row = sheet.createRow(0);
            row.setRowStyle(style);
            int currentCell = 0;
            for (String header : headers) {
                row.createCell(currentCell++).setCellValue(header);
            }
        }
        return sheets;
    }

    public static void saveToOss(XSSFWorkbook sheets, String ossBucketName, TaskEntity task) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        sheets.write(bos);
        saveToOss(new ByteArrayInputStream(bos.toByteArray()), ossBucketName, task);
    }

    public static void saveToOss(InputStream is, String ossBucketName, TaskEntity task) throws IOException {
        TaskEntity t = taskRepo.findByOrgIdAndId(task.getOrgId(), task.getId());
        if (null == t || Objects.equals(t.getStatus(), TaskEntity.SUCCESS_STATUS)) {
            if (null != t && !Objects.equals(t.getProgress(), 100)) {
                CommonUtil.saveTaskProgress(t, 100);
            }
            return;
        }
        String port = Objects.equals(TaskEntity.PORT_EXPORT, task.getPort()) ? "export" : "import";
        String fileDir = "cs/" + port + "/" + task.getType() + "/" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()) + "/";
        String name = task.getName();
        int i = null != name ? name.lastIndexOf('.') : -1;
        String extension = i > -1 ? name.substring(i) : "";
        String file = fileDir + UUID.randomUUID() + extension;
        task.setFileId(Base64.getEncoder().encodeToString(file.getBytes()));
        OssFileClient.putObject(ossBucketName, file, is);
        CommonUtil.saveTaskProgress(task, 100);
        is.close();
    }
}
