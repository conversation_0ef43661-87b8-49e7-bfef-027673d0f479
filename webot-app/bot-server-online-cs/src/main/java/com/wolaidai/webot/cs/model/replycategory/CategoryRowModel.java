package com.wolaidai.webot.cs.model.replycategory;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "快捷回复分类查询返回数据")
public class CategoryRowModel extends BaseModel {
    private Integer id;
    private String title;
    private Integer level;
    private List<CategoryRowModel> children = new ArrayList<>();
    @JsonIgnore
    private Integer position;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<CategoryRowModel> getChildren() {
        return children;
    }

    public void setChildren(List<CategoryRowModel> children) {
        this.children = children;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }
}
