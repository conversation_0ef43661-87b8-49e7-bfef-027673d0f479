package com.wolaidai.webot.cs.exception;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@RestControllerAdvice
public class ResponseExceptionHandler extends ResponseEntityExceptionHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResponseExceptionHandler.class);

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        LOGGER.error("参数错误", ex);
        String errMsg = ex.getMessage();
        BindingResult result = ex.getBindingResult();
        if (result != null && result.hasErrors()) {
            FieldError fieldError = result.getFieldError();
            errMsg = fieldError.getDefaultMessage();
        }
        return new ResponseEntity(new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, errMsg), HttpStatus.OK);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<Object> handleAccessDeniedException(AccessDeniedException ex, WebRequest request) {
        LOGGER.error("拒绝访问", ex);
        return new ResponseEntity(new ResponseModel(WebStatusConstants.RESPONSE_CODE_ACCESS_DENIED, "拒绝访问"), HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(Exception.class)
    public Object handleUnknownException(Exception ex, WebRequest request) {
        LOGGER.error("请求失败", ex);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "请求失败");
    }

}
