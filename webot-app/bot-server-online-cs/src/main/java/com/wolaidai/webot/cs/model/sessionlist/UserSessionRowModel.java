package com.wolaidai.webot.cs.model.sessionlist;

import java.util.Date;

import com.wolaidai.webot.cs.model.BaseModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "用户会话记录")
public class UserSessionRowModel extends BaseModel{

    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "会话开始时间")
    private Date startTime;
    @ApiModelProperty(value = "小结时间")
    private Date summaryTime;
    @ApiModelProperty(value = "服务类型")
    private String type;
    @ApiModelProperty(value = "最后接待客服的姓名")
    private String lastServiceUser;
    @ApiModelProperty(value = "客服工号")
    private String workNo;
    @ApiModelProperty(value = "话务组")
    private String group;
    @ApiModelProperty(value = "小结")
    private String summary;
    @ApiModelProperty(value = "小结备注")
    private String summaryRemark;
    @ApiModelProperty(value = "ai小结")
    private String aiSummary;

    public String getMobile() {
        return mobile;
    }
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public Date getStartTime() {
        return startTime;
    }
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    public Date getSummaryTime() {
        return summaryTime;
    }
    public void setSummaryTime(Date summaryTime) {
        this.summaryTime = summaryTime;
    }
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
    public String getLastServiceUser() {
        return lastServiceUser;
    }
    public void setLastServiceUser(String lastServiceUser) {
        this.lastServiceUser = lastServiceUser;
    }
    public String getWorkNo() {
        return workNo;
    }
    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }
    public String getGroup() {
        return group;
    }
    public void setGroup(String group) {
        this.group = group;
    }
    public String getSummary() {
        return summary;
    }
    public void setSummary(String summary) {
        this.summary = summary;
    }
    public String getSummaryRemark() {
        return summaryRemark;
    }
    public void setSummaryRemark(String summaryRemark) {
        this.summaryRemark = summaryRemark;
    }

    public String getAiSummary() {
        return aiSummary;
    }

    public void setAiSummary(String aiSummary) {
        this.aiSummary = aiSummary;
    }
}
