package com.wolaidai.webot.cs.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties("app")
public class AppPropertyConfig {

    //是否测试环境
    private boolean envTest;

    private Integer productId;
    //文件服务器地址
    private String fileServerUrl;
    private String customerInfoUrl;
    private String ipInfoUrl;
    private String faceDocumentsUrl;

    private String baseFileDir;
    private String ossEndpoint;
    private String ossAccessKeyID;
    private String ossAccessKeySecret;
    private String ossBucketName;

    private String aliAccessKeyId;
    private String aliAccessKeySecret;
    private String aliAppKey;

    private Integer taskCorePool = 8;
    private Integer taskMaxPool = 16;
    private Integer taskQueueCapacity = 100;

    private Integer checkMsgFlag = 0;

    private Integer faceLinkIntervalMinutes = 3;
    private Integer maxFaceDetectFailureTimes = 5;
    private Integer faceTokenTimeoutMinutes = 10;
    private String faceDetectionUrl;
    private String apiAuthUrl;
    private String summaryWebhookUrl;


    //针对测试临时使用
    private String whiteEmail = "<EMAIL>";
    private String whiteMobile = "18588210641";

    public boolean isEnvTest() {
        return envTest;
    }

    public void setEnvTest(boolean envTest) {
        this.envTest = envTest;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getFileServerUrl() {
        return fileServerUrl;
    }

    public void setFileServerUrl(String fileServerUrl) {
        this.fileServerUrl = fileServerUrl;
    }

    public String getCustomerInfoUrl() {
        return customerInfoUrl;
    }

    public void setCustomerInfoUrl(String customerInfoUrl) {
        this.customerInfoUrl = customerInfoUrl;
    }

    public String getIpInfoUrl() {
        return ipInfoUrl;
    }

    public void setIpInfoUrl(String ipInfoUrl) {
        this.ipInfoUrl = ipInfoUrl;
    }

    public String getFaceDocumentsUrl() {
        return faceDocumentsUrl;
    }

    public void setFaceDocumentsUrl(String faceDocumentsUrl) {
        this.faceDocumentsUrl = faceDocumentsUrl;
    }

    public String getBaseFileDir() {
        return baseFileDir;
    }

    public void setBaseFileDir(String baseFileDir) {
        this.baseFileDir = baseFileDir;
    }

    public String getOssEndpoint() {
        return ossEndpoint;
    }

    public void setOssEndpoint(String ossEndpoint) {
        this.ossEndpoint = ossEndpoint;
    }

    public String getOssAccessKeyID() {
        return ossAccessKeyID;
    }

    public void setOssAccessKeyID(String ossAccessKeyID) {
        this.ossAccessKeyID = ossAccessKeyID;
    }

    public String getOssAccessKeySecret() {
        return ossAccessKeySecret;
    }

    public void setOssAccessKeySecret(String ossAccessKeySecret) {
        this.ossAccessKeySecret = ossAccessKeySecret;
    }

    public String getOssBucketName() {
        return ossBucketName;
    }

    public void setOssBucketName(String ossBucketName) {
        this.ossBucketName = ossBucketName;
    }

    public String getAliAccessKeyId() {
        return aliAccessKeyId;
    }

    public void setAliAccessKeyId(String aliAccessKeyId) {
        this.aliAccessKeyId = aliAccessKeyId;
    }

    public String getAliAccessKeySecret() {
        return aliAccessKeySecret;
    }

    public void setAliAccessKeySecret(String aliAccessKeySecret) {
        this.aliAccessKeySecret = aliAccessKeySecret;
    }

    public String getAliAppKey() {
        return aliAppKey;
    }

    public void setAliAppKey(String aliAppKey) {
        this.aliAppKey = aliAppKey;
    }

    public Integer getTaskCorePool() {
        return taskCorePool;
    }

    public void setTaskCorePool(Integer taskCorePool) {
        this.taskCorePool = taskCorePool;
    }

    public Integer getTaskMaxPool() {
        return taskMaxPool;
    }

    public void setTaskMaxPool(Integer taskMaxPool) {
        this.taskMaxPool = taskMaxPool;
    }

    public Integer getTaskQueueCapacity() {
        return taskQueueCapacity;
    }

    public void setTaskQueueCapacity(Integer taskQueueCapacity) {
        this.taskQueueCapacity = taskQueueCapacity;
    }

    public Integer getCheckMsgFlag() {
        return checkMsgFlag;
    }

    public void setCheckMsgFlag(Integer checkMsgFlag) {
        this.checkMsgFlag = checkMsgFlag;
    }

    public String getWhiteEmail() {
        return whiteEmail;
    }

    public void setWhiteEmail(String whiteEmail) {
        this.whiteEmail = whiteEmail;
    }

    public String getWhiteMobile() {
        return whiteMobile;
    }

    public void setWhiteMobile(String whiteMobile) {
        this.whiteMobile = whiteMobile;
    }

    public Integer getFaceLinkIntervalMinutes() {
        return faceLinkIntervalMinutes;
    }

    public void setFaceLinkIntervalMinutes(Integer faceLinkIntervalMinutes) {
        this.faceLinkIntervalMinutes = faceLinkIntervalMinutes;
    }

    public Integer getMaxFaceDetectFailureTimes() {
        return maxFaceDetectFailureTimes;
    }

    public void setMaxFaceDetectFailureTimes(Integer maxFaceDetectFailureTimes) {
        this.maxFaceDetectFailureTimes = maxFaceDetectFailureTimes;
    }

    public Integer getFaceTokenTimeoutMinutes() {
        return faceTokenTimeoutMinutes;
    }

    public void setFaceTokenTimeoutMinutes(Integer faceTokenTimeoutMinutes) {
        this.faceTokenTimeoutMinutes = faceTokenTimeoutMinutes;
    }

    public String getFaceDetectionUrl() {
        return faceDetectionUrl;
    }

    public void setFaceDetectionUrl(String faceDetectionUrl) {
        this.faceDetectionUrl = faceDetectionUrl;
    }

    public String getApiAuthUrl() {
        return apiAuthUrl;
    }

    public void setApiAuthUrl(String apiAuthUrl) {
        this.apiAuthUrl = apiAuthUrl;
    }

    public String getSummaryWebhookUrl() {
        return summaryWebhookUrl;
    }

    public void setSummaryWebhookUrl(String summaryWebhookUrl) {
        this.summaryWebhookUrl = summaryWebhookUrl;
    }
}
