package com.wolaidai.webot.cs.model.sessionlist;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = "客服历史会话返回数据")
public class StaffHistorySessionRowModel extends BaseModel {

    private Integer id;
    @ApiModelProperty(value = "业务名")
    private String businessName;
    private String customerName;
    @ApiModelProperty(value = "uuid")
    private String uuid;
    @ApiModelProperty(value = "userId")
    private Integer userId;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "会话标识:1-已标识,0-未标识")
    private Integer mark;
    @ApiModelProperty(value = "服务总结状态:1-已总结,0-未总结")
    private Integer serviceSummaryStatus = 0;
    @ApiModelProperty(value = "是否参评:1-已参评,0-未下发,-1-下发未参评")
    private Integer appraiseStatus = 0;
    @ApiModelProperty(value = "评价等级")
    private Integer appraiseLevel;
    private Date startTime;
    @ApiModelProperty(value = "咨询渠道")
    private Integer clientId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Integer getMark() {
        return mark;
    }

    public void setMark(Integer mark) {
        this.mark = mark;
    }

    public Integer getServiceSummaryStatus() {
        return serviceSummaryStatus;
    }

    public void setServiceSummaryStatus(Integer serviceSummaryStatus) {
        this.serviceSummaryStatus = serviceSummaryStatus;
    }

    public Integer getAppraiseStatus() {
        return appraiseStatus;
    }

    public void setAppraiseStatus(Integer appraiseStatus) {
        this.appraiseStatus = appraiseStatus;
    }

    public Integer getAppraiseLevel() {
        return appraiseLevel;
    }

    public void setAppraiseLevel(Integer appraiseLevel) {
        this.appraiseLevel = appraiseLevel;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Integer getClientId() {
        return clientId;
    }

    public void setClientId(Integer clientId) {
        this.clientId = clientId;
    }
}
