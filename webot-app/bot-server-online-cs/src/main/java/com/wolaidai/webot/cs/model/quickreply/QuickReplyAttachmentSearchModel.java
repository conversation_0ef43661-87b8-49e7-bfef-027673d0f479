package com.wolaidai.webot.cs.model.quickreply;

import com.wolaidai.webot.cs.model.PageableModel;

import java.util.ArrayList;
import java.util.List;

public class QuickReplyAttachmentSearchModel extends PageableModel {

    private String key;
    private Integer categoryId = -1;
    private List<QuickReplyAttachmentRowModel> list = new ArrayList<>();

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public List<QuickReplyAttachmentRowModel> getList() {
        return list;
    }

    public void setList(List<QuickReplyAttachmentRowModel> list) {
        this.list = list;
    }
}
