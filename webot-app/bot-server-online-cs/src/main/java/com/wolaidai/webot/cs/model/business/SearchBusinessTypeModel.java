package com.wolaidai.webot.cs.model.business;

import com.wolaidai.webot.cs.model.BaseModel;

import java.util.ArrayList;
import java.util.List;

public class SearchBusinessTypeModel extends BaseModel {

    private List<BusinessTypeRow> typeRowList = new ArrayList<>();

    public List<BusinessTypeRow> getTypeRowList() {
        return typeRowList;
    }

    public void setTypeRowList(List<BusinessTypeRow> typeRowList) {
        this.typeRowList = typeRowList;
    }

    public static class BusinessTypeRow {

        private Integer businessId;
        private String businessName;

        public Integer getBusinessId() {
            return businessId;
        }

        public void setBusinessId(Integer businessId) {
            this.businessId = businessId;
        }

        public String getBusinessName() {
            return businessName;
        }

        public void setBusinessName(String businessName) {
            this.businessName = businessName;
        }
    }

}
