package com.wolaidai.webot.cs.model.innerchat;

import com.wolaidai.webot.cs.model.PageableModel;

import java.util.ArrayList;
import java.util.List;

public class InnerChatUserModel extends PageableModel {

    private Integer roomId;
    private List<InnerServiceUserModel> members = new ArrayList<>();

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public List<InnerServiceUserModel> getMembers() {
        return members;
    }

    public void setMembers(List<InnerServiceUserModel> members) {
        this.members = members;
    }
}
