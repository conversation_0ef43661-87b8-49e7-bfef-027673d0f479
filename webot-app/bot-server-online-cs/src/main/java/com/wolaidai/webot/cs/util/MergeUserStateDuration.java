package com.wolaidai.webot.cs.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;

import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.model.UserStateDuration;

public class MergeUserStateDuration {
    public Date firstLogin;
    public Date lastLogout;
    public Integer totalLogin = 0;
    public Integer totalOnline = 0;
    public Integer totalBusy = 0;
    public Integer totalRest = 0;
    public Integer totalLeave = 0;
    public Integer totalEat = 0;
    public Integer totalStudy = 0;

    public MergeUserStateDuration(ArrayList<UserStateDuration> l) {
        if (null == l) {
            return;
        }
        for (UserStateDuration usd : l) {
            Integer state = usd.getState();
            Integer seconds = usd.getSeconds();
            if (null == seconds) {
                seconds = 0;
            }
            if (Objects.equals(UserStateEntity.STATE_LOGIN, state)) {
                if (null == firstLogin) {
                    firstLogin = usd.getCreateTime();
                }
            } else if (Objects.equals(UserStateEntity.STATE_ONLINE, state)) {
                totalOnline += seconds;
            } else if (Objects.equals(UserStateEntity.STATE_BUSY, state)) {
                totalBusy += seconds;
            } else if (Objects.equals(UserStateEntity.STATE_RESTING, state)) {
                totalRest += seconds;
            } else if (Objects.equals(UserStateEntity.STATE_LEAVE, state)) {
                totalLeave += seconds;
            } else if (Objects.equals(UserStateEntity.STATE_STUDYING, state)) {
                totalStudy += seconds;
            } else if (Objects.equals(UserStateEntity.STATE_EATING, state)) {
                totalEat += seconds;
            } else if (Objects.equals(UserStateEntity.STATE_OFFLINE, state)) {
                lastLogout = usd.getCreateTime();
            }
            if (!Objects.equals(UserStateEntity.STATE_OFFLINE, state)) {
                totalLogin += seconds;
            }
        }
    }
}
