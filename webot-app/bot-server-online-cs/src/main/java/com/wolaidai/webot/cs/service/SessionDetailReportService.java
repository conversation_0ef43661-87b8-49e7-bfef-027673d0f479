package com.wolaidai.webot.cs.service;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.wolaidai.webot.data.mysql.entity.report.SessionDetailReportEntity;

public interface SessionDetailReportService extends CommonReportService, TaskUnit {

    List<SessionDetailReportEntity> generateSession(Integer orgId, Set<String> emails, Date startTime, Date endTime);

    void loadData(Integer orgId, Object model);
}
