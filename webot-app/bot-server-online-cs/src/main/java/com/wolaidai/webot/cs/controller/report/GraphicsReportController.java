package com.wolaidai.webot.cs.controller.report;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.report.LineDataSearchModel;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.elastic.repo.ComplexSessionListElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.security.domain.UserDomain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "图报表接口")
@RestController
@RequestMapping("/graphicsReport")
public class GraphicsReportController extends BaseController {

    @Autowired
    private ComplexSessionListElasticRepo sessionListElasticRepo;

    @GetMapping("/lineData")
    @ApiOperation(value = "线性图报表")
    public ResponseModel lineReport(LineDataSearchModel model) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);
        Date endDate = DateUtils.addDays(today, 1);
        switch (model.getType()) {
        case LineDataSearchModel.TYPE_PERSONAL_SESSION: {
            List<SessionListEntity> sessions = ReportUtil.getSessionList(false, orgId, Arrays.asList(email), today, endDate);
            Set<Integer> ids = sessions.stream().filter(i -> Objects.equals(i.getStatus(), SessionListEntity.STATUS_OFFLINE)).map(i -> i.getId()).collect(Collectors.toSet());
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", sessionListElasticRepo.offlineSessionCountReport(orgId, ids, today, endDate, model.getInterval()));
        }
        default:
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "类型错误");
        }
    }

}
