package com.wolaidai.webot.cs.model.sessionlist.personal;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@ApiModel(description = "个人聊天历史记录返回数据")
public class PersonalSessionRowModel extends BaseModel {

    @ApiModelProperty(value = "会话ID")
    private Integer id;
    @ApiModelProperty(value = "客户姓名")
    private String customerName;
    @ApiModelProperty(value = "会话开始时间")
    private Date createTime;
    @ApiModelProperty(value = "会话持续时间")
    private String durationTime;
    @ApiModelProperty(value = "机器人回复数")
    private Long botReply;
    @ApiModelProperty(value = "人工回复数")
    private Long artificialReply;
    @ApiModelProperty(value = "用户回复数")
    private Long userReply;
    @ApiModelProperty(value = "客户额外属性")
    private Map<String,Object> extraProperties = new HashMap<>();
    @ApiModelProperty(value = "最后接待客服")
    private String lastCustomerService;
    @ApiModelProperty(value = "业务名")
    private String businessName;
    @ApiModelProperty(value = "服务小结")
    private String sessionSummaryRemark;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(String durationTime) {
        this.durationTime = durationTime;
    }

    public Long getBotReply() {
        return botReply;
    }

    public void setBotReply(Long botReply) {
        this.botReply = botReply;
    }

    public Long getArtificialReply() {
        return artificialReply;
    }

    public void setArtificialReply(Long artificialReply) {
        this.artificialReply = artificialReply;
    }

    public Long getUserReply() {
        return userReply;
    }

    public void setUserReply(Long userReply) {
        this.userReply = userReply;
    }

    public Map<String, Object> getExtraProperties() {
        return extraProperties;
    }

    public void setExtraProperties(Map<String, Object> extraProperties) {
        this.extraProperties = extraProperties;
    }

    public String getLastCustomerService() {
        return lastCustomerService;
    }

    public void setLastCustomerService(String lastCustomerService) {
        this.lastCustomerService = lastCustomerService;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getSessionSummaryRemark() {
        return sessionSummaryRemark;
    }

    public void setSessionSummaryRemark(String sessionSummaryRemark) {
        this.sessionSummaryRemark = sessionSummaryRemark;
    }
}
