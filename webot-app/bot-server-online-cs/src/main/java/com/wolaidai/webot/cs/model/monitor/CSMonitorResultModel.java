package com.wolaidai.webot.cs.model.monitor;

import com.wolaidai.webot.cs.model.BaseModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "客服在线监控返回")
public class CSMonitorResultModel extends BaseModel {

    @ApiModelProperty(value = "已完成会话数")
    private Integer sessionCount = 0;
    @ApiModelProperty(value = "登录总时长")
    private String loginDuration = "00:00:00";
    @ApiModelProperty(value = "平均响应时长")
    private String responseTimeAvg = "00:00:00";
    @ApiModelProperty(value = "平均接待时长")
    private String sessionDurationAvg = "00:00:00";
    @ApiModelProperty(value = "满意度")
    private String satisfactionPercent = "0%";
    @ApiModelProperty(value = "服务小结完成占比")
    private String serviceSummaryPercent = "0%";

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public String getLoginDuration() {
        return loginDuration;
    }

    public void setLoginDuration(String loginDuration) {
        this.loginDuration = loginDuration;
    }

    public String getResponseTimeAvg() {
        return responseTimeAvg;
    }

    public void setResponseTimeAvg(String responseTimeAvg) {
        this.responseTimeAvg = responseTimeAvg;
    }

    public String getSessionDurationAvg() {
        return sessionDurationAvg;
    }

    public void setSessionDurationAvg(String sessionDurationAvg) {
        this.sessionDurationAvg = sessionDurationAvg;
    }

    public String getSatisfactionPercent() {
        return satisfactionPercent;
    }

    public void setSatisfactionPercent(String satisfactionPercent) {
        this.satisfactionPercent = satisfactionPercent;
    }

    public String getServiceSummaryPercent() {
        return serviceSummaryPercent;
    }

    public void setServiceSummaryPercent(String serviceSummaryPercent) {
        this.serviceSummaryPercent = serviceSummaryPercent;
    }

}
