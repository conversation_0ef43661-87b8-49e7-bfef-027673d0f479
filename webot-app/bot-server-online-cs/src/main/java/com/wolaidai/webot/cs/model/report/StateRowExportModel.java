package com.wolaidai.webot.cs.model.report;

import java.util.Date;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.model.BaseModel;

public class StateRowExportModel extends BaseModel {

    private String email = "";
    private String date = "";
    private String workNumber = "";
    private String nickName = "";
    private String onlineStart = "";
    private String onlineEnd = "";
    private String onlineDuration = "";
    private String busyStart = "";
    private String busyEnd = "";
    private String busyDuration = "";
    private String restStart = "";
    private String restEnd = "";
    private String restDuration = "";
    private String leaveStart = "";
    private String leaveEnd = "";
    private String leaveDuration = "";
    private String eatStart = "";
    private String eatEnd = "";
    private String eatDuration = "";
    private String studyStart = "";
    private String studyEnd = "";
    private String studyDuration = "";
    private Date createTime;
    
    public StateRowExportModel(StateRowModel m, int i) {
        this.email = m.getEmail();
        this.date = m.getDate();
        this.workNumber = m.getWorkNumber();
        this.nickName = m.getNickName();
        JSONArray online = m.getOnlineData();
        if (null != online && online.size() > i) {
            JSONObject json = online.getJSONObject(i);
            this.onlineStart = json.getString("begin");
            this.onlineEnd = json.getString("end");
            this.onlineDuration = json.getString("seconds");
        }
        JSONArray busy = m.getBusyData();
        if (null != busy && busy.size() > i) {
            JSONObject json = busy.getJSONObject(i);
            this.busyStart = json.getString("begin");
            this.busyEnd = json.getString("end");
            this.busyDuration = json.getString("seconds");
        }
        JSONArray rest = m.getRestData();
        if (null != rest && rest.size() > i) {
            JSONObject json = rest.getJSONObject(i);
            this.restStart = json.getString("begin");
            this.restEnd = json.getString("end");
            this.restDuration = json.getString("seconds");
        }
        JSONArray leave = m.getLeaveData();
        if (null != leave && leave.size() > i) {
            JSONObject json = leave.getJSONObject(i);
            this.leaveStart = json.getString("begin");
            this.leaveEnd = json.getString("end");
            this.leaveDuration = json.getString("seconds");
        }
        JSONArray eat = m.getEatData();
        if (null != eat && eat.size() > i) {
            JSONObject json = eat.getJSONObject(i);
            this.eatStart = json.getString("begin");
            this.eatEnd = json.getString("end");
            this.eatDuration = json.getString("seconds");
        }
        JSONArray study = m.getStudyData();
        if (null != study && study.size() > i) {
            JSONObject json = study.getJSONObject(i);
            this.studyStart = json.getString("begin");
            this.studyEnd = json.getString("end");
            this.studyDuration = json.getString("seconds");
        }
        this.createTime = m.getCreateTime();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getOnlineStart() {
        return onlineStart;
    }

    public void setOnlineStart(String onlineStart) {
        this.onlineStart = onlineStart;
    }

    public String getOnlineEnd() {
        return onlineEnd;
    }

    public void setOnlineEnd(String onlineEnd) {
        this.onlineEnd = onlineEnd;
    }

    public String getOnlineDuration() {
        return onlineDuration;
    }

    public void setOnlineDuration(String onlineDuration) {
        this.onlineDuration = onlineDuration;
    }

    public String getBusyStart() {
        return busyStart;
    }

    public void setBusyStart(String busyStart) {
        this.busyStart = busyStart;
    }

    public String getBusyEnd() {
        return busyEnd;
    }

    public void setBusyEnd(String busyEnd) {
        this.busyEnd = busyEnd;
    }

    public String getBusyDuration() {
        return busyDuration;
    }

    public void setBusyDuration(String busyDuration) {
        this.busyDuration = busyDuration;
    }

    public String getRestStart() {
        return restStart;
    }

    public void setRestStart(String restStart) {
        this.restStart = restStart;
    }

    public String getRestEnd() {
        return restEnd;
    }

    public void setRestEnd(String restEnd) {
        this.restEnd = restEnd;
    }

    public String getRestDuration() {
        return restDuration;
    }

    public void setRestDuration(String restDuration) {
        this.restDuration = restDuration;
    }

    public String getLeaveStart() {
        return leaveStart;
    }

    public void setLeaveStart(String leaveStart) {
        this.leaveStart = leaveStart;
    }

    public String getLeaveEnd() {
        return leaveEnd;
    }

    public void setLeaveEnd(String leaveEnd) {
        this.leaveEnd = leaveEnd;
    }

    public String getLeaveDuration() {
        return leaveDuration;
    }

    public void setLeaveDuration(String leaveDuration) {
        this.leaveDuration = leaveDuration;
    }

    public String getEatStart() {
        return eatStart;
    }

    public void setEatStart(String eatStart) {
        this.eatStart = eatStart;
    }

    public String getEatEnd() {
        return eatEnd;
    }

    public void setEatEnd(String eatEnd) {
        this.eatEnd = eatEnd;
    }

    public String getEatDuration() {
        return eatDuration;
    }

    public void setEatDuration(String eatDuration) {
        this.eatDuration = eatDuration;
    }

    public String getStudyStart() {
        return studyStart;
    }

    public void setStudyStart(String studyStart) {
        this.studyStart = studyStart;
    }

    public String getStudyEnd() {
        return studyEnd;
    }

    public void setStudyEnd(String studyEnd) {
        this.studyEnd = studyEnd;
    }

    public String getStudyDuration() {
        return studyDuration;
    }

    public void setStudyDuration(String studyDuration) {
        this.studyDuration = studyDuration;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
