package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

public class BusTypeCollectOpModel extends BaseModel {

    @ApiModelProperty(value = "操作类型,1-删除/2-排序(此值时,summaryKeys不为空)", allowableValues = "1,2")
    private Integer opType;
    private List<String> summaryKeys = new ArrayList<>();

    public Integer getOpType() {
        return opType;
    }

    public void setOpType(Integer opType) {
        this.opType = opType;
    }

    public List<String> getSummaryKeys() {
        return summaryKeys;
    }

    public void setSummaryKeys(List<String> summaryKeys) {
        this.summaryKeys = summaryKeys;
    }
}
