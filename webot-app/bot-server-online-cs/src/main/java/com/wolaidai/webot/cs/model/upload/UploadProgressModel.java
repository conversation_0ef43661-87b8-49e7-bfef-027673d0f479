package com.wolaidai.webot.cs.model.upload;


import com.wolaidai.webot.cs.model.BaseModel;

public class UploadProgressModel extends BaseModel {

    private double percent;
    private long progress;
    private long total;
    private long repeatCnt;
    private String errMsg;

    public double getPercent() {
        return percent;
    }

    public void setPercent(double percent) {
        this.percent = percent;
    }

    public long getProgress() {
        return progress;
    }

    public void setProgress(long progress) {
        this.progress = progress;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public long getRepeatCnt() {
        return repeatCnt;
    }

    public void setRepeatCnt(long repeatCnt) {
        this.repeatCnt = repeatCnt;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
