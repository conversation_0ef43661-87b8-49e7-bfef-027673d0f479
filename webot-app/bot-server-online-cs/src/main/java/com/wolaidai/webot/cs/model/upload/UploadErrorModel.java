package com.wolaidai.webot.cs.model.upload;


import com.wolaidai.webot.cs.model.BaseModel;

public class UploadErrorModel extends BaseModel {

    public static final int QUICK_REPLY_TYPE = 1;
    public static final int BUSINESS_UNIT_TYPE = 2;
    public static final int UNKNOWN_TYPE = -1;

    private int type;
    private String sheetName;
    private int index;
    private String content;
    private String error;

    public UploadErrorModel() {
    }

    public UploadErrorModel(int type, String sheetName, int index, String content, String error) {
        this.type = type;
        this.sheetName = sheetName;
        this.index = index;
        this.content = content;
        this.error = error;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }
}
