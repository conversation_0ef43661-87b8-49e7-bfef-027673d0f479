package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.elastic.entity.ChatHistoryElasticEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ChatRecordEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class InnerChatSearchModel extends BaseModel {

    private Long date;
    private Integer pageSize = 30;

    private List<ChatRecordRow> rowList = new ArrayList<>();

    public Long getDate() {
        return date;
    }

    public void setDate(Long date) {
        this.date = date;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<ChatRecordRow> getRowList() {
        return rowList;
    }

    public void setRowList(List<ChatRecordRow> rowList) {
        this.rowList = rowList;
    }

    public static class ChatRecordRow {
        private String sender;
        private String msgId;
        private String type;
        private String content;
        private Date date;
        private Map<String, Object> media;
        private Map<String, Object> manual;

        public String getSender() {
            return sender;
        }

        public void setSender(String sender) {
            this.sender = sender;
        }

        public String getMsgId() {
            return msgId;
        }

        public void setMsgId(String msgId) {
            this.msgId = msgId;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }

        public Map<String, Object> getMedia() {
            return media;
        }

        public void setMedia(Map<String, Object> media) {
            this.media = media;
        }

        public Map<String, Object> getManual() {
            return manual;
        }

        public void setManual(Map<String, Object> manual) {
            this.manual = manual;
        }

        public ChatRecordRow() {
        }

        public ChatRecordRow(String myself, ChatRecordEntity entity) {
            String fromEmail = entity.getFromEmail();
            if (myself.equals(fromEmail)) {
                this.sender = ChatHistoryElasticEntity.SENDER_TYPE_MANUAL;
            } else {
                this.sender = ChatHistoryElasticEntity.SENDER_TYPE_USER;
            }
            this.msgId = entity.getMsgId();
            this.type = entity.getType();
            this.content = entity.getContent();
            this.date = entity.getCreateTime();
            this.manual = entity.getManual();
            this.media = entity.getMedia();
        }
    }
}
