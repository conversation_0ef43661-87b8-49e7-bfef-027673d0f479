package com.wolaidai.webot.cs.model.userstate;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "用户状态查询")
public class UserStateSearchModel extends BaseModel {
    private List<UserStateRowModel> list = new ArrayList<>();

    public List<UserStateRowModel> getList() {
        return list;
    }

    public void setList(List<UserStateRowModel> list) {
        this.list = list;
    }
}
