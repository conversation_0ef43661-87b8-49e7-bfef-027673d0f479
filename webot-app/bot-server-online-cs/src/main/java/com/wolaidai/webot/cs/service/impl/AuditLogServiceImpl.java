package com.wolaidai.webot.cs.service.impl;

import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.service.AuditLogService;
import com.wolaidai.webot.data.mysql.entity.audit.AuditLogEntity;
import com.wolaidai.webot.data.mysql.enums.AuditAction;
import com.wolaidai.webot.data.mysql.enums.AuditModule;
import com.wolaidai.webot.data.mysql.repo.AuditLogRepo;
import com.wolaidai.webot.security.domain.UserDomain;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class AuditLogServiceImpl implements AuditLogService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppPropertyConfig appPropertyConfig;

    @Autowired
    private AuditLogRepo auditLogRepo;

    @Override
    public void auditLog(UserDomain user, AuditAction action, AuditModule module, Object sourceId, String path, String ip, String remark, String supportUsername) {
        if (user == null) {
            LOGGER.error("user is null when save audit log...");
            return;
        }
        LOGGER.info("save auditLog,user:{},action:{},module:{},sourceId:{},path:{},ip:{},remark:{}", user.getEmail(), action, module, sourceId, path, ip, remark);
        AuditLogEntity auditLogEntity = new AuditLogEntity();
        auditLogEntity.setUsername(user.getEmail());
        auditLogEntity.setOrgId(user.getOrganizationId());
        auditLogEntity.setProductId(appPropertyConfig.getProductId());
        auditLogEntity.setAction(action);
        auditLogEntity.setModule(module);
        if (sourceId != null) {
            auditLogEntity.setSourceId(sourceId.toString());
        }
        auditLogEntity.setRemark(remark);
        auditLogEntity.setSupportUsername(supportUsername);
        auditLogEntity.setPath(path);
        auditLogEntity.setIp(ip);
        auditLogEntity.setCreateTime(new Date());
        auditLogRepo.save(auditLogEntity);
    }
}
