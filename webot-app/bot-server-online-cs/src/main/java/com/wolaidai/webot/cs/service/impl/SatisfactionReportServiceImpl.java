package com.wolaidai.webot.cs.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.model.report.ReportReqModel;
import com.wolaidai.webot.cs.model.report.SatisfactionLabelModel;
import com.wolaidai.webot.cs.model.report.SatisfactionLabelRowModel;
import com.wolaidai.webot.cs.model.report.SatisfactionLabelRowModel.Pair;
import com.wolaidai.webot.cs.service.SatisfactionReportService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.elastic.entity.SatisfactionLabelReportEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexSatisfactionElasticRepo;
import com.wolaidai.webot.data.mysql.entity.report.SatisfactionReportEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.repo.SatisfactionDataRepo;
import com.wolaidai.webot.data.mysql.repo.SatisfactionReportRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeAnalyzeEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity;

@Service
public class SatisfactionReportServiceImpl implements SatisfactionReportService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SessionReportServiceImpl.class);

    @Autowired
    private SessionListRepo sessionListRepo;

    @Autowired
    private SatisfactionDataRepo satisfactionDataRepo;

    @Autowired
    private SatisfactionReportRepo satisfactionReportRepo;

    @Autowired
    protected AppPropertyConfig appPropertyConfig;

    @Autowired
    private ComplexSatisfactionElasticRepo satisfactionElasticRepo;

    @Override
    public void generateReportByDate(Integer orgId, Date startTime, Date endTime) {
        if (startTime.after(endTime)) {
            LOGGER.error("generate satisfaction report fail, startTime can't greater than endTime");
            return;
        }

        try {
            List<SatisfactionReportEntity> reportList = getSatisfactionInfoList(orgId, startTime, endTime);
            satisfactionReportRepo.deleteAllByOrgIdAndDateGreaterThanEqualAndDateLessThanEqual(orgId, startTime, endTime);
            if (reportList != null && reportList.size() > 0) {
                satisfactionReportRepo.saveAll(reportList);
            }
            LOGGER.info("generate satisfaction report success, orgId:{}, startTime:{}, endTime:{}", orgId, startTime, endTime);
        } catch (Exception e) {
            LOGGER.error("generate satisfaction report occur error, error:" + e.getMessage());
        }
    }

    @Override
    public String getReportName() {
        return "满意度";
    }

    @Override
    public Map<String, Object> getSatisfactionReport(Integer orgId, Date startTime, Date endTime) {
        if (startTime.after(new Date())) {
            return null;
        }
        Date todayStart = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        Date reportEndTime = endTime;
        Date realTimeStart = startTime;
        boolean realTime = false;
        if (!endTime.before(todayStart)) {
            reportEndTime = DateUtils.addSeconds(todayStart, -1);
            realTime = true;
            realTimeStart = startTime.after(todayStart) ? startTime : todayStart;
        }
        List<SatisfactionReportEntity> reportList = satisfactionReportRepo.findByOrgIdAndDateGreaterThanEqualAndDateLessThanEqual(orgId, startTime, reportEndTime);

        //当天实时数据
        if (realTime) {
            List<SatisfactionReportEntity> todayInfoList = getSatisfactionInfoList(orgId, realTimeStart, endTime);
            if (todayInfoList != null && todayInfoList.size() > 0) {
                reportList.addAll(todayInfoList);
            }
        }
        if (reportList == null || reportList.size() == 0) {
            return null;
        }
        List<Map<String, Object>> detailList = new ArrayList<>();
        reportList.stream().forEach(t -> {
            Map<String, Object> infoMap = convertSatisfationInfo(t);
            infoMap.put("date", DateFormatUtils.format(t.getDate(), "yyyy-MM-dd"));
            detailList.add(infoMap);
        });

        List<Map<String, Object>> detailSortList = detailList.stream().sorted(Comparator.comparing(m -> (String) m.get("date"))).collect(Collectors.toList());
        SatisfactionReportEntity totalInfo = getSatisfactionReportSumInfo(reportList);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("detail", detailSortList);
        resultMap.put("total", convertSatisfationInfo(totalInfo));
        return resultMap;
    }

    private Map<String, Object> convertSatisfationInfo(SatisfactionReportEntity t) {
        JSONObject info = (JSONObject) JSON.toJSON(t);
        info.put("evaluatedRate", t.getEvaluatedRate() + "%");
        info.put("unEvaluatedRate", t.getUnEvaluatedRate() + "%");
        info.put("inviteRate", t.getInviteRate() + "%");
        info.put("satisfactionRate", t.getSatisfactionRate() + "%");
        return info;
    }

    public List<SatisfactionReportEntity> getSatisfactionInfoList(Integer orgId, Date startTime, Date endTime) {
        List<StatisticsTimeCountEntity> sessionCountList = sessionListRepo.getOfflineCountByDate(orgId, startTime, endTime);
        List<StatisticsTimeCountEntity> evaluatedCountList = satisfactionDataRepo.getEvaluatedCountByDate(orgId, startTime, endTime);
        Map<Date, Long> evaluatedCountMap = evaluatedCountList.stream().collect(Collectors.toMap(StatisticsTimeCountEntity::getTime, StatisticsTimeCountEntity::getTotal));
        List<StatisticsTimeCountEntity> inviteCountList = satisfactionDataRepo.getInviteCountByDate(orgId, startTime, endTime);
        Map<Date, Long> inviteCountMap = inviteCountList.stream().collect(Collectors.toMap(StatisticsTimeCountEntity::getTime, StatisticsTimeCountEntity::getTotal));
        List<StatisticsTimeAnalyzeEntity> levelCountList = satisfactionDataRepo.getLevelCountByDate(orgId, startTime, endTime);
        Map<Date, Map<Number, Number>> levelCountMap = levelCountList.stream().collect(Collectors.groupingBy(e -> e.getTime(), Collectors.toMap(f -> f.getTotal(), StatisticsTimeAnalyzeEntity::getAnalyze)));
        List<SatisfactionReportEntity> infoList = new ArrayList<>();
        Date currentTime = new Date();
        sessionCountList.stream().forEach(s -> {
            Date date = s.getTime();
            Map<Number, Number> levelMap = levelCountMap.getOrDefault(date, new HashMap<>());
            Long sessionCount = s.getTotal();
            Long evaluatedCount = evaluatedCountMap.getOrDefault(date, 0L);
            Long invitedCount = inviteCountMap.getOrDefault(date, 0L);
            Long leve1Count = levelMap.getOrDefault(1, 0L).longValue();
            Long leve2Count = levelMap.getOrDefault(2, 0L).longValue();
            Long leve3Count = levelMap.getOrDefault(3, 0L).longValue();
            Long leve4Count = levelMap.getOrDefault(4, 0L).longValue();
            Long leve5Count = levelMap.getOrDefault(5, 0L).longValue();
            BigDecimal evaluatedRate = BigDecimal.valueOf(evaluatedCount * 100).divide(BigDecimal.valueOf(sessionCount), 2, RoundingMode.HALF_UP);
            Long unEvaluatedCount = sessionCount - evaluatedCount;
            BigDecimal unEvaluatedRate = BigDecimal.valueOf(unEvaluatedCount * 100).divide(BigDecimal.valueOf(sessionCount), 2, RoundingMode.HALF_UP);
            BigDecimal inviteRate = BigDecimal.valueOf(invitedCount * 100).divide(BigDecimal.valueOf(sessionCount), 2, RoundingMode.HALF_UP);
            BigDecimal satisfactionRate = evaluatedCount > 0 ? BigDecimal.valueOf((leve4Count + leve5Count) * 100).divide(BigDecimal.valueOf(evaluatedCount), 2, RoundingMode.HALF_UP) : new BigDecimal("0.00");

            SatisfactionReportEntity infoEntity = new SatisfactionReportEntity();
            infoEntity.setDate(date);
            infoEntity.setSessionCount(sessionCount);
            infoEntity.setEvaluatedCount(evaluatedCount);
            infoEntity.setEvaluatedRate(evaluatedRate);
            infoEntity.setUnEvaluatedCount(unEvaluatedCount);
            infoEntity.setUnEvaluatedRate(unEvaluatedRate);
            infoEntity.setInviteCount(invitedCount);
            infoEntity.setInviteRate(inviteRate);
            infoEntity.setLevel1(leve1Count);
            infoEntity.setLevel2(leve2Count);
            infoEntity.setLevel3(leve3Count);
            infoEntity.setLevel4(leve4Count);
            infoEntity.setLevel5(leve5Count);
            infoEntity.setSatisfactionRate(satisfactionRate);
            infoEntity.setOrgId(orgId);
            infoEntity.setCreatTime(currentTime);
            infoList.add(infoEntity);
        });
        return infoList;
    }

    public SatisfactionReportEntity getSatisfactionReportSumInfo(List<SatisfactionReportEntity> infoList) {
        if (infoList == null || infoList.size() == 0) {
            return null;
        }
        Long sessionTotalCount = infoList.stream().mapToLong(v -> v.getSessionCount()).sum();
        Long evaluatedTotalCount = infoList.stream().mapToLong(v -> v.getEvaluatedCount()).sum();
        Long unEvaluatedTotalCount = infoList.stream().mapToLong(v -> v.getUnEvaluatedCount()).sum();
        Long inviteTotalCount = infoList.stream().mapToLong(v -> v.getInviteCount()).sum();
        Long level1Total = infoList.stream().mapToLong(v -> v.getLevel1()).sum();
        Long level2Total = infoList.stream().mapToLong(v -> v.getLevel2()).sum();
        Long level3Total = infoList.stream().mapToLong(v -> v.getLevel3()).sum();
        Long level4Total = infoList.stream().mapToLong(v -> v.getLevel4()).sum();
        Long level5Total = infoList.stream().mapToLong(v -> v.getLevel5()).sum();
        BigDecimal evaluatedTotalRate = BigDecimal.valueOf(evaluatedTotalCount * 100).divide(BigDecimal.valueOf(sessionTotalCount), 2, RoundingMode.HALF_UP);
        BigDecimal unEvaluatedTotalRate = BigDecimal.valueOf(unEvaluatedTotalCount * 100).divide(BigDecimal.valueOf(sessionTotalCount), 2, RoundingMode.HALF_UP);
        BigDecimal inviteTotalRate = BigDecimal.valueOf(inviteTotalCount * 100).divide(BigDecimal.valueOf(sessionTotalCount), 2, RoundingMode.HALF_UP);
        BigDecimal satisfactionTotalRate = evaluatedTotalCount > 0 ? BigDecimal.valueOf((level4Total + level5Total) * 100).divide(BigDecimal.valueOf(evaluatedTotalCount), 2, RoundingMode.HALF_UP) : new BigDecimal("0.00");
        SatisfactionReportEntity infoEntity = new SatisfactionReportEntity();
        infoEntity.setSessionCount(sessionTotalCount);
        infoEntity.setEvaluatedCount(evaluatedTotalCount);
        infoEntity.setEvaluatedRate(evaluatedTotalRate);
        infoEntity.setUnEvaluatedCount(unEvaluatedTotalCount);
        infoEntity.setUnEvaluatedRate(unEvaluatedTotalRate);
        infoEntity.setInviteCount(inviteTotalCount);
        infoEntity.setInviteRate(inviteTotalRate);
        infoEntity.setLevel1(level1Total);
        infoEntity.setLevel2(level2Total);
        infoEntity.setLevel3(level3Total);
        infoEntity.setLevel4(level4Total);
        infoEntity.setLevel5(level5Total);
        infoEntity.setSatisfactionRate(satisfactionTotalRate);
        return infoEntity;
    }

    @Override
    public boolean run(TaskEntity task) throws Exception {
        if (TaskEntity.SATISFACTION_LABEL_REPORT.equals(task.getType())) {
            return exportSatisfactionLabelReport(task);
        }
        Integer orgId = task.getOrgId();
        String[] titleArray = new String[] { "日期", "会话数", "评价数", "评价率", "未评数", "未评率", "坐席邀请数", "坐席邀请率", "1星", "2星", "3星", "4星", "5星", "满意度" };
        String[] titleFieldArray = new String[] { "date", "sessionCount", "evaluatedCount", "evaluatedRate", "unEvaluatedCount", "unEvaluatedRate", "inviteCount", "inviteRate", "level1", "level2", "level3", "level4", "level5", "satisfactionRate" };
        ReportReqModel model = JSON.toJavaObject(task.getExtraParams(), ReportReqModel.class);
        Date startTime = model.getStartTime(), endTime = model.getEndTime();
        String fileName = "满意度_" + DateFormatUtils.format(startTime, "yyyy-MM-dd") + "_" + DateFormatUtils.format(endTime, "yyyy-MM-dd") + ".xlsx";
        task.setName(fileName);
        CommonUtil.saveTaskProgress(task, 0);
        Map<String, Object> reportData = getSatisfactionReport(orgId, startTime, endTime);
        if (null == reportData || reportData.isEmpty()) {
            CommonUtil.saveTaskProgress(task, -1);
            return false;
        }
        CommonUtil.saveTaskProgress(task, 75);
        XSSFWorkbook workbook = new XSSFWorkbook();

        List<Map<String, Object>> detailList = (List<Map<String, Object>>) reportData.get("detail");
        Map<String, Object> total = (Map<String, Object>) reportData.get("total");
        Sheet sheet = workbook.createSheet();
        sheet.setDefaultColumnWidth(14);

        Row header = sheet.createRow(0);
        for (int i = 0; i < titleArray.length; i++) {
            Cell headerCell = header.createCell(i);
            headerCell.setCellValue(titleArray[i]);
        }

        for (int j = 0; j < detailList.size(); j++) {
            Map<String, Object> detailMap = detailList.get(j);
            Row row = sheet.createRow(j + 1);
            for (int t = 0; t < titleFieldArray.length; t++) {
                Cell rowCell = row.createCell(t);
                Object value = detailMap.get(titleFieldArray[t]);
                if (value == null) {
                    rowCell.setCellValue("");
                } else if (value instanceof Long) {
                    rowCell.setCellValue((Long) value);
                } else {
                    rowCell.setCellValue(String.valueOf(value));
                }
            }
        }

        int totalRowIndex = detailList.size() + 1;
        Row totalRow = sheet.createRow(totalRowIndex);
        Cell totalCell = totalRow.createCell(0);
        totalCell.setCellValue("总计");
        for (int s = 1; s < titleFieldArray.length; s++) {
            Cell rowCell = totalRow.createCell(s);
            Object value = total.get(titleFieldArray[s]);
            if (value == null) {
                rowCell.setCellValue("");
            } else if (value instanceof Long) {
                rowCell.setCellValue((Long) value);
            } else {
                rowCell.setCellValue(String.valueOf(value));
            }
        }
        LOGGER.info("export satisfaction report success, orgId:{}, startTime:{}, endTime:{}", orgId, startTime, endTime);
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(workbook, appPropertyConfig.getOssBucketName(), task);
        return true;
    }

    @Override
    public void loadData(Integer orgId, Object m) {
        SatisfactionLabelModel model = (SatisfactionLabelModel) m;
        List<SatisfactionLabelReportEntity> list = satisfactionElasticRepo.findSatisfactionLabels(orgId, model.getEmails(), model.getBeginDate(), model.getEndDate());
        HashMap<String, UserInfo> infoMap = new HashMap<>();
        if (null != model.getEmails() && model.getEmails().size() > 0) {
            ReportUtil.getUserInfoList(orgId, appPropertyConfig.getProductId(), infoMap, model.getEmails());
        } else {
            HashSet<String> emails = new HashSet<>();
            for (SatisfactionLabelReportEntity sl : list) {
                emails.addAll(sl.getData().keySet());
            }
            ReportUtil.getUserInfoList(orgId, appPropertyConfig.getProductId(), infoMap, emails);
        }
        model.setList(list.stream().map(i -> new SatisfactionLabelRowModel(orgId, i, infoMap)).collect(Collectors.toList()));
    }

    private boolean exportSatisfactionLabelReport(TaskEntity task) throws IOException {
        SatisfactionLabelModel model = JSON.toJavaObject(task.getExtraParams(), SatisfactionLabelModel.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = String.format("满意度标签_%s_%s.xlsx", sdf.format(model.getBeginDate()), sdf.format(model.getEndDate()));
        task.setName(fileName);
        CommonUtil.saveTaskProgress(task, 0);
        loadData(task.getOrgId(), model);
        CommonUtil.saveTaskProgress(task, 75);
        HashMap<String, JSONArray> map = new HashMap<>();
        JSONArray data = new JSONArray();
        LinkedHashMap<String, LinkedHashMap<String, Pair>> dataMap = new LinkedHashMap<>();
        List<String> names = null;
        LinkedHashMap<String, String> emails = new LinkedHashMap<>();
        LinkedHashMap<String, String> headerMap = new LinkedHashMap<>();
        headerMap.put("name", "");
        for (SatisfactionLabelRowModel row : model.getList()) {
            LinkedHashMap<String, Pair> m = new LinkedHashMap<>();
            for (Pair pair : row.getData()) {
                m.put(pair.email, pair);
                emails.put(pair.email, pair.name);
            }
            if (null == names) {
                names = m.values().stream().map(i -> i.name).collect(Collectors.toList());
            }
            dataMap.put(row.getLabel(), m);
            headerMap.put(row.getLabel(), row.getLabel());
        }
        headerMap.put("总计", "总计");
        HashMap<String, Integer> countMap = new HashMap<>();
        for (Map.Entry<String, String> entry : emails.entrySet()) {
            String email = entry.getKey();
            JSONObject row = new JSONObject(true);
            row.put("name", entry.getValue());
            int count = 0;
            for (Map.Entry<String, LinkedHashMap<String, Pair>> e : dataMap.entrySet()) {
                Integer c = countMap.get(e.getKey());
                if (null == c) {
                    c = 0;
                }
                Pair pair = e.getValue().get(email);
                if (null == pair) {
                    row.put(e.getKey(), 0);
                } else {
                    row.put(e.getKey(), pair.count);
                    count += pair.count;
                    countMap.put(e.getKey(), c + pair.count);
                }
            }
            row.put("总计", count);
            data.add(row);
        }
        JSONObject row = new JSONObject(true);
        row.put("name", "总计");
        for (String label : dataMap.keySet()) {
            row.put(label, countMap.get(label));
        }
        data.add(row);
        map.put(String.format("%s_%s", sdf.format(model.getBeginDate()), sdf.format(model.getEndDate())), data);
        XSSFWorkbook sheets = ReportUtil.convertToExcel(map, headerMap);
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(sheets, appPropertyConfig.getOssBucketName(), task);
        return true;
    }
}
