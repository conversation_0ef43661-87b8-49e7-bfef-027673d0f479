package com.wolaidai.webot.cs.config;

import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.cs.service.MessageReceiverService;
import com.wolaidai.webot.cs.service.ReportHistoryService;
import com.wolaidai.webot.cs.service.ScheduleService;
import com.wolaidai.webot.data.elastic.config.ElasticConfig;
import com.wolaidai.webot.data.mysql.config.MysqlConfig;
import com.wolaidai.webot.data.redis.config.RedisConfig;
import com.wolaidai.webot.security.config.SecurityConfig;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.csrf.HttpSessionCsrfTokenRepository;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
@EnableScheduling
@Import({SecurityConfig.class, MysqlConfig.class, RedisConfig.class, ElasticConfig.class})
@EnableConfigurationProperties({AppPropertyConfig.class})
public class AppConfig implements ApplicationRunner {

    @Autowired
    private RedisProperties redisProperties;

    @Autowired
    private AppPropertyConfig appPropertyConfig;

    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private MessageReceiverService messageReceiverService;
    @Autowired
    private ReportHistoryService reportHistoryService;

    @Bean
    public LocalValidatorFactoryBean validator(MessageSource messageSource) {
        LocalValidatorFactoryBean validatorFactoryBean = new LocalValidatorFactoryBean();
        validatorFactoryBean.setValidationMessageSource(messageSource);
        return validatorFactoryBean;
    }

    @Bean
    public CommonsMultipartResolver multipartResolver() {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
        multipartResolver.setMaxUploadSize(-1);
        return multipartResolver;
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return PasswordEncoderFactories.createDelegatingPasswordEncoder();
    }

    @Bean
    public CsrfTokenRepository csrfTokenRepository() {
        return new HttpSessionCsrfTokenRepository();
    }

    @Bean
    public CookieSerializer cookieSerializer() {
        DefaultCookieSerializer defaultCookieSerializer = new DefaultCookieSerializer();
        defaultCookieSerializer.setSameSite(null);
        return defaultCookieSerializer;
    }

    @Bean(destroyMethod="shutdown")
    public RedissonClient redisson(){
        Config config = new Config();
        String username = null;
        String password = null;
        try {
            URI uri = new URI(redisProperties.getUrl());
            if (uri.getUserInfo() != null) {
                String candidate = uri.getUserInfo();
                int index = candidate.indexOf(':');
                if (index >= 0) {
                    username = candidate.substring(0, index);
                    password = candidate.substring(index + 1);
                }
                else {
                    password = candidate;
                }
            }
        }
        catch (URISyntaxException ex) {
        }
        SingleServerConfig serverConfig = config.useSingleServer();
        serverConfig.setAddress(redisProperties.getUrl());
        serverConfig.setDatabase(redisProperties.getDatabase());
        if(StringUtils.isNotBlank(username)){
            serverConfig.setUsername(username);
        }
        if(StringUtils.isNotBlank(password)){
            serverConfig.setPassword(password);
        }
        return Redisson.create(config);
    }

    @Bean("threadPool")
    @Primary
    public ThreadPoolTaskExecutor threadPool(){
        ThreadPoolTaskExecutor callThreadPool = new ThreadPoolTaskExecutor();
        callThreadPool.setCorePoolSize(appPropertyConfig.getTaskCorePool());
        callThreadPool.setMaxPoolSize(appPropertyConfig.getTaskMaxPool());
        callThreadPool.setQueueCapacity(appPropertyConfig.getTaskQueueCapacity());
        callThreadPool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        callThreadPool.setWaitForTasksToCompleteOnShutdown(true);
        callThreadPool.setAwaitTerminationSeconds(10);
        callThreadPool.initialize();
        return callThreadPool;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        OssFileClient.init(appPropertyConfig.getOssEndpoint(), appPropertyConfig.getOssAccessKeyID(), appPropertyConfig.getOssAccessKeySecret());
        if(appPropertyConfig.getCheckMsgFlag()==1) {
            messageReceiverService.getMsg();
        }
        reportHistoryService.generateReport();
        scheduleService.startTaskSchedule();
    }
}
