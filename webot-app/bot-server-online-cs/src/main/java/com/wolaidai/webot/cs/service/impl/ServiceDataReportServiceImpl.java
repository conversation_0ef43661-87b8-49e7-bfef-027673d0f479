package com.wolaidai.webot.cs.service.impl;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.model.report.ServiceDataModel;
import com.wolaidai.webot.cs.model.report.ServiceDataRowModel;
import com.wolaidai.webot.cs.service.ServiceDataReportService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.MergeUserStateDuration;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.elastic.entity.HistoryManualEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexChatHistoryElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.report.ServiceDataReportEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.model.UserStateDuration;
import com.wolaidai.webot.data.mysql.repo.SatisfactionDataRepo;
import com.wolaidai.webot.data.mysql.repo.ServiceDataReportRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;

@Service
public class ServiceDataReportServiceImpl implements ServiceDataReportService {
    final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ComplexChatHistoryElasticRepo chatHistoryElasticRepo;;
    @Autowired
    private ServiceDataReportRepo serviceDataRepo;
    @Autowired
    private SatisfactionDataRepo satisfactionDataRepo;
    @Autowired
    private AppPropertyConfig config;
    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    protected AppPropertyConfig appPropertyConfig;

    @Override
    public List<ServiceDataReportEntity> generateServiceData(Integer _orgId, Set<String> emails, Date startTime, Date endTime) {
        ArrayList<ServiceDataReportEntity> sdrs = new ArrayList<>();
        HashMap<String, UserInfo> infoMap = new HashMap<>();
        for (Date[] arr : DateUtil.splitDateByDay(startTime, endTime)) {
            HashMap<Integer, HashMap<String, ArrayList<UserStateDuration>>> map = ReportUtil.getUserStateDuration(_orgId, emails, arr[0], arr[1]);
            for (Map.Entry<Integer, HashMap<String, ArrayList<UserStateDuration>>> entry : map.entrySet()) {
                Integer orgId = entry.getKey();
                Map<String, HistoryManualEntity> historyMap = chatHistoryElasticRepo.findManualHistories(orgId, false, arr[0], arr[1]);
                HashMap<String, ArrayList<UserStateDuration>> m = entry.getValue();
                if (m.size() > 0) {
                    List<ServiceDataReportEntity> list = processOneDayAttendance(orgId, m, infoMap, historyMap, arr[0], arr[1]);
                    if (null != list && list.size() > 0) {
                        serviceDataRepo.saveAll(list);
                        sdrs.addAll(list);
                    }
                }
            }
        }
        return sdrs;
    }

    private List<ServiceDataReportEntity> processOneDayAttendance(Integer orgId, HashMap<String, ArrayList<UserStateDuration>> m, HashMap<String, UserInfo> infoMap, Map<String, HistoryManualEntity> historyMap, Date startTime, Date endTime) {
        List<ServiceDataReportEntity> usses = new ArrayList<>();
        ReportUtil.getUserInfoList(orgId, config.getProductId(), infoMap, m.keySet());
        HashMap<String, ServiceDataReportEntity> emptyInfoMap = new HashMap<>();
        for (Map.Entry<String, ArrayList<UserStateDuration>> entry : m.entrySet()) {
            String email = entry.getKey();
            ServiceDataReportEntity uss = new ServiceDataReportEntity();
            uss.setCreateTime(new Date());
            uss.setDataTime(startTime);
            uss.setOrgId(orgId);
            uss.setEmail(email);
            MergeUserStateDuration musd = new MergeUserStateDuration(entry.getValue());
            uss.setTotalOnline(musd.totalOnline);
            uss.setTotalBusy(musd.totalBusy);
            uss.setTotalRest(musd.totalRest + musd.totalLeave);

            List<SessionListEntity> sessions = ReportUtil.getSessionList(false, orgId, Arrays.asList(email), startTime, endTime);
            if (null != sessions && sessions.size() > 0) {
                uss.setSessionCount(sessions.size());
                long totalDuration = 0;
                long totalFirstResponseTime = 0;
                long totalAVGResponseTime = 0;
                HashSet<Integer> sessionIds = new HashSet<>();
                for (SessionListEntity s : sessions) {
                    if (!Objects.equals(s.getStatus(), SessionListEntity.STATUS_OFFLINE)) {
                        continue;
                    }
                    totalDuration += ReportUtil.getSessionDurationSeconds(email, s);
                    HistoryManualEntity history = historyMap.get(email + s.getSessionKey());
                    if (null != history) {
                        if (null != history.getFirstResponseTime()) {
                            totalFirstResponseTime += history.getFirstResponseTime();
                        }
                        if (null != history.getAvgResponseTime()) {
                            totalAVGResponseTime += history.getAvgResponseTime();
                        }
                    }
                    sessionIds.add(s.getId());
                }
                uss.setDurationSecondsAvg(totalDuration / sessions.size());
                uss.setResponseAvg(totalAVGResponseTime / sessions.size());
                uss.setFirstResponseAvg(totalFirstResponseTime / sessions.size());

                HashMap<Integer, Integer> levelMap = new HashMap<>();
                List<SatisfactionDataEntity> sds = satisfactionDataRepo.findByOrgIdAndSessionIdInAndCreateTimeGreaterThanEqualAndCreateTimeLessThan(orgId, sessionIds, startTime, endTime);
                for (SatisfactionDataEntity sd : sds) {
                    if (null != sd.getLevel()) {
                        Integer count = levelMap.get(sd.getLevel());
                        if (null == count) {
                            levelMap.put(sd.getLevel(), 1);
                        } else {
                            levelMap.put(sd.getLevel(), count + 1);
                        }
                    }
                }
                uss.setStarOne(null != levelMap.get(1) ? levelMap.get(1) : 0);
                uss.setStarTwo(null != levelMap.get(2) ? levelMap.get(2) : 0);
                uss.setStarThree(null != levelMap.get(3) ? levelMap.get(3) : 0);
                uss.setStarFour(null != levelMap.get(4) ? levelMap.get(4) : 0);
                uss.setStarFive(null != levelMap.get(5) ? levelMap.get(5) : 0);
                uss.setSatisfactionPercent(ReportUtil.getPercent(uss.getStarFour() + uss.getStarFive(), uss.getStarOne() + uss.getStarTwo() + uss.getStarThree() + uss.getStarFour() + uss.getStarFive()));
                usses.add(uss);
                UserInfo info = infoMap.get(orgId + email);
                UserExtraField uef = null;
                if (null != info && null != (uef = info.getUserExtraField())) {
                    uss.setWorkNumber(uef.getWorkNumber());
                    uss.setNickName(uef.getNickName());
                } else {
                    emptyInfoMap.put(email, uss);
                }
            }
        }
        if (emptyInfoMap.size() > 0) {
            List<UserStateEntity> list = userStateRepo.findByOrgIdAndEmailInAndCreateTimeGreaterThanEqual(orgId, emptyInfoMap.keySet(), startTime);
            for (UserStateEntity s : list) {
                ServiceDataReportEntity ss = emptyInfoMap.get(s.getEmail());
                ss.setWorkNumber(s.getWorkNumber());
                ss.setNickName(s.getNickName());
            }
        }
        return usses;
    }

    @Override
    public void generateReportByDate(Integer orgId, Date startTime, Date endTime) {
        if (null != orgId) {
            serviceDataRepo.deleteByOrgIdAndDataTime(orgId, startTime, endTime);
        } else {
            serviceDataRepo.deleteByDataTime(startTime, endTime);
        }
        generateServiceData(orgId, null, startTime, endTime);
    }

    @Override
    public String getReportName() {
        return "数字化报表统计";
    }

    @Override
    public void loadData(Integer orgId, Object m) {
        ServiceDataModel model = (ServiceDataModel) m;
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);
        List<ServiceDataReportEntity> result = new ArrayList<>();
        if (today.getTime() >= model.getBeginDate().getTime() && today.before(model.getEndDate())) {
            result = generateServiceData(orgId, model.getEmails(), today, new Date());
        }
        ArrayList<Date[]> daysDate = DateUtil.splitDateByDay(model.getBeginDate(), model.getEndDate());
        Date begin = daysDate.get(0)[0], end = daysDate.get(daysDate.size() - 1)[1];
        if (model.getBeginDate().before(today)) {
            Date endDate = model.getEndDate().after(today) ? DateUtils.addMilliseconds(today, -1) : model.getEndDate();
            List<ServiceDataReportEntity> list = null;
            if (model.getEmails().size() > 0) {
                list = serviceDataRepo.findByOrgIdAndEmailInAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAsc(orgId, model.getEmails(), model.getBeginDate(), endDate);
            } else {
                list = serviceDataRepo.findByOrgIdAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAsc(orgId, model.getBeginDate(), endDate);
            }
            result.addAll(list);
        }
        if (result.size() > 0) {
            Map<String, List<ServiceDataReportEntity>> map = result.stream().collect(Collectors.groupingBy(ServiceDataReportEntity::getEmail));
            Integer sessionCount = 0;
            Long durationSecondsAvg = 0l, totalBusy = 0l, totalRest = 0l, firstResponseAvg = 0l, responseAvg = 0l, onlineAvg = 0l;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String date = String.format("%s ~ %s", sdf.format(begin), sdf.format(DateUtils.addDays(end, -1)));
            int one = 0, two = 0, three = 0, four = 0, five = 0;
            for (List<ServiceDataReportEntity> l : map.values()) {
                ServiceDataRowModel i = new ServiceDataRowModel(l);
                i.setDate(date);
                sessionCount += i.getSessionCount();
                durationSecondsAvg += i.getDurationSecondsAvg();
                totalBusy += i.getTotalBusy();
                totalRest += i.getTotalRest();
                firstResponseAvg += i.getFirstResponseAvg();
                responseAvg += i.getResponseAvg();
                onlineAvg += i.getOnlineAvg();
                one += i.getOne();
                two += i.getTwo();
                three += i.getThree();
                four += i.getFour();
                five += i.getFive();
                model.getDetail().add(i);
            }
            int count = map.size();
            ServiceDataRowModel avg = new ServiceDataRowModel();
            avg.setSessionCount(sessionCount / count);
            avg.setDurationSecondsAvg(durationSecondsAvg / count);
            avg.setTotalBusy(totalBusy / count);
            avg.setTotalRest(totalRest / count);
            avg.setFirstResponseAvg(firstResponseAvg / count);
            avg.setResponseAvg(responseAvg / count);
            avg.setSatisfactionPercent(ReportUtil.getPercent(four + five, one + two + three + four + five) + "%");
            avg.setOnlineAvg(onlineAvg / count);
            avg.setOnlineAvgStr(DateUtil.formatSecondsTime(avg.getOnlineAvg(), null));
            model.setAvg(avg);
        }
    }

    @Override
    public boolean run(TaskEntity task) throws IOException {
        ServiceDataModel model = JSON.toJavaObject(task.getExtraParams(), ServiceDataModel.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = String.format("数字化报表_%s_%s.xlsx", sdf.format(model.getBeginDate()), sdf.format(model.getEndDate()));
        task.setName(fileName);
        CommonUtil.saveTaskProgress(task, 0);
        loadData(task.getOrgId(), model);
        CommonUtil.saveTaskProgress(task, 75);
        ServiceDataRowModel avg = model.getAvg();
        if (null != avg && model.getDetail().size() > 0) {
            avg.setDate(model.getDetail().get(0).getDate());
            model.getDetail().add(avg);
        }
        Map<String, List<ServiceDataRowModel>> map = model.getDetail().stream().collect(Collectors.groupingBy(i -> i.getDate()));
        Map<String, JSONArray> map2 = new TreeMap<>(Comparator.comparing(i -> i));
        for (Map.Entry<String, List<ServiceDataRowModel>> entry : map.entrySet()) {
            map2.put(entry.getKey(), (JSONArray) JSON.toJSON(entry.getValue()));
        }
        LinkedHashMap<String, String> headerMap = new LinkedHashMap<>();
        headerMap.put("date", "时间周期");
        headerMap.put("workNumber", "工号");
        headerMap.put("nickName", "姓名");
        headerMap.put("sessionCount", "接待量");
        headerMap.put("durationSecondsAvg", "平均接待时长(s)");
        headerMap.put("totalBusy", "忙碌时长(s)");
        headerMap.put("totalRest", "休息时长(s)");
        headerMap.put("firstResponseAvg", "平均首次响应时长(s)");
        headerMap.put("responseAvg", "平均响应时长(s)");
        headerMap.put("satisfactionPercent", "客户满意度");
        headerMap.put("onlineAvgStr", "平均在线时长");
        XSSFWorkbook sheets = ReportUtil.convertToExcel(map2, headerMap);
        XSSFSheet sheet = sheets.getSheetAt(0);
        sheet.setColumnWidth(0, (avg.getDate().length() + 1) * 256);
        int row = sheet.getLastRowNum();
        sheet.addMergedRegion(new CellRangeAddress(row, row, 0, 2));
        sheet.getRow(row).getCell(0).setCellValue("均值");
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(sheets, appPropertyConfig.getOssBucketName(), task);
        return true;
    }

}
