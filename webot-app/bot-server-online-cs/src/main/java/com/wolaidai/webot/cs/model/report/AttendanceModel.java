package com.wolaidai.webot.cs.model.report;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.wolaidai.webot.cs.model.BaseModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "考勤统计请求参数")
public class AttendanceModel extends BaseModel {

    private Set<String> emails = new HashSet<>();
    private Date beginDate;
    private Date endDate;
    @ApiModelProperty(required = true, value = "类型，1-按日统计，2-按周期统计")
    private Integer type = 1;
    private List<AttendanceRowModel> list = new ArrayList<>();

    public Set<String> getEmails() {
        return emails;
    }

    public void setEmails(Set<String> emails) {
        this.emails = emails;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<AttendanceRowModel> getList() {
        return list;
    }

    public void setList(List<AttendanceRowModel> list) {
        this.list = list;
    }

}
