package com.wolaidai.webot.cs.model.report;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "报表统计请求数据")
public class ReportReqModel extends BaseModel {

    @ApiModelProperty(required = true, value = "开始时间")
    private Date startTime;

    @ApiModelProperty(required = true, value = "结束时间")
    private Date endTime;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
