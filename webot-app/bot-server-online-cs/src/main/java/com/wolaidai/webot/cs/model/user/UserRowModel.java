package com.wolaidai.webot.cs.model.user;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;

public class UserRowModel extends BaseModel {

    private String email;
    private String nickName;
    private String workNumber;
    private Integer status;

    public UserRowModel(UserInfo i) {
        this.email = i.getEmail();
        UserExtraField uef = i.getUserExtraField();
        if (null != uef) {
            this.nickName = uef.getNickName();
            this.workNumber = uef.getWorkNumber();
        }
        this.status = i.getStatus();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

}
