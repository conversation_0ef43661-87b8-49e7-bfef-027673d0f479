package com.wolaidai.webot.cs.model.sessionlist;

import com.wolaidai.webot.cs.model.PageableModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiModel(value = "客服历史会话")
public class StaffHistorySessionSearchModel extends PageableModel {

    private String key;
    private Date startTime;
    private Date endTime;
    private String customerName;
    @ApiModelProperty(value = "客户uuid")
    private String uuid;
    @ApiModelProperty(value = "客户userId")
    private Integer userId;
    @ApiModelProperty(value = "客户手机号")
    private String mobile;
    @ApiModelProperty(value = "会话标识")
    private Integer mark;
    @ApiModelProperty(value = "业务类型")
    private List<Integer> businessIds = new ArrayList<>();
    @ApiModelProperty(value = "咨询渠道")
    private List<Integer> clientIds = new ArrayList<>();
    @ApiModelProperty(value = "服务总结状态:1-已总结,0-未总结")
    private Integer serviceSummaryStatus;
    @ApiModelProperty(value = "评价,0-5,0-未评价")
    private List<Integer> appraiseLevels = new ArrayList<>();
    private List<StaffHistorySessionRowModel> list = new ArrayList<>();

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getMark() {
        return mark;
    }

    public void setMark(Integer mark) {
        this.mark = mark;
    }

    public List<Integer> getBusinessIds() {
        return businessIds;
    }

    public void setBusinessIds(List<Integer> businessIds) {
        this.businessIds = businessIds;
    }

    public List<Integer> getClientIds() {
        return clientIds;
    }

    public void setClientIds(List<Integer> clientIds) {
        this.clientIds = clientIds;
    }

    public Integer getServiceSummaryStatus() {
        return serviceSummaryStatus;
    }

    public void setServiceSummaryStatus(Integer serviceSummaryStatus) {
        this.serviceSummaryStatus = serviceSummaryStatus;
    }

    public List<Integer> getAppraiseLevels() {
        return appraiseLevels;
    }

    public void setAppraiseLevels(List<Integer> appraiseLevels) {
        this.appraiseLevels = appraiseLevels;
    }

    public List<StaffHistorySessionRowModel> getList() {
        return list;
    }

    public void setList(List<StaffHistorySessionRowModel> list) {
        this.list = list;
    }
}
