package com.wolaidai.webot.cs.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.wolaidai.webot.common.util.HttpClientUtil;
import com.wolaidai.webot.data.mysql.repo.*;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.ExcelReader;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.entity.Event;
import com.wolaidai.webot.cs.model.report.ServiceSummaryModel;
import com.wolaidai.webot.cs.model.report.ServiceSummaryRowModel;
import com.wolaidai.webot.cs.model.upload.UploadCheckResultModel;
import com.wolaidai.webot.cs.model.upload.UploadErrorModel;
import com.wolaidai.webot.cs.model.upload.UploadProgressModel;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.cs.service.ServiceSummaryService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.elastic.entity.ServiceSummaryReportEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexServiceSummaryElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.repo.config.BusinessTypeRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessUnitRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;


@Service
public class ServiceSummaryServiceImpl implements ServiceSummaryService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final String CONFIG_KEY = "serviceSummary";

    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    private SessionListRepo sessionListRepo;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private ServiceSummaryRepo serviceSummaryRepo;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private BusinessUnitRepo businessUnitRepo;
    @Autowired
    private BusinessTypeRepo businessTypeRepo;
    @Autowired
    protected AppPropertyConfig appPropertyConfig;
    @Autowired
    private TaskRepo taskRepo;
    @Autowired
    private ConfigService configService;
    @Autowired
    private ComplexServiceSummaryElasticRepo serviceSummaryElasticRepo;
    @Autowired
    private CommonConfigRepo commonConfigRepo;
    @Autowired
    private UserRepo userRepo;

    private boolean isEnable(HashOperations<String, String, String> hashOperations, Integer orgId) {
        String status = configService.read(orgId, String.format(RedisKey.WORKBENCH_CONFIG_STATUS, orgId), CONFIG_KEY, CommonConfigEntity.TYPE_WORKBENCH_CONFIG_STATUS, CONFIG_KEY);
        return StringUtils.isNotBlank(status) && Boolean.valueOf(status);
    }

    private JSONObject getConfig(HashOperations<String, String, String> hashOperations, Integer orgId) {
        String conf = configService.read(orgId, String.format(RedisKey.WORKBENCH_CONFIG, orgId), CONFIG_KEY, CommonConfigEntity.TYPE_WORKBENCH_CONFIG, CONFIG_KEY);
        if (StringUtils.isNotBlank(conf)) {
            JSONArray arr = JSON.parseObject(conf).getJSONArray("configList");
            arr.sort(Comparator.comparing(i -> ((JSONObject) JSON.toJSON(i)).getString("time")));
            LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.MINUTES);
            for (int i = 0; i < arr.size(); i++) {
                JSONObject json = arr.getJSONObject(i);
                String time = json.getString("time");
                if (now.isEqual(LocalDateTime.of(now.toLocalDate(), LocalTime.of(Integer.valueOf(time.split(":")[0]), Integer.valueOf(time.split(":")[1]))))) {
                    return json;
                }
            }
        }
        return null;
    }

    @Override
    public void checkSummaryTarget() {
        RLock lock = redissonClient.getLock(RedisKey.LOCK_KEY + "checkSummaryTarget");
        try {
            if (!lock.tryLock(0, 30, TimeUnit.SECONDS)) {
                return;
            }
            LOGGER.info("start checkSummaryTarget...");
            long start = System.currentTimeMillis();
            HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
            Date today = DateUtils.truncate(new Date(), Calendar.DATE);
            Date endDate = DateUtils.addDays(today, 1);
            List<UserStateEntity> states = userStateRepo.findByUserTypeAndCreateTimeGreaterThanEqual(UserStateEntity.TYPE_NORMAL, today);
            Map<Integer, Map<String, UserStateEntity>> map = states.stream().collect(Collectors.groupingBy(UserStateEntity::getOrgId, Collectors.toMap(UserStateEntity::getEmail, Function.identity())));
            for (Map.Entry<Integer, Map<String, UserStateEntity>> entry : map.entrySet()) {
                Integer orgId = entry.getKey();
                JSONObject config = null;
                if (isEnable(hashOperations, orgId) && null != (config = getConfig(hashOperations, orgId))) {
                    JSONArray details = new JSONArray();
                    List<SessionListEntity> sessions = sessionListRepo.findByOrgIdAndStatusAndLastServiceUserInAndCreateTimeGreaterThanEqualAndCreateTimeLessThanOrderByCreateTimeAsc(orgId, SessionListEntity.STATUS_OFFLINE, entry.getValue().keySet(), today, endDate);
                    if (sessions.size() > 0) {
                        Map<String, List<SessionListEntity>> m = sessions.stream().collect(Collectors.groupingBy(SessionListEntity::getLastServiceUser));
                        for (Map.Entry<String, List<SessionListEntity>> e : m.entrySet()) {
                            List<SessionListEntity> list = e.getValue();
                            long count = serviceSummaryRepo.countBySessionIdIn(list.stream().map(i -> i.getId()).collect(Collectors.toSet()));
                            if (ReportUtil.getPercent(count, list.size()) < config.getIntValue("target")) {
                                JSONObject detail = new JSONObject();
                                detail.put("email", e.getKey());
                                detail.put("total", list.size());
                                detail.put("finished", count);
                                details.add(detail);
                            }
                        }
                    }
                    if (details.size() > 0) {
                        redisTemplate.opsForList().rightPush(RedisKey.EVENT_SERVER_KEY, JSONObject.toJSONString(new Event(UUID.randomUUID().toString(), Event.SUMMARY_NOTIFY_KEY, orgId, new JSONObject(config).fluentPut("orgId", orgId).fluentPut("details", details), new Date())));
                    }
                }
            }
            LOGGER.info("end checkSummaryTarget,cost {} ms", System.currentTimeMillis() - start);
        } catch (Exception e) {
            LOGGER.error("execute checkSummaryTarget schedule fail", e);
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private boolean importBusinessType(TaskEntity task) throws IOException {
        Integer orgId = task.getOrgId();
        File tmpFile = CommonUtil.getImportFileFromOss(task, appPropertyConfig.getOssBucketName());
        ExcelReader excelReader = new ExcelReader(tmpFile.getAbsolutePath());
        UploadCheckResultModel resultModel = validateRecords(task.getName(), excelReader);
        String uploadId = resultModel.getUploadId();
        if (resultModel.getErrors().isEmpty()) {
            UploadProgressModel uploadProgressModel = new UploadProgressModel();
            uploadProgressModel.setTotal(excelReader.getTotalCount());
            UploadProgressModel model = importRecords(task, uploadId, orgId, excelReader);
            return StringUtils.isBlank(model.getErrMsg());
        }
        task.setErrorLog((JSONArray) JSON.toJSON(resultModel.getErrors()));
        CommonUtil.saveTaskProgress(task, -1);
        return false;
    }

    @Override
    public boolean run(TaskEntity task) throws IOException {
        if (TaskEntity.SERVICE_SUMMARY_REPORT.equals(task.getType())) {
            return exportServiceSummaryReport(task);
        }
        if (TaskEntity.BUSINESS_TYPE.equals(task.getType()) && TaskEntity.PORT_IMPORT.equals(task.getPort())) {
            return importBusinessType(task);
        }
        String fileName = "服务小结_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + ".xlsx";
        task.setName(fileName);
        CommonUtil.saveTaskProgress(task, 0);
        Integer orgId = task.getOrgId();
        XSSFWorkbook sheets = new XSSFWorkbook();
        XSSFSheet sheet = sheets.createSheet();
        sheet.setDefaultColumnWidth(15);
        XSSFRow headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("业务单元");
        headerRow.createCell(1).setCellValue("二级分类");
        headerRow.createCell(2).setCellValue("三级分类");
        headerRow.createCell(3).setCellValue("四级分类");
        headerRow.createCell(4).setCellValue("五级分类");
        List<BusinessUnitEntity> businessUnitEntities = businessUnitRepo.findByOrgId(orgId);
        for (BusinessUnitEntity unitEntity : businessUnitEntities) {
            List<BusinessTypeEntity> typeEntityList = businessTypeRepo.findByOrgIdAndUnitIdAndParentTypeIsNull(orgId, unitEntity.getId(), Sort.by(new Sort.Order(Sort.Direction.ASC, "position"), new Sort.Order(Sort.Direction.ASC, "id")));
            if (CollectionUtils.isNotEmpty(typeEntityList)) {
                for (BusinessTypeEntity typeEntity : typeEntityList) {
                    String unitName = unitEntity.getName();
                    List<String> fullPathTitles = new ArrayList<>();
                    typeEntity.fullPathTitle(fullPathTitles);
                    for (String titles : fullPathTitles) {
                        int currentCellIndex = 0;
                        XSSFRow row = sheet.createRow(sheet.getLastRowNum() + 1);
                        row.createCell(currentCellIndex++).setCellValue(unitName);
                        String[] fullTitles = titles.split("\\|,");
                        int length = fullTitles.length;
                        if (length > 0) {
                            row.createCell(currentCellIndex++).setCellValue(fullTitles[0]);
                        }
                        if (length > 1) {
                            row.createCell(currentCellIndex++).setCellValue(fullTitles[1]);
                        }
                        if (length > 2) {
                            row.createCell(currentCellIndex++).setCellValue(fullTitles[2]);
                        }
                        if (length > 3) {
                            row.createCell(currentCellIndex).setCellValue(fullTitles[3]);
                        }
                    }
                }
            }
        }
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(sheets, appPropertyConfig.getOssBucketName(), task);
        return true;
    }

    /**
     * 上传内容校验
     * @param fileName
     * @param excelReader
     * @return
     */
    private UploadCheckResultModel validateRecords(String fileName, ExcelReader excelReader) {
        UploadCheckResultModel resultModel = new UploadCheckResultModel();
        resultModel.setUploadId(UUID.randomUUID().toString());
        resultModel.setFileName(fileName);
        resultModel.setTotalCount(excelReader.getTotalCount());
        for (int sheetIndex = 0; sheetIndex < excelReader.getTotalSheet(); sheetIndex++) {
            excelReader.switchSheet(sheetIndex);
            String[] headers = excelReader.getHeaders();
            String currentSheetName = excelReader.currentSheetName();
            if (headers != null && headers.length > 0) {
                if (!CommonUtil.excelHeaderCheck(new String[] { "业务单元", "二级分类", "三级分类", "四级分类", "五级分类" }, headers)) {
                    resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, 1, "", "excel列头不符合模板要求"));
                    return resultModel;
                }
                int currentIndex = 1;
                while (excelReader.hasNext()) {
                    ++currentIndex;
                    String[] columnValues = excelReader.getColumnValues();
                    int length = columnValues.length;
                    if (length == 0) {
                        continue;
                    }
                    List<Integer> nullIndex = new ArrayList<>();
                    List<Integer> notNullIndex = new ArrayList<>();
                    for (int i = 0; i < length; i++) {
                        if (StringUtils.isBlank(columnValues[i])) {
                            nullIndex.add(i);
                        } else {
                            notNullIndex.add(i);
                        }
                    }
                    //都不为空
                    if (CollectionUtils.isEmpty(nullIndex)) {
                        continue;
                    }
                    //不空最大下标<空最小下标
                    if (Collections.max(notNullIndex) < Collections.min(nullIndex)) {
                        continue;
                    }
                    if (nullIndex.contains(0)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "业务单元不能为空"));
                        continue;
                    }
                    if (nullIndex.contains(1)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "二级分类不能为空"));
                    }
                    if (nullIndex.contains(2)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "三级分类不能为空"));
                    }
                    if (nullIndex.contains(3)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "四级分类不能为空"));
                    }
                    if (nullIndex.contains(4)) {
                        resultModel.addError(new UploadErrorModel(UploadErrorModel.BUSINESS_UNIT_TYPE, currentSheetName, currentIndex, null, "五级分类不能为空"));
                    }
                }
            } else {
                resultModel.addError(new UploadErrorModel(UploadErrorModel.QUICK_REPLY_TYPE, currentSheetName, 1, "", "excel列头不能为空"));
                return resultModel;
            }
        }
        excelReader.close();
        return resultModel;
    }

    /**
     * 导入记录
     * @param task
     * @param uploadId
     * @param orgId
     * @param excelReader
     */
    private UploadProgressModel importRecords(TaskEntity task, String uploadId, Integer orgId, ExcelReader excelReader) {
        String fileName = task.getName();
        UploadProgressModel uploadProgressModel = new UploadProgressModel();
        uploadProgressModel.setTotal(excelReader.getTotalCount());
        String currentSheetName = "";
        int currentRowIndex = -1;
        int progress = 0;
        int repeatCnt = 0;
        long last = System.currentTimeMillis();
        try {
            //重新读取excel
            excelReader.reset();
            for (int sheetIndex = 0; sheetIndex < excelReader.getTotalSheet(); sheetIndex++) {
                excelReader.switchSheet(sheetIndex);
                String[] headers = excelReader.getHeaders();
                if (headers != null && headers.length > 0) {
                    int currentIndex = 1;
                    currentSheetName = excelReader.currentSheetName();
                    while (excelReader.hasNext()) {
                        currentRowIndex = ++currentIndex;
                        String[] columnValues = excelReader.getColumnValues();
                        boolean failInsert = insertQuickReply(orgId, columnValues, --currentRowIndex);
                        if (failInsert) {
                            uploadProgressModel.setRepeatCnt(++repeatCnt);
                        } else {
                            uploadProgressModel.setProgress(++progress);
                        }
                        uploadProgressModel.setPercent(BigDecimal.valueOf((uploadProgressModel.getProgress() + uploadProgressModel.getRepeatCnt()) * 100).divide(BigDecimal.valueOf(uploadProgressModel.getTotal()), 2, RoundingMode.HALF_UP).doubleValue());
                        LOGGER.info("total:{}, progress:{}, repeatCnt:{}, percent:{}", uploadProgressModel.getTotal(), uploadProgressModel.getProgress(), uploadProgressModel.getRepeatCnt(), uploadProgressModel.getPercent());
                        long now = System.currentTimeMillis();
                        if (now - last > 1000) {
                            last = now;
                            task.setImportInfo((JSONObject) JSON.toJSON(uploadProgressModel));
                            CommonUtil.saveTaskProgress(task, (int) uploadProgressModel.getProgress());
                        }
                    }
                    uploadProgressModel.setPercent(100);
                    task.setImportInfo((JSONObject) JSON.toJSON(uploadProgressModel));
                    CommonUtil.saveTaskProgress(task, 100);
                }
            }
        } catch (Exception e) {
            LOGGER.error("导入业务类型失败,文件名:{},工作表:{},行数:{}", fileName, currentSheetName, currentRowIndex, e);
            String errorMsg = String.format("导入业务类型失败,文件名:%s,工作表:%s,行数:%s", fileName, currentSheetName, currentRowIndex);
            uploadProgressModel.setErrMsg(errorMsg);
            task.setImportInfo((JSONObject) JSON.toJSON(uploadProgressModel));
            task.setStatus(TaskEntity.FAIL_STATUS);
            taskRepo.save(task);
        } finally {
            excelReader.close();
        }
        return uploadProgressModel;
    }

    /**
     * 入库
     * @param orgId
     * @param columnValues
     */
    private boolean insertQuickReply(Integer orgId, String[] columnValues, int currentRowIndex) {
        boolean repeatInsert = true;
        //业务单元名称
        String unitName = columnValues[0];
        BusinessUnitEntity sameNameUnit = businessUnitRepo.findByOrgIdAndName(orgId, unitName);
        if (sameNameUnit == null) {
            sameNameUnit = new BusinessUnitEntity();
            sameNameUnit.setName(unitName);
            sameNameUnit.setOrgId(orgId);
            sameNameUnit.setCreateTime(new Date());
            sameNameUnit.setUpdateTime(sameNameUnit.getCreateTime());
            businessUnitRepo.save(sameNameUnit);
            repeatInsert = false;
        }
        //二级
        BusinessTypeEntity typeEntity = null;
        int length = columnValues.length;
        if (length > 1) {
            String typeName2 = columnValues[1];
            if (StringUtils.isNotBlank(typeName2)) {
                typeEntity = businessTypeRepo.findByOrgIdAndUnitIdAndTitleAndParentTypeIsNull(orgId, sameNameUnit.getId(), typeName2);
                if (typeEntity == null) {
                    typeEntity = new BusinessTypeEntity();
                    typeEntity.setLevel(1);
                    typeEntity.setTitle(typeName2);
                    typeEntity.setOrgId(orgId);
                    typeEntity.setUnit(sameNameUnit);
                    typeEntity.setPosition(currentRowIndex);
                    typeEntity.setUpdateTime(new Date());
                    typeEntity.setCreateTime(typeEntity.getUpdateTime());
                    businessTypeRepo.save(typeEntity);
                    repeatInsert = false;
                }
            }
        }
        //三级
        BusinessTypeEntity parentEntity = typeEntity;
        if (length > 2) {
            String typeName3 = columnValues[2];
            if (StringUtils.isNotBlank(typeName3)) {
                typeEntity = businessTypeRepo.findByOrgIdAndParentTypeIdAndTitle(orgId, parentEntity.getId(), typeName3);
                if (typeEntity == null) {
                    typeEntity = new BusinessTypeEntity();
                    typeEntity.setLevel(2);
                    typeEntity.setTitle(typeName3);
                    typeEntity.setParentType(parentEntity);
                    typeEntity.setOrgId(orgId);
                    typeEntity.setUnit(sameNameUnit);
                    typeEntity.setPosition(currentRowIndex);
                    typeEntity.setUpdateTime(new Date());
                    typeEntity.setCreateTime(typeEntity.getUpdateTime());
                    businessTypeRepo.save(typeEntity);
                    repeatInsert = false;
                }
            }
        }
        if (length > 3) {
            //四级
            parentEntity = typeEntity;
            String typeName4 = columnValues[3];
            if (StringUtils.isNotBlank(typeName4)) {
                typeEntity = businessTypeRepo.findByOrgIdAndParentTypeIdAndTitle(orgId, parentEntity.getId(), typeName4);
                if (typeEntity == null) {
                    typeEntity = new BusinessTypeEntity();
                    typeEntity.setLevel(3);
                    typeEntity.setTitle(typeName4);
                    typeEntity.setParentType(parentEntity);
                    typeEntity.setOrgId(orgId);
                    typeEntity.setUnit(sameNameUnit);
                    typeEntity.setPosition(currentRowIndex);
                    typeEntity.setUpdateTime(new Date());
                    typeEntity.setCreateTime(typeEntity.getUpdateTime());
                    businessTypeRepo.save(typeEntity);
                    repeatInsert = false;
                }
            }
        }
        if (length > 4) {
            //五级
            parentEntity = typeEntity;
            String typeName5 = columnValues[4];
            if (StringUtils.isNotBlank(typeName5)) {
                typeEntity = businessTypeRepo.findByOrgIdAndParentTypeIdAndTitle(orgId, parentEntity.getId(), typeName5);
                if (typeEntity == null) {
                    typeEntity = new BusinessTypeEntity();
                    typeEntity.setLevel(4);
                    typeEntity.setTitle(typeName5);
                    typeEntity.setParentType(parentEntity);
                    typeEntity.setOrgId(orgId);
                    typeEntity.setUnit(sameNameUnit);
                    typeEntity.setPosition(currentRowIndex);
                    typeEntity.setUpdateTime(new Date());
                    typeEntity.setCreateTime(typeEntity.getUpdateTime());
                    businessTypeRepo.save(typeEntity);
                    repeatInsert = false;
                }
            }
        }
        return repeatInsert;
    }

    @Override
    public void loadData(Integer orgId, Object m) {
        ServiceSummaryModel model = (ServiceSummaryModel) m;
        List<ServiceSummaryReportEntity> list = serviceSummaryElasticRepo.findServiceSummaries(orgId, model.getBusinessId(), model.getClientTypeId(), model.getBeginDate(), model.getEndDate());
        HashSet<Integer> unitIds = new HashSet<>();
        HashSet<Integer> typeIds = new HashSet<>();
        for (ServiceSummaryReportEntity ssr : list) {
            unitIds.add(ssr.getUnitId());
            typeIds.add(ssr.getTypeId());
        }
        typeIds.remove(null);
        Map<Integer, BusinessUnitEntity> unitMap = businessUnitRepo.findByOrgIdAndIdIn(orgId, unitIds).stream().collect(Collectors.toMap(BusinessUnitEntity::getId, Function.identity()));
        Map<Integer, BusinessTypeEntity> typeMap = businessTypeRepo.findByOrgIdAndIdIn(orgId, typeIds).stream().collect(Collectors.toMap(BusinessTypeEntity::getId, Function.identity()));
        model.setList(list.stream().map(i -> new ServiceSummaryRowModel(i, unitMap.get(i.getUnitId()), typeMap.get(i.getTypeId()))).collect(Collectors.toList()));
        Comparator<ServiceSummaryRowModel> c = Comparator.comparing(i -> i.getItemCount(), Comparator.reverseOrder());
        model.getList().sort(c.thenComparing(Comparator.comparing(i -> i.getUnitName())));
    }

    private boolean exportServiceSummaryReport(TaskEntity task) throws IOException {
        ServiceSummaryModel model = JSON.toJavaObject(task.getExtraParams(), ServiceSummaryModel.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = String.format("服务小结统计_%s_%s.xlsx", sdf.format(model.getBeginDate()), sdf.format(model.getEndDate()));
        task.setName(fileName);
        CommonUtil.saveTaskProgress(task, 0);
        loadData(task.getOrgId(), model);
        CommonUtil.saveTaskProgress(task, 75);
        HashMap<String, JSONArray> map = new HashMap<>();
        map.put(String.format("%s_%s", sdf.format(model.getBeginDate()), sdf.format(model.getEndDate())), (JSONArray) JSON.toJSON(model.getList()));
        LinkedHashMap<String, String> headerMap = new LinkedHashMap<>();
        headerMap.put("unitName", "业务单元");
        headerMap.put("typeName1", "二级分类");
        headerMap.put("typeName2", "三级分类");
        headerMap.put("itemCount", "数量");
        headerMap.put("totalCount", "小结数量");
        headerMap.put("percent", "占比");
        XSSFWorkbook sheets = ReportUtil.convertToExcel(map, headerMap);
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(sheets, appPropertyConfig.getOssBucketName(), task);
        return true;
    }

    @Override
    public void processSummarySensitive(SessionListEntity sessionList, String content) {
        if(StringUtils.isNotBlank(content)) {
            try {
                Integer orgId = sessionList.getOrgId();
                String type = CommonConfigEntity.TYPE_SUMMARY_SENSITIVE;
                CommonConfigEntity configEntity = commonConfigRepo.findOneByOrgIdAndType(orgId, type);
                if(configEntity != null && StringUtils.isNotBlank(configEntity.getContent())) {
                    List<String> sensitiveList = JSON.parseArray(configEntity.getContent(), String.class);
                    List<String> hitList = new ArrayList<>();
                    for(String sensitive : sensitiveList) {
                        if(content.contains(sensitive)) {
                            hitList.add(sensitive);
                        }
                    }
                    if(hitList.size() > 0) {
                        Map<String, String> warnMap = new HashMap<>();
                        JSONObject detail = sessionList.getCustomerDetail();
                        JSONArray customers = null != detail ? detail.getJSONArray("customers") : null;
                        if(null != customers && customers.size() > 0) {
                            JSONObject customer = customers.getJSONObject(0);
                            warnMap.put("uuid", customer.getString("uuid"));
                        }
                        String serviceName = "";
                        String extraField = userRepo.findExtraField(orgId, appPropertyConfig.getProductId(), sessionList.getLastServiceUser());
                        if (StringUtils.isNotBlank(extraField)) {
                            JSONObject extra = JSON.parseObject(extraField);
                            serviceName = extra.getString("nickName");
                        }
                        warnMap.put("customerName", sessionList.getCustomerName());
                        warnMap.put("serviceName", serviceName);
                        warnMap.put("hitInfo", String.join(",", hitList));
                        warnMap.put("sessionId", sessionList.getId().toString());
                        sendWarnInfo(warnMap);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("检测服务小结, 发送告警信息发生错误, sessionId:{}", sessionList.getId(),e);
            }
        }
    }

    private void sendWarnInfo(Map<String, String> warnMap) {
        try {
            JSONObject data = new JSONObject();
            data.put("msgtype", "markdown");
            StringBuilder sb = new StringBuilder();
            sb.append("<font color=\"warning\">").append("【在线客服敏感小结通知】").append("</font>\n");
            sb.append("用户姓名：").append(warnMap.get("customerName")).append("\n");
            if(warnMap.containsKey("uuid")) {
                sb.append("用户uuid：").append(warnMap.get("uuid")).append("\n");
            }
            sb.append("客服姓名：").append(warnMap.get("serviceName")).append("\n");
            sb.append("人工小结命中敏感词：").append("<font color=\"warning\">").append(warnMap.get("hitInfo")).append("</font>\n");
            data.put("markdown", new JSONObject().fluentPut("content", sb.toString()));
            HttpClientUtil.post(appPropertyConfig.getSummaryWebhookUrl(), data.toJSONString(), 3000);
            LOGGER.info("检测服务小结，sessionId:{}, 发送消息：{}", warnMap.get("sessionId"), sb);
        } catch (Exception e) {
            LOGGER.error("检测服务小结, 发送告警信息发生错误",e);
        }
    }


}
