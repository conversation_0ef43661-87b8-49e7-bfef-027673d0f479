package com.wolaidai.webot.cs.service;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.CsvWriter;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.common.util.JsoupUtil;
import com.wolaidai.webot.common.util.StringUtil;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.model.sessionlist.GlobalSessionExportModel;
import com.wolaidai.webot.cs.model.sessionlist.personal.PersonalSessionRowModel;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.cs.util.ZipUtils;
import com.wolaidai.webot.data.elastic.entity.ChatHistoryElasticEntity;
import com.wolaidai.webot.data.elastic.entity.SessionListElasticEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexChatHistoryElasticRepo;
import com.wolaidai.webot.data.elastic.repo.ComplexSessionListElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ServiceSummaryEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.config.BsTypeSampleEntity;
import com.wolaidai.webot.data.mysql.entity.face.FaceDetectionEntity;
import com.wolaidai.webot.data.mysql.entity.report.SessionStatisticsEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.repo.FaceDetectionRepo;
import com.wolaidai.webot.data.mysql.repo.SatisfactionDataRepo;
import com.wolaidai.webot.data.mysql.repo.ServiceSummaryRepo;
import com.wolaidai.webot.data.mysql.repo.SessionStatisticsRepo;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessTypeRepo;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticSatisfactionEntity;

@Service
public class SessionListService implements TaskUnit {
    final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ComplexChatHistoryElasticRepo complexChatHistoryElasticRepo;

    @Autowired
    private ComplexSessionListElasticRepo complexSessionListElasticRepo;
    @Autowired
    private UserRepo userRepo;
    @Autowired
    private ServiceSummaryRepo serviceSummaryRepo;
    @Autowired
    protected AppPropertyConfig appPropertyConfig;
    @Autowired
    private SessionStatisticsRepo sessionStatisticsRepo;
    @Autowired
    private SessionStatisticsService sessionStatisticsService;
    @Autowired
    private SatisfactionDataRepo satisfactionDataRepo;
    @Autowired
    private BusinessTypeRepo businessTypeRepo;
    @Autowired
    private FaceDetectionRepo faceDetectionRepo;

    @Autowired
    private CustomerInfoEncService customerInfoEncService;

    public List<PersonalSessionRowModel> searchPersonalSession(Integer orgId, String key, String mobile, String clientId, Date startTime, Date endTime) {
        PageRequest pageRequest = PageRequest.of(0, 10000, Sort.Direction.DESC, "createTime");
        SearchHits<SessionListElasticEntity> searchHits = null;
        if (StringUtils.isNotBlank(mobile)) {
            String encMobile = customerInfoEncService.encryptMobile(mobile);
            searchHits = complexSessionListElasticRepo.findRecordByMobile(orgId, encMobile, key, startTime, endTime, pageRequest);
        } else if (StringUtils.isNotBlank(clientId)) {
            searchHits = complexSessionListElasticRepo.findRecordByVisitor(orgId, clientId, key, startTime, endTime, pageRequest);
        }
        List<PersonalSessionRowModel> list = new ArrayList<>();
        if (searchHits != null) {
            List<SessionListElasticEntity> sessionListElasticEntities = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
            for (SessionListElasticEntity elasticEntity : sessionListElasticEntities) {
                String gcid = elasticEntity.getGcid();
                Integer primaryId = elasticEntity.getId();
                PersonalSessionRowModel rowModel = new PersonalSessionRowModel();
                rowModel.setId(primaryId);
                rowModel.setCustomerName(elasticEntity.getCustomerName());
                rowModel.setCreateTime(elasticEntity.getCreateTime());
                rowModel.setDurationTime(DateUtil.formatSecondsTime(elasticEntity.getDurationSecond(), null));
                if (StringUtils.isNotBlank(gcid)) {
                    rowModel.setBotReply(complexChatHistoryElasticRepo.countByGcidAndSenderAndTypeNotAndRecallNot(gcid, ChatHistoryElasticEntity.SENDER_TYPE_BOT, ChatHistoryElasticEntity.TYPE_EVENT));
                    rowModel.setArtificialReply(complexChatHistoryElasticRepo.countByGcidAndSenderAndTypeNotAndRecallNot(gcid, ChatHistoryElasticEntity.SENDER_TYPE_MANUAL, ChatHistoryElasticEntity.TYPE_EVENT));
                    rowModel.setUserReply(complexChatHistoryElasticRepo.countByGcidAndSenderAndTypeNotAndRecallNot(gcid, ChatHistoryElasticEntity.SENDER_TYPE_USER, ChatHistoryElasticEntity.TYPE_EVENT));
                }
                if(elasticEntity.getCustomerDetail() != null && elasticEntity.getCustomerDetail().containsKey("customers")) {
                    customerInfoEncService.decryptCustomerInfo(elasticEntity.getCustomerDetail().getJSONArray("customers"));
                }
                rowModel.setExtraProperties(CommonUtil.maskInfo(elasticEntity.getCustomerDetail()));
                String extraField = userRepo.findExtraField(orgId, appPropertyConfig.getProductId(), elasticEntity.getLastServiceUser());
                if (StringUtils.isNotBlank(extraField)) {
                    JSONObject extra = JSON.parseObject(extraField);
                    rowModel.setLastCustomerService(extra.getString("nickName"));
                }
                rowModel.setBusinessName(elasticEntity.getBusinessName());
                ServiceSummaryEntity serviceSummary = serviceSummaryRepo.findFirstByOrgIdAndSessionId(orgId, primaryId);
                if (serviceSummary != null) {
                    rowModel.setSessionSummaryRemark(serviceSummary.getRemark());
                }
                list.add(rowModel);
            }
        }
        return list;
    }

  


    public void getSessionIdByFaceDetection(Date startTime, Date endTime, Integer faceStatus, List<Integer> includeIds, List<Integer> excludeIds) {
        if (faceStatus == null) {
            return;
        }
        //时间都不传就默认最近一个月
        if (startTime==null && endTime==null) {
            Calendar cal = Calendar.getInstance();
            endTime = cal.getTime();
            cal.add(Calendar.MONTH, -1);
            startTime = cal.getTime();
        }
        if (faceStatus==1) {
            //所有发送的过session_id，后续查询中排除掉这些数据
            excludeIds.addAll(faceDetectionRepo.getSessionIdsByTime(startTime, endTime));
        } else if (faceStatus==2) {
            includeIds.addAll(faceDetectionRepo.getUndoneSessionIdsByTime(startTime, endTime));
        } else if (faceStatus==3) {
            includeIds.addAll(faceDetectionRepo.getSuccessSessionIdsByTime(startTime, endTime));
        } else if (faceStatus==4) {
            includeIds.addAll(faceDetectionRepo.getFailSessionIdsByTime(startTime, endTime));
        }
    }


    public void getSessionIdByMsgCount(JSONObject model, List<Integer> includeIds) {
        if (model.getInteger("userMsgCountMin")==null && model.getInteger("userMsgCountMax")==null && model.getInteger("serviceMsgCountMin")==null && model.getInteger("serviceMsgCountMax")==null) {
            return;
        }
        //时间都不传就默认最近一个月
        Date startTime = model.getDate("startTime");
        Date endTime = model.getDate("endTime");
        if (startTime==null && endTime==null) {
            Calendar cal = Calendar.getInstance();
            endTime = cal.getTime();
            cal.add(Calendar.MONTH, -1);
            startTime = cal.getTime();
        }

        List<Integer> sessionIdList = sessionStatisticsRepo.findIdsByMsgCount(startTime, endTime, model.getInteger("userMsgCountMin"), 
            model.getInteger("userMsgCountMax"), model.getInteger("serviceMsgCountMin"), model.getInteger("serviceMsgCountMax"));
        if(!CollectionUtils.isEmpty(includeIds)) {
            includeIds.retainAll(sessionIdList);
        } else {
            includeIds.addAll(sessionIdList);
        }
    }

    
    private boolean exportSessionRecord(TaskEntity task) throws IOException {
        task.setName("会话记录_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
        CommonUtil.saveTaskProgress(task, 0);
        String[] titles = {"访问IP", "位置", "系统", "业务类型", "姓名", "电话", "uuid", "userId", "新老访客", "咨询渠道",
                "人工接通方式", "会话建立时间", "会话结束时间", "接入人工客服时间", "机器人接待时长", "人工接待时长", "排队时长", "咨询机器人消息数", "机器人回复数", "咨询人工消息数",
                "人工回复数", "撤回消息数", "转接次数", "结束方式", "服务小结业务单元", "服务小结2级分类", "服务小结3级分类", "服务小结4级分类", "服务小结5级分类", "人工小结备注",
                "AI小结备注", "评价类型", "满意度评分","满意度标签","满意度用户评价", "24小时首解", "首次响应超时", "平均响应超时", "会话响应超时", "人脸验证",
                "最后接待客服", "是否逾期", "逾期天数"};
        //每次最多1K条
        Pageable pageRequest = PageRequest.of(0, 1000, Sort.Direction.DESC, "createTime");
        List<File> fileList = new ArrayList<>();
        CsvWriter cw = null;
        File csvFile = null;
        int part = 1;
        Integer orgId = task.getOrgId();
        Map<Integer, BsTypeSampleEntity> typeMap = businessTypeRepo.findByOrgId(orgId).stream().collect(Collectors.toMap(BsTypeSampleEntity::getId, Function.identity()));
        GlobalSessionExportModel model = JSON.toJavaObject(task.getExtraParams(), GlobalSessionExportModel.class);
        long last = System.currentTimeMillis();
        try {
            do {
                List<Integer> includeSessionIds = new ArrayList<>();
                List<Integer> excludeSessionIds = new ArrayList<>();
                getSessionIdByFaceDetection(model.getStartTime(), model.getEndTime(), model.getFaceStatus(), includeSessionIds, excludeSessionIds);
                if(model.getFaceStatus()!=null && Arrays.asList(2,3,4).contains(model.getFaceStatus()) && CollectionUtils.isEmpty(includeSessionIds)) {
                    break;
                }
                //过滤消息数
                getSessionIdByMsgCount((JSONObject)JSON.toJSON(model), includeSessionIds);
                if ((model.getUserMsgCountMin()!=null || model.getUserMsgCountMax()!=null || model.getServiceMsgCountMin()!=null || model.getServiceMsgCountMax()!=null) 
                    && CollectionUtils.isEmpty(includeSessionIds)) {
                    break;
                }
                String encMobile = customerInfoEncService.encryptMobile(model.getMobile());
                SearchHits<SessionListElasticEntity> searchHits = complexSessionListElasticRepo.findRecordByParams(orgId,
                        model.getStaffEmails(), model.getBusinessIds(), model.getClientIds(), model.getAppraiseLevels(),
                        model.getContent(), model.getHasPic(), model.getRespKeys(), model.getStartTime(), model.getEndTime(),
                        model.getUuid(), model.getUserId(), encMobile, model.getCustomerName(),
                        includeSessionIds, excludeSessionIds, pageRequest);
                if (searchHits == null || !searchHits.hasSearchHits()) {
                    break;
                }
                if (csvFile == null) {
                    csvFile = File.createTempFile("会话记录_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + "_part" + part + "_random", ".csv");
                    cw = new CsvWriter(csvFile.getAbsolutePath(), ',', Charset.forName("GBK"));
                    cw.writeRecord(titles);
                }
                List<SessionListElasticEntity> sessionListElastics = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(sessionListElastics)) {
                    Map<Integer, SessionStatisticsEntity> statisticsEntityMap = new HashMap<>();
                    Map<Integer, ServiceSummaryEntity> summaryEntityMap = new HashMap<>();
                    Map<Integer, StatisticSatisfactionEntity> satisfactionEntityMap = new HashMap<>();
                    Map<Integer, Set<Integer>> faceResultMap = new HashMap<>();
                    Set<Integer> sessionIdSet = sessionListElastics.stream().map(SessionListElasticEntity::getId).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(sessionIdSet)) {
                        //查询聚合数据
                        List<SessionStatisticsEntity> statisticsEntities = sessionStatisticsRepo.findBySessionIdIn(sessionIdSet);
                        if (CollectionUtils.isNotEmpty(statisticsEntities)) {
                            statisticsEntityMap = statisticsEntities.stream().collect(Collectors.toMap(SessionStatisticsEntity::getSessionId, Function.identity()));
                        }
                        //查询服务小结
                        List<ServiceSummaryEntity> summaryEntities = serviceSummaryRepo.findByOrgIdAndSessionIdIn(orgId, sessionIdSet);
                        if (CollectionUtils.isNotEmpty(summaryEntities)) {
                            summaryEntityMap = summaryEntities.stream().collect(Collectors.toMap(entity -> entity.getSession().getId(), Function.identity()));
                        }
                        //查询满意度评价
                        List<StatisticSatisfactionEntity> satisfactionEntities = satisfactionDataRepo.findByOrgIdAndStatusAndSessionIdIn(orgId, SatisfactionDataEntity.STATUS_TREATED, sessionIdSet);
                        if (CollectionUtils.isNotEmpty(satisfactionEntities)) {
                            satisfactionEntityMap = satisfactionEntities.stream().collect(Collectors.toMap(StatisticSatisfactionEntity::getSessionId, Function.identity()));
                        }
                        List<FaceDetectionEntity> faceList = faceDetectionRepo.findAllBySessionIdIn(sessionIdSet);
                        if (CollectionUtils.isNotEmpty(faceList)) {
                            faceResultMap = faceList.stream().collect(Collectors.groupingBy(FaceDetectionEntity::getSessionId, Collectors.mapping(FaceDetectionEntity::getCode, Collectors.toSet())));
                        }
                    }
                    for (SessionListElasticEntity elasticEntity : sessionListElastics) {
                        Integer sessionId = elasticEntity.getId();
                        if (sessionId == null) {
                            continue;
                        }
                        Date createTime = elasticEntity.getCreateTime();
                        String[] contents = new String[titles.length];
                        JSONObject customerDetail = elasticEntity.getCustomerDetail();
                        if (customerDetail != null) {
                            contents[0] = customerDetail.getString("ip");
                            JSONObject ipInfo = customerDetail.getJSONObject("ipInfo");
                            if (ipInfo != null) {
                                contents[1] = ipInfo.getString("province");
                            }
                            contents[2] = customerDetail.getString("platform");
                            JSONArray customers = customerDetail.getJSONArray("customers");
                            if (customers != null && customers.size() > 0) {
                                JSONObject customer = customers.getJSONObject(0);
                                contents[5] = CommonUtil.maskPhoneNum(customerInfoEncService.decryptMobile(customer.getString("mobile")));
                                contents[6] = customer.getString("uuid");
                                contents[7] = null!=customer.getInteger("userId")? String.valueOf(customer.getInteger("userId")): "";
                                contents[41] = "";
                                contents[42] = "";
                                if(null != customer.get("isOverdue")) {
                                    contents[41] = customer.getBoolean("isOverdue") ? "是" : "否";
                                }
                                if(null != customer.get("overdueDays") && customer.getInteger("overdueDays") > 0) {
                                    contents[42] = String.valueOf(customer.getInteger("overdueDays"));
                                }
                            }
                        }
                        contents[3] = elasticEntity.getBusinessName();
                        contents[4] = elasticEntity.getCustomerName();
                        contents[9] = CommonUtil.getClientTypeName(elasticEntity.getClientTypeId()); //咨询渠道
                        contents[10] = elasticEntity.getAssignType() == 1 ? "自动分配" : "客服邀请";
                        Date gcTime = elasticEntity.getGcTime();
                        if (gcTime != null) {
                            //会话开始时间
                            contents[11] = DateFormatUtils.format(gcTime, "yyyy-MM-dd HH:mm:ss");
                            //机器人接待时长
                            contents[14] = DateUtil.formatSecondsTime(
                                    (createTime.getTime() - gcTime.getTime()) / 1000, null);
                        }
                        Date offlineTime = elasticEntity.getOfflineTime();
                        if (offlineTime != null) {
                            //会话结束时间
                            contents[12] = DateFormatUtils.format(offlineTime, "yyyy-MM-dd HH:mm:ss");
                            //人工接待时长
                            contents[15] = DateUtil.formatSecondsTime(elasticEntity.getDurationSecond(), null);
                        }
                        //接入人工客服时间
                        contents[13] = DateFormatUtils.format(createTime, "yyyy-MM-dd HH:mm:ss");
                        //排队时长
                        contents[16] = DateUtil.formatSecondsTime(elasticEntity.getWaitSecond(), null);
                        //结束方式,1是客户超时，2客服主动关闭，3客服超时关闭,4用户主动关闭
                        contents[23] = "";
                        Integer closeType = elasticEntity.getCloseType();
                        if (SessionListEntity.CLOSE_TYPE_CUSTOMER_TIMEOUT.equals(closeType)) {
                            contents[23] = "用户超时会话关闭";
                        } else if (SessionListEntity.CLOSE_TYPE_MANUAL.equals(closeType)) {
                            contents[23] = "客服手动关闭";
                        } else if (SessionListEntity.CLOSE_TYPE_CS_TIMEOUT.equals(closeType)) {
                            contents[23] = "客服超时会话关闭";
                        } else if (SessionListEntity.CLOSE_TYPE_CUSTOMER.equals(closeType)) {
                            contents[23] = "用户手动关闭";
                        }
                        SessionStatisticsEntity statisticsEntity = statisticsEntityMap.get(sessionId);
                        if (statisticsEntity == null) {
                            statisticsEntity = sessionStatisticsService.getSessionStatistics(elasticEntity);
                        }
                        if (statisticsEntity == null) {
                            LOGGER.warn("生成会话统计记录异常,sessionId:{}", sessionId);
                            continue;
                        }
                        //新老客户 0-新客户,1-老客户
                        contents[8] = Objects.equals(statisticsEntity.getNewOldCustomer(), 0) ? "新客户" : "老客户";
                        //咨询机器人消息数
                        contents[17] = String.valueOf(statisticsEntity.getSeekBotCnt());
                        //机器人回复数
                        contents[18] = String.valueOf(statisticsEntity.getBotAnswerCnt());
                        //咨询人工消息数
                        contents[19] = String.valueOf(statisticsEntity.getSeekManualCnt());
                        //人工回复数
                        contents[20] = String.valueOf(statisticsEntity.getManualAnswerCnt());
                        //撤回消息数
                        contents[21] = String.valueOf(statisticsEntity.getRecallCnt());
                        //转接次数
                        contents[22] = String.valueOf(statisticsEntity.getTransferCnt());
                        ServiceSummaryEntity serviceSummary = summaryEntityMap.get(sessionId);
                        if (serviceSummary != null) {
                            //服务小结业务单元
                            contents[24] = Optional.ofNullable(serviceSummary.getUnit())
                                    .map(v -> v.getName())
                                    .orElse("");
                            Integer typeId = Optional.ofNullable(serviceSummary.getType())
                                    .map(v -> v.getId())
                                    .orElse(null);
                            BsTypeSampleEntity bsTypeSampleEntity = typeId != null ? typeMap.get(typeId) : null;
                            if (bsTypeSampleEntity != null) {
                                String[] typeTitles = bsTypeSampleEntity.fullPathTitle(typeMap).split(",");
                                if (typeTitles.length > 0) {
                                    //服务小结2级分类
                                    contents[25] = typeTitles[0];
                                }
                                if (typeTitles.length > 1) {
                                    //服务小结3级分类
                                    contents[26] = typeTitles[1];
                                }
                                if (typeTitles.length > 2) {
                                    //服务小结4级分类
                                    contents[27] = typeTitles[2];
                                }
                                if (typeTitles.length > 3) {
                                    //服务小结5级分类
                                    contents[28] = typeTitles[3];
                                }
                            }
                            //小结备注
                            contents[29] = serviceSummary.getRemark();
                            contents[30] = serviceSummary.getAiRemark();
                        }
                        //评价类型
                        contents[31] = "";
                        //满意度评分
                        contents[32] = "";
                        StatisticSatisfactionEntity satisfactionData = satisfactionEntityMap.get(sessionId);
                        if (satisfactionData != null) {
                            if (SatisfactionDataEntity.TYPE_SYSTEM.equals(satisfactionData.getType())) {
                                contents[31] = "系统下发";
                            } else if (SatisfactionDataEntity.TYPE_SERVICEUSER.equals(satisfactionData.getType())) {
                                contents[31] = "客服发起";
                            } else if (SatisfactionDataEntity.TYPE_CUSTOMER.equals(satisfactionData.getType())) {
                                contents[31] = "用户发起";
                            }
                            contents[32] = StringUtil.valueOf(satisfactionData.getLevel(), "");
                            if (null != satisfactionData.getLabels() && satisfactionData.getLabels().size() > 0) {
                                contents[33] = satisfactionData.getLabels().stream().map(i -> String.valueOf(i)).collect(Collectors.joining(","));
                            } else {
                                contents[33] = "";
                            }
                            contents[34] = StringUtil.valueOf(satisfactionData.getContent(), "");
                        }
                        //24小时首解 0-否, 1-是
                        contents[35] = Objects.equals(statisticsEntity.getTwentyFourFirst(), 0) ? "否" : "是";
                        
                        contents[36] = Objects.equals(elasticEntity.getFirstRespTimeout(), 1) ? "是" : "否";
                        contents[37] = Objects.equals(elasticEntity.getAvgRespTimeout(), 1) ? "是" : "否";
                        contents[38] = Objects.equals(elasticEntity.getRespTimeout(), 1) ? "是" : "否";

                        //人脸验证结果
                        Set<Integer> faceResultSet = faceResultMap.get(elasticEntity.getId());
                        String faceStatus = "";
                        if (CollectionUtils.isEmpty(faceResultSet)) {
                            faceStatus = "未发送验证请求";
                        } else if (faceResultSet.contains(0)) {
                            faceStatus = "验证通过";
                        } else if (faceResultSet.stream().anyMatch(v -> v!=null && v>0)){
                            faceStatus = "验证不通过";
                        } else if (faceResultSet.contains(null)) {
                            faceStatus = "用户未验证";
                        }
                        contents[39] = faceStatus;

                        String lastServiceUser = "";
                        String extraField = userRepo.findExtraField(orgId, appPropertyConfig.getProductId(), elasticEntity.getLastServiceUser());
                        if (StringUtils.isNotBlank(extraField)) {
                            JSONObject extra = JSON.parseObject(extraField);
                            lastServiceUser = extra.getString("nickName");
                        }
                        contents[40] = lastServiceUser;

                        cw.writeRecord(contents);
                    }
                }
                pageRequest = pageRequest.next();
                if (pageRequest.getPageNumber() % 30 == 0) {
                    cw.close();
                    fileList.add(csvFile);
                    csvFile = null;
                    part++;
                }
                long now = System.currentTimeMillis();
                if (now - last > 1000) {
                    last = now;
                    CommonUtil.saveTaskProgress(task, Math.min(part, 75));
                }
            } while (pageRequest.getPageNumber() < 150); //暂定只能导出15w记录
            if (csvFile != null) {
                cw.close();
                fileList.add(csvFile);
            }
        } catch (Exception e) {
            LOGGER.error("导出会话记录异常", e);
        } finally {
            if (cw != null) {
                cw.close();
            }
        }
        CommonUtil.saveTaskProgress(task, 75);
        if (CollectionUtils.isNotEmpty(fileList)) {
            if (fileList.size() == 1) { //单个文件直接下载不压缩
                File file = fileList.get(0);
                InputStream fis = new BufferedInputStream(new FileInputStream(file));
                String fileName = "会话记录_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + ".csv";
                task.setName(fileName);
                CommonUtil.saveTaskProgress(task, 90);
                ReportUtil.saveToOss(fis, appPropertyConfig.getOssBucketName(), task);
                file.delete();
                return true;
            } else {
                String fileName = "会话记录_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + ".zip";
                task.setName(fileName);
                CommonUtil.saveTaskProgress(task, 90);
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ZipOutputStream out = new ZipOutputStream(baos);
                for (File file : fileList) {
                    ZipUtils.doCompress(file, out);
                    out.flush();
                    file.delete();
                }
                out.close();
                ReportUtil.saveToOss(new ByteArrayInputStream(baos.toByteArray()), appPropertyConfig.getOssBucketName(), task);
                return true;
            }
        }
        CommonUtil.saveTaskProgress(task, -1);
        return false;
    }

    private boolean exportChatDetail(TaskEntity task) throws IOException {
        task.setName("会话记录_会话内容_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
        CommonUtil.saveTaskProgress(task, 0);
        String[] titles = {"会话ID", "消息时间", "消息来源", "消息内容"};
        Pageable pageRequest = PageRequest.of(0, 1000, Sort.Direction.DESC, "createTime");
        List<File> fileList = new ArrayList<>();
        CsvWriter cw = null;
        File csvFile = null;
        int part = 1;
        Integer orgId = task.getOrgId();
        GlobalSessionExportModel model = JSON.toJavaObject(task.getExtraParams(), GlobalSessionExportModel.class);
        long last = System.currentTimeMillis();
        try {
            do {
                List<Integer> includeSessionIds = new ArrayList<>();
                List<Integer> excludeSessionIds = new ArrayList<>();
                getSessionIdByFaceDetection(model.getStartTime(), model.getEndTime(), model.getFaceStatus(), includeSessionIds, excludeSessionIds);
                if(model.getFaceStatus()!=null && Arrays.asList(2,3,4).contains(model.getFaceStatus()) && CollectionUtils.isEmpty(includeSessionIds)) {
                    break;
                }
                //过滤消息数
                getSessionIdByMsgCount((JSONObject)JSON.toJSON(model), includeSessionIds);
                if ((model.getUserMsgCountMin()!=null || model.getUserMsgCountMax()!=null || model.getServiceMsgCountMin()!=null || model.getServiceMsgCountMax()!=null) 
                    && CollectionUtils.isEmpty(includeSessionIds)) {
                    break;
                }
                String encMobile = customerInfoEncService.encryptMobile(model.getMobile());
                SearchHits<SessionListElasticEntity> searchHits = complexSessionListElasticRepo.findRecordByParams(orgId,
                        model.getStaffEmails(), model.getBusinessIds(), model.getClientIds(), model.getAppraiseLevels(),
                        model.getContent(), model.getHasPic(), model.getRespKeys(), model.getStartTime(), model.getEndTime(),
                        model.getUuid(), model.getUserId(), encMobile, model.getCustomerName(),
                        includeSessionIds, excludeSessionIds, pageRequest);
                if (searchHits.hasSearchHits() || part == 1) {
                    csvFile = File.createTempFile("会话记录_会话内容_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + "_part" + part + "_random", ".csv");
                    cw = new CsvWriter(csvFile.getAbsolutePath(), ',', Charset.forName("GBK"));
                    cw.writeRecord(titles);
                }
                if (!searchHits.hasSearchHits()) {
                    break;
                }
                Map<String, String> gcidCustomerNameMap = new LinkedHashMap<>();
                for (SearchHit<SessionListElasticEntity> searchHit : searchHits) {
                    SessionListElasticEntity sessionList = searchHit.getContent();
                    gcidCustomerNameMap.put(sessionList.getGcid(), sessionList.getCustomerName());
                }
                if (gcidCustomerNameMap.isEmpty()) {
                    break;
                }
                Set<String> gcids = gcidCustomerNameMap.keySet();
                SearchHits<ChatHistoryElasticEntity> partList = complexChatHistoryElasticRepo.findByGcids(gcids);
                ConcurrentMap<String, List<ChatHistoryElasticEntity>> chatHistoryMap = partList.stream().map(SearchHit::getContent).collect(Collectors.groupingByConcurrent(ChatHistoryElasticEntity::getGcid));
                for (String gcid : gcids) {
                    List<ChatHistoryElasticEntity> oneSessionChats = chatHistoryMap.get(gcid);
                    if (null == oneSessionChats) {
                        continue;
                    }
                    oneSessionChats = oneSessionChats.stream().sorted(Comparator.comparing(ChatHistoryElasticEntity::getDate)).collect(Collectors.toList());
                    boolean hasRecord = false;
                    for (ChatHistoryElasticEntity elasticEntity : oneSessionChats) {
                        String type = elasticEntity.getType();
                        //事件消息忽略
                        if (ChatHistoryElasticEntity.TYPE_EVENT.equals(type)) {
                            continue;
                        }
                        String[] contents = new String[4];
                        //会话Id
                        contents[0] = gcid;
                        //消息时间
                        contents[1] = DateFormatUtils.format(elasticEntity.getDate(), "yyyy-MM-dd HH:mm:ss");
                        //消息来源
                        contents[2] = buildMsgFrom(gcid, elasticEntity.getSender(), elasticEntity.getManual(), gcidCustomerNameMap);
                        //消息内容
                        String msg = buildContent(elasticEntity.getContent(), type, elasticEntity.getSender());
                        //撤回消息
                        if (elasticEntity.isRecall()) {
                            msg = "[已撤回]".concat(msg);
                        }
                        contents[3] = msg;
                        cw.writeRecord(contents);
                        hasRecord = true;
                    }
                    //每个会话空一行
                    if(hasRecord) {
                        cw.writeRecord(new String[4]);
                    }
                }
                cw.close();
                fileList.add(csvFile);
                csvFile = null;
                part++;
                pageRequest = pageRequest.next();
                long now = System.currentTimeMillis();
                if (now - last > 1000) {
                    last = now;
                    CommonUtil.saveTaskProgress(task, Math.min(part, 75));
                }
            } while (pageRequest.getPageNumber() < 150);
            if (csvFile != null) {
                cw.close();
                fileList.add(csvFile);
            }
        } catch (Exception e) {
            LOGGER.error("导出会话记录-会话内容异常", e);
        } finally {
            if (cw != null) {
                cw.close();
            }
        }
        CommonUtil.saveTaskProgress(task, 75);
        if (CollectionUtils.isNotEmpty(fileList)) {
            if (fileList.size() == 1) {//单个文件直接下载不压缩
                File file = fileList.get(0);
                InputStream fis = new BufferedInputStream(new FileInputStream(file));
                String fileName = "会话记录_会话内容_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + ".csv";
                task.setName(fileName);
                CommonUtil.saveTaskProgress(task, 90);
                ReportUtil.saveToOss(fis, appPropertyConfig.getOssBucketName(), task);
                file.delete();
                return true;
            } else {
                String fileName = "会话记录_会话内容_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + ".zip";
                task.setName(fileName);
                CommonUtil.saveTaskProgress(task, 90);
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ZipOutputStream out = new ZipOutputStream(baos);
                for (File file : fileList) {
                    ZipUtils.doCompress(file, out);
                    out.flush();
                    file.delete();
                }
                out.close();
                ReportUtil.saveToOss(new ByteArrayInputStream(baos.toByteArray()), appPropertyConfig.getOssBucketName(), task);
                return true;
            }
        }
        CommonUtil.saveTaskProgress(task, -1);
        return false;
    }

    /**
     * 生成聊天内容
     * @param content
     * @param type
     * @param sender
     * @return
     */
    private String buildContent(String content, String type, String sender) {
        boolean mediaContent = Objects.equals(type, ChatHistoryElasticEntity.TYPE_IMAGE)
                || Objects.equals(type, ChatHistoryElasticEntity.TYPE_VIDEO)
                || Objects.equals(type, ChatHistoryElasticEntity.TYPE_VOICE)
                || Objects.equals(type, ChatHistoryElasticEntity.TYPE_FILE);
        if (mediaContent) {
            if (Objects.equals(sender, ChatHistoryElasticEntity.SENDER_TYPE_BOT)) {
                return buildMedialUrl("2", content);
            } else {
                return buildMedialUrl("1", content);
            }
        }
        if (Objects.equals(sender, ChatHistoryElasticEntity.SENDER_TYPE_BOT)
                || Objects.equals(sender, ChatHistoryElasticEntity.SENDER_TYPE_SYSTEM)) {
            return JsoupUtil.text(content);
        }
        return content;
    }

    /**
     * 生成消息来源
     * @param gcid
     * @param sender
     * @param manual
     * @param gcidCustomerNameMap
     * @return
     */
    private String buildMsgFrom(String gcid, String sender, JSONObject manual, Map<String, String> gcidCustomerNameMap) {
        if (Objects.equals(sender, ChatHistoryElasticEntity.SENDER_TYPE_BOT)) {
            return "机器人";
        } else if (Objects.equals(sender, ChatHistoryElasticEntity.SENDER_TYPE_MANUAL)) {
            return "客服-" + (manual == null ? "" : manual.getOrDefault("nickName", ""));
        } else if (Objects.equals(sender, ChatHistoryElasticEntity.SENDER_TYPE_SYSTEM)) {
            return "系统";
        } else {
            return "客户-" + gcidCustomerNameMap.getOrDefault(gcid, "");
        }
    }

    protected String buildMedialUrl(String type, String fileId) {
        return appPropertyConfig.getFileServerUrl() + "/" + type + "/" + fileId;
    }

    @Override
    public boolean run(TaskEntity task) throws Exception {
        if (TaskEntity.SESSION_RECORD.equals(task.getType())) {
            return exportSessionRecord(task);
        } else if (TaskEntity.CHAT_DETAIL.equals(task.getType())) {
            return exportChatDetail(task);
        }
        return false;
    }

}
