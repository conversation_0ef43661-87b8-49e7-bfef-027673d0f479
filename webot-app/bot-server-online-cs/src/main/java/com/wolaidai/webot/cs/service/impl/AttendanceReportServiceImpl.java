package com.wolaidai.webot.cs.service.impl;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.model.report.AttendanceMergeModel;
import com.wolaidai.webot.cs.model.report.AttendanceModel;
import com.wolaidai.webot.cs.model.report.AttendanceRowModel;
import com.wolaidai.webot.cs.service.AttendanceReportService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.MergeUserStateDuration;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.elastic.entity.HistoryManualEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexChatHistoryElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.report.AttendanceReportEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.model.UserStateDuration;
import com.wolaidai.webot.data.mysql.repo.AttendanceReportRepo;
import com.wolaidai.webot.data.mysql.repo.SatisfactionDataRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;

@Service
public class AttendanceReportServiceImpl implements AttendanceReportService {

    final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ComplexChatHistoryElasticRepo chatHistoryElasticRepo;
    @Autowired
    private SatisfactionDataRepo satisfactionDataRepo;
    @Autowired
    private AttendanceReportRepo statisticsAttendanceRepo;
    @Autowired
    private AppPropertyConfig config;
    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    protected AppPropertyConfig appPropertyConfig;

    @Override
    public List<AttendanceReportEntity> generateAttendance(Integer _orgId, Set<String> emails, Date startTime, Date endTime) {
        ArrayList<AttendanceReportEntity> usses = new ArrayList<>();
        HashMap<String, UserInfo> infoMap = new HashMap<>();
        for (Date[] arr : DateUtil.splitDateByDay(startTime, endTime)) {
            HashMap<Integer, HashMap<String, ArrayList<UserStateDuration>>> map = ReportUtil.getUserStateDuration(_orgId, emails, arr[0], arr[1]);
            for (Map.Entry<Integer, HashMap<String, ArrayList<UserStateDuration>>> entry : map.entrySet()) {
                Integer orgId = entry.getKey();
                Map<String, HistoryManualEntity> historyMap = chatHistoryElasticRepo.findManualHistories(orgId, false, arr[0], arr[1]);
                HashMap<String, ArrayList<UserStateDuration>> m = entry.getValue();
                if (m.size() > 0) {
                    List<AttendanceReportEntity> list = processOneDayAttendance(orgId, m, infoMap, historyMap, arr[0], arr[1]);
                    if (null != list && list.size() > 0) {
                        statisticsAttendanceRepo.saveAll(list);
                        usses.addAll(list);
                    }
                }
            }
        }
        return usses;
    }

    private List<AttendanceReportEntity> processOneDayAttendance(Integer orgId, HashMap<String, ArrayList<UserStateDuration>> m, HashMap<String, UserInfo> infoMap, Map<String, HistoryManualEntity> historyMap, Date startTime, Date endTime) {
        List<AttendanceReportEntity> usses = new ArrayList<>();
        ReportUtil.getUserInfoList(orgId, config.getProductId(), infoMap, m.keySet());
        HashMap<String, AttendanceReportEntity> emptyInfoMap = new HashMap<>();
        for (Map.Entry<String, ArrayList<UserStateDuration>> entry : m.entrySet()) {
            String email = entry.getKey();
            AttendanceReportEntity uss = new AttendanceReportEntity();
            uss.setDataTime(startTime);
            uss.setOrgId(orgId);
            uss.setEmail(email);
            UserInfo info = infoMap.get(orgId + email);
            UserExtraField uef = null;
            if (null != info && null != (uef = info.getUserExtraField())) {
                uss.setWorkNumber(uef.getWorkNumber());
                uss.setNickName(uef.getNickName());
            } else {
                emptyInfoMap.put(email, uss);
            }
            MergeUserStateDuration musd = new MergeUserStateDuration(entry.getValue());
            uss.setCreateTime(new Date());
            uss.setFirstLogin(musd.firstLogin);
            uss.setLastLogout(musd.lastLogout);
            uss.setTotalLogin(musd.totalLogin);
            uss.setTotalOnline(musd.totalOnline);
            uss.setTotalBusy(musd.totalBusy);
            uss.setTotalRest(musd.totalRest);
            uss.setTotalLeave(musd.totalLeave);
            uss.setTotalEat(musd.totalEat);
            uss.setTotalStudy(musd.totalStudy);

            List<SessionListEntity> sessions = ReportUtil.getSessionList(false, orgId, Arrays.asList(email), startTime, endTime);
            if (null != sessions && sessions.size() > 0) {
                uss.setSessionCount(sessions.size());
                long totalDuration = 0;
                long totalFirstResponseTime = 0;
                float totalAVGResponseTime = 0;
                HashSet<Integer> sessionIds = new HashSet<>();
                for (SessionListEntity s : sessions) {
                    if (!Objects.equals(s.getStatus(), SessionListEntity.STATUS_OFFLINE)) {
                        continue;
                    }
                    totalDuration += ReportUtil.getSessionDurationSeconds(email, s);
                    HistoryManualEntity history = historyMap.get(email + s.getSessionKey());
                    if (null != history) {
                        if (null != history.getFirstResponseTime()) {
                            totalFirstResponseTime += history.getFirstResponseTime();
                        }
                        if (null != history.getAvgResponseTime()) {
                            totalAVGResponseTime += history.getAvgResponseTime();
                        }
                    }
                    sessionIds.add(s.getId());
                }
                uss.setSessionDuration(totalDuration);
                uss.setSessionCostAvg(totalDuration / sessions.size());
                uss.setTotalFirstRsp(totalFirstResponseTime);
                uss.setFirstAnswerAvg((int) (totalFirstResponseTime / sessions.size()));
                uss.setTotalAvgRsp(totalAVGResponseTime);
                uss.setAnswerAvg((int) (totalAVGResponseTime / sessions.size()));

                HashMap<Integer, Integer> levelMap = new HashMap<>();
                List<SatisfactionDataEntity> sds = satisfactionDataRepo.findByOrgIdAndSessionIdInAndCreateTimeGreaterThanEqualAndCreateTimeLessThan(orgId, sessionIds, startTime, endTime);
                for (SatisfactionDataEntity sd : sds) {
                    if (null != sd.getLevel()) {
                        Integer count = levelMap.get(sd.getLevel());
                        if (null == count) {
                            levelMap.put(sd.getLevel(), 1);
                        } else {
                            levelMap.put(sd.getLevel(), count + 1);
                        }
                    }
                }
                uss.setStarOne(null != levelMap.get(1) ? levelMap.get(1) : 0);
                uss.setStarTwo(null != levelMap.get(2) ? levelMap.get(2) : 0);
                uss.setStarThree(null != levelMap.get(3) ? levelMap.get(3) : 0);
                uss.setStarFour(null != levelMap.get(4) ? levelMap.get(4) : 0);
                uss.setStarFive(null != levelMap.get(5) ? levelMap.get(5) : 0);
                uss.setEvaluationCount(uss.getStarOne() + uss.getStarTwo() + uss.getStarThree() + uss.getStarFour() + uss.getStarFive());
                uss.setEvaluationPercent(ReportUtil.getPercent(uss.getEvaluationCount(), sessions.size()));
                uss.setNoEvaluationCount(sessions.size() - uss.getEvaluationCount());
                uss.setNoEvaluationPercent(ReportUtil.getPercent(uss.getNoEvaluationCount(), sessions.size()));
                uss.setSatisfactionPercent(ReportUtil.getPercent(uss.getStarFour() + uss.getStarFive(), uss.getEvaluationCount()));
            }
            usses.add(uss);
        }
        if (emptyInfoMap.size() > 0) {
            List<UserStateEntity> list = userStateRepo.findByOrgIdAndEmailInAndCreateTimeGreaterThanEqual(orgId, emptyInfoMap.keySet(), startTime);
            for (UserStateEntity s : list) {
                AttendanceReportEntity ss = emptyInfoMap.get(s.getEmail());
                ss.setWorkNumber(s.getWorkNumber());
                ss.setNickName(s.getNickName());
            }
        }
        return usses;
    }

    @Override
    public void generateReportByDate(Integer orgId, Date startTime, Date endTime) {
        if (null != orgId) {
            statisticsAttendanceRepo.deleteByOrgIdAndDataTime(orgId, startTime, endTime);
        } else {
            statisticsAttendanceRepo.deleteByDataTime(startTime, endTime);
        }
        generateAttendance(orgId, null, startTime, endTime);
    }

    @Override
    public String getReportName() {
        return "考勤统计";
    }

    @Override
    public void loadData(Integer orgId, Object m) {
        AttendanceModel model = (AttendanceModel) m;
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);
        List<AttendanceReportEntity> result = new ArrayList<>();
        if (today.getTime() >= model.getBeginDate().getTime() && today.before(model.getEndDate())) {
            result = generateAttendance(orgId, model.getEmails(), today, new Date());
        }
        if (model.getBeginDate().before(today)) {
            Date endDate = model.getEndDate().after(today) ? DateUtils.addMilliseconds(today, -1) : model.getEndDate();
            List<AttendanceReportEntity> list = null;
            if (model.getEmails().size() > 0) {
                list = statisticsAttendanceRepo.findByOrgIdAndEmailInAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAscNickNameAsc(orgId, model.getEmails(), model.getBeginDate(), endDate);
            } else {
                list = statisticsAttendanceRepo.findByOrgIdAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAscNickNameAsc(orgId, model.getBeginDate(), endDate);
            }
            result.addAll(list);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (Objects.equals(model.getType(), 2)) {
            Map<String, List<AttendanceReportEntity>> map = result.stream().collect(Collectors.groupingBy(AttendanceReportEntity::getEmail, Collectors.toList()));
            String date = String.format("%s ~ %s", sdf.format(model.getBeginDate()), sdf.format(model.getEndDate()));
            for (Map.Entry<String, List<AttendanceReportEntity>> entry : map.entrySet()) {
                model.getList().add(new AttendanceRowModel(new AttendanceMergeModel(date, entry.getValue())));
            }
        } else {
            for (AttendanceReportEntity s : result) {
                model.getList().add(new AttendanceRowModel(s, sdf));
            }
        }
    }

    @Override
    public boolean run(TaskEntity task) throws IOException {
        AttendanceModel model = JSON.toJavaObject(task.getExtraParams(), AttendanceModel.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = String.format("考勤_%s_%s.xlsx", sdf.format(model.getBeginDate()), sdf.format(model.getEndDate()));
        task.setName(fileName);
        CommonUtil.saveTaskProgress(task, 0);
        loadData(task.getOrgId(), model);
        CommonUtil.saveTaskProgress(task, 75);
        Map<String, List<AttendanceRowModel>> map = model.getList().stream().collect(Collectors.groupingBy(i -> i.getDate()));
        Map<String, JSONArray> map2 = new TreeMap<>(Comparator.comparing(i -> i));
        for (Map.Entry<String, List<AttendanceRowModel>> entry : map.entrySet()) {
            map2.put(entry.getKey(), (JSONArray) JSON.toJSON(entry.getValue()));
        }
        LinkedHashMap<String, String> headerMap = new LinkedHashMap<>();
        headerMap.put("date", "日期");
        headerMap.put("workNumber", "工号");
        headerMap.put("nickName", "姓名");
        if (!Objects.equals(model.getType(), 2)) {
            headerMap.put("firstLogin", "首次上线时间");
            headerMap.put("lastLogout", "末次下线时间");
        }
        headerMap.put("totalLogin", "线上总时长");
        headerMap.put("totalOnline", "在线总时长");
        headerMap.put("totalBusy", "忙碌总时长");
        headerMap.put("totalRest", "小休总时长");
        headerMap.put("totalLeave", "离开总时长");
        headerMap.put("totalEat", "用餐总时长");
        headerMap.put("totalStudy", "培训总时长");
        headerMap.put("sessionCount", "总会话数");
        headerMap.put("sessionDuration", "总会话时长");
        headerMap.put("sessionCostAvg", "平均处理时长");
        headerMap.put("firstAnswerAvg", "平均首次响应时长");
        headerMap.put("answerAvg", "平均响应时长");
        headerMap.put("evaluationCount", "评价数");
        headerMap.put("evaluationPercent", "评价率");
        headerMap.put("noEvaluationCount", "未评数");
        headerMap.put("noEvaluationPercent", "未评率");
        headerMap.put("starOne", "1星");
        headerMap.put("starTwo", "2星");
        headerMap.put("starThree", "3星");
        headerMap.put("starFour", "4星");
        headerMap.put("starFive", "5星");
        headerMap.put("satisfactionPercent", "满意度");
        XSSFWorkbook sheets = ReportUtil.convertToExcel(map2, headerMap);
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(sheets, appPropertyConfig.getOssBucketName(), task);
        return true;
    }

}
