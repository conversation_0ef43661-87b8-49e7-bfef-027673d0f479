package com.wolaidai.webot.cs.model.innerchat;

import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import io.swagger.annotations.ApiModelProperty;

public class InnerMemberInfoModel extends InnerServiceUserModel {

    @ApiModelProperty(value = "是否群主")
    private boolean owner;

    public boolean isOwner() {
        return owner;
    }

    public void setOwner(boolean owner) {
        this.owner = owner;
    }

    public InnerMemberInfoModel() {
    }

    public InnerMemberInfoModel(UserStateEntity entity) {
       super(entity);
    }
}
