package com.wolaidai.webot.cs.model.blacklist;

import java.util.List;

import com.wolaidai.webot.cs.model.BaseModel;

public class BlackListUpdateModel extends BaseModel {
    private List<Integer> ids;
    private Integer status;
    private Integer validDays;
    private String remark;

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getValidDays() {
        return validDays;
    }

    public void setValidDays(Integer validDays) {
        this.validDays = validDays;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
