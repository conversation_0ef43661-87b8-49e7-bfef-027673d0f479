package com.wolaidai.webot.cs.model.quickreply;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "快捷回复文本列表")
public class QuickReplyTextResponseModel extends BaseModel {

    private List<QuickReplyTextDataModel> list = new ArrayList<>();

    public List<QuickReplyTextDataModel> getList() {
        return list;
    }

    public void setList(List<QuickReplyTextDataModel> list) {
        this.list = list;
    }
}
