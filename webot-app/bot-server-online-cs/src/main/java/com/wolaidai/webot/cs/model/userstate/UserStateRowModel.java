package com.wolaidai.webot.cs.model.userstate;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import io.swagger.annotations.ApiModel;

import java.util.Date;

@ApiModel(description = "用户状态返回数据")
public class UserStateRowModel extends BaseModel {
    private String email;
    private String name;
    private String nickName;
    private String workNumber;
    private Integer state;
    //最后上线时间
    private Date lastActiveTime;

    public UserStateRowModel(UserStateEntity u, String name, String nickName, String workNumber) {
        this.email = u.getEmail();
        this.name = name;
        this.nickName = nickName;
        this.workNumber = workNumber;
        this.state = u.getState();
        this.lastActiveTime = u.getCreateTime();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(Date lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }
}
