package com.wolaidai.webot.cs.model.sessionlist;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "公共历史会话记录返回数据")
public class GlobalSessionRowModel extends BaseModel {
    @ApiModelProperty(value = "会话ID")
    private Integer id;
    @ApiModelProperty(value = "客户姓名")
    private String customerName;
    @ApiModelProperty(value = "uuid")
    private String uuid;
    @ApiModelProperty(value = "userId")
    private Integer userId;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "会话开始时间")
    private Date startTime;
    @ApiModelProperty(value = "会话结束时间")
    private Date endTime;
    @ApiModelProperty(value = "会话持续时间")
    private String durationTime;
    @ApiModelProperty(value = "会话排队时间")
    private String waitTime;
    @ApiModelProperty(value = "机器人回复数")
    private long botReply;
    @ApiModelProperty(value = "人工回复数")
    private long artificialReply;
    @ApiModelProperty(value = "用户回复数")
    private long userReply;
    @ApiModelProperty(value = "最后接待客服")
    private String lastCustomerService;
    @ApiModelProperty(value = "业务名")
    private String businessName;
    @ApiModelProperty(value = "是否参评:1-已参评,0-未下发,-1-下发未参评")
    private Integer appraiseStatus = 0;
    @ApiModelProperty(value = "评价,1-5,null-未评价")
    private Integer appraiseLevel;
    @ApiModelProperty(value = "是否有图片,1-有,0-无")
    private int hasPic;
    @ApiModelProperty(value = "咨询渠道")
    private Integer clientId;
    @ApiModelProperty(value = "首次响应超时")
    private Integer firstRespTimeout;
    @ApiModelProperty(value = "平均响应超时")
    private Integer avgRespTimeout;
    @ApiModelProperty(value = "会话响应超时")
    private Integer respTimeout;
    @ApiModelProperty(value = "人脸验证")
    private String faceStatus;
    

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(String durationTime) {
        this.durationTime = durationTime;
    }

    public String getWaitTime() {
        return waitTime;
    }

    public void setWaitTime(String waitTime) {
        this.waitTime = waitTime;
    }

    public long getBotReply() {
        return botReply;
    }

    public void setBotReply(long botReply) {
        this.botReply = botReply;
    }

    public long getArtificialReply() {
        return artificialReply;
    }

    public void setArtificialReply(long artificialReply) {
        this.artificialReply = artificialReply;
    }

    public long getUserReply() {
        return userReply;
    }

    public void setUserReply(long userReply) {
        this.userReply = userReply;
    }

    public String getLastCustomerService() {
        return lastCustomerService;
    }

    public void setLastCustomerService(String lastCustomerService) {
        this.lastCustomerService = lastCustomerService;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Integer getAppraiseLevel() {
        return appraiseLevel;
    }

    public void setAppraiseLevel(Integer appraiseLevel) {
        this.appraiseLevel = appraiseLevel;
    }

    public int getHasPic() {
        return hasPic;
    }

    public void setHasPic(int hasPic) {
        this.hasPic = hasPic;
    }

    public Integer getClientId() {
        return clientId;
    }

    public void setClientId(Integer clientId) {
        this.clientId = clientId;
    }

    public Integer getAppraiseStatus() {
        return appraiseStatus;
    }

    public void setAppraiseStatus(Integer appraiseStatus) {
        this.appraiseStatus = appraiseStatus;
    }

    public Integer getFirstRespTimeout() {
        return firstRespTimeout;
    }

    public void setFirstRespTimeout(Integer firstRespTimeout) {
        this.firstRespTimeout = firstRespTimeout;
    }

    public Integer getAvgRespTimeout() {
        return avgRespTimeout;
    }

    public void setAvgRespTimeout(Integer avgRespTimeout) {
        this.avgRespTimeout = avgRespTimeout;
    }

    public Integer getRespTimeout() {
        return respTimeout;
    }

    public void setRespTimeout(Integer respTimeout) {
        this.respTimeout = respTimeout;
    }

    public String getFaceStatus() {
        return faceStatus;
    }

    public void setFaceStatus(String faceStatus) {
        this.faceStatus = faceStatus;
    }

    
}
