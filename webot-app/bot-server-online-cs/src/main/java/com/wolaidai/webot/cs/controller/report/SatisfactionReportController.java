package com.wolaidai.webot.cs.controller.report;

import java.util.Date;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.report.ReportReqModel;
import com.wolaidai.webot.cs.model.report.SatisfactionLabelModel;
import com.wolaidai.webot.cs.service.SatisfactionReportService;
import com.wolaidai.webot.cs.service.TaskService;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.security.domain.UserDomain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "满意度统计报表接口")
@RestController
@RequestMapping("/report/satisfaction")
public class SatisfactionReportController extends BaseController {

    @Autowired
    private SatisfactionReportService satisfactionReportService;
    @Autowired
    private TaskService taskService;

    @GetMapping
    @ApiOperation(value = "满意度统计")
    public ResponseModel satisfactionReport(ReportReqModel reportReqModel) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        Date startTime = reportReqModel.getStartTime();
        Date endTime = reportReqModel.getEndTime();
        if (startTime == null || endTime == null || startTime.after(endTime)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "请提供正确的时间段");
        }
        Map<String, Object> result = satisfactionReportService.getSatisfactionReport(orgId, startTime, endTime);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "success", result);
    }

    @GetMapping("/export")
    @ApiOperation(value = "满意度统计导出")
    public ResponseModel satisfactionReportExport(ReportReqModel reportReqModel) {
        UserDomain user = getUser();
        Date startTime = reportReqModel.getStartTime();
        Date endTime = reportReqModel.getEndTime();
        if (startTime == null || endTime == null || startTime.after(endTime)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "请提供正确的时间段");
        }
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.SATISFACTION_REPORT, TaskEntity.PORT_EXPORT, reportReqModel, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }

    @GetMapping("/labels")
    @ApiOperation(value = "满意度标签统计")
    public ResponseModel satisfactionLabelsReport(SatisfactionLabelModel model) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        Date begin = model.getBeginDate();
        Date end = model.getEndDate();
        if (begin == null || end == null || begin.after(end)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "请提供正确的时间段");
        }
        satisfactionReportService.loadData(orgId, model);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "success", model);
    }

    @GetMapping("/labels/export")
    @ApiOperation(value = "导出满意度标签统计")
    public ResponseModel exportSatisfactionLabelsReport(SatisfactionLabelModel model) {
        UserDomain user = getUser();
        Date begin = model.getBeginDate();
        Date end = model.getEndDate();
        if (begin == null || end == null || begin.after(end)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "请提供正确的时间段");
        }
        String err = taskService.submitTask(user.getOrganizationId(), TaskEntity.SATISFACTION_LABEL_REPORT, TaskEntity.PORT_EXPORT, model, user.getEmail());
        if (null != err) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, err);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "导出任务创建成功，请稍后到下载中心进行下载");
    }
}
