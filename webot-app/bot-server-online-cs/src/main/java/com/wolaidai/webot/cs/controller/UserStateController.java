package com.wolaidai.webot.cs.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.entity.Event;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.userstate.MaxReceptionUpdateModel;
import com.wolaidai.webot.cs.model.userstate.UserStateRowModel;
import com.wolaidai.webot.cs.model.userstate.UserStateSearchModel;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateHistoryEntity;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.repo.UserStateHistoryRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import com.wolaidai.webot.security.domain.UserDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Api(tags = "用户状态接口")
@RestController
@RequestMapping("/userState")
public class UserStateController extends BaseController {

    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    private UserStateHistoryRepo userStateHistoryRepo;
    @Autowired
    private UserRepo userRepo;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ConfigService configService;

    @GetMapping
    @ApiOperation(value = "获取当前用户状态", response = UserStateRowModel.class)
    public ResponseModel getUserState() {
        UserDomain user = getUser();
        UserExtraField uef = getUserExtraField();
        String nickName = null;
        String workNumber = null;
        if (null != uef) {
            nickName = uef.getNickName();
            workNumber = uef.getWorkNumber();
        }
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);
        UserStateEntity u = userStateRepo.findByOrgIdAndEmail(user.getOrganizationId(), user.getEmail());
        if (u==null||!DateUtils.isSameDay(today,u.getCreateTime())||Objects.equals(u.getState(),UserStateEntity.STATE_OFFLINE)){
            RLock lock = redissonClient.getLock(RedisKey.LOCK_KEY + "user_state:" + getUser().getEmail());
            try {
                boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
                if (res) {
                    if(u==null) {
                        u = new UserStateEntity();
                        u.setOrgId(user.getOrganizationId());
                        u.setEmail(user.getEmail());
                        u.setCreateTime(new Date());
                    }else{
                        if(!DateUtils.isSameDay(today,u.getCreateTime())){
                            u.setCreateTime(new Date());
                        }
                    }
                    u.setUpdateTime(new Date());
                    u.setUserType(getRoleType());
                    u.setState(UserStateEntity.STATE_LOGIN);
                    u.setName(user.getFullName());
                    u.setNickName(nickName);
                    u.setWorkNumber(workNumber);
                    userStateRepo.save(u);
                    UserStateHistoryEntity history = new UserStateHistoryEntity();
                    history.setCreateTime(new Date());
                    history.setEmail(user.getEmail());
                    history.setOrgId(user.getOrganizationId());
                    history.setState(u.getState());
                    history.setUserType(u.getUserType());
                    userStateHistoryRepo.save(history);
                }
            }catch (Exception e){
                LOGGER.error("set userState error",e);
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "获取当前客服状态操作繁忙，请稍后重试");
            }finally {
                if(lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        UserStateRowModel model = new UserStateRowModel(u, user.getFullName(), nickName, workNumber);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/list")
    @ApiOperation(value = "获取用户状态列表", response = UserStateSearchModel.class)
    public ResponseModel getUserStateList(UserStateSearchModel model) {
        UserDomain user = getUser();
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);
        List<UserStateEntity> states = userStateRepo.findByOrgIdAndUserTypeAndCreateTimeGreaterThanEqual(user.getOrganizationId(), UserStateEntity.TYPE_NORMAL, today);
        HashMap<String, UserStateRowModel> map = new HashMap<>();
        for (UserStateEntity u : states) {
            UserStateRowModel m = new UserStateRowModel(u, null, null, null);
            map.put(u.getEmail(), m);
            model.getList().add(m);
        }
        List<UserInfo> list = userRepo.find(user.getOrganizationId(), appPropertyConfig.getProductId(), map.keySet());
        for (UserInfo i : list) {
            UserStateRowModel m = map.get(i.getEmail());
            m.setName(i.getFullName());
            m.setNickName(i.getUserExtraField().getNickName());
            m.setWorkNumber(i.getUserExtraField().getWorkNumber());
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    private int getMinCustomerServiceCount(Integer orgId) {
        String status = configService.read(orgId, String.format(RedisKey.WORKBENCH_CONFIG_STATUS, orgId), "onlineCustomer", CommonConfigEntity.TYPE_WORKBENCH_CONFIG_STATUS, "onlineCustomer");
        if (null != status && Objects.equals(status, "true")) {
            String config = configService.read(orgId, String.format(RedisKey.WORKBENCH_CONFIG, orgId), "onlineCustomer", CommonConfigEntity.TYPE_WORKBENCH_CONFIG, "onlineCustomer");
            if (StringUtils.isNotBlank(config)) {
                JSONArray configList = JSON.parseObject(config).getJSONArray("configList");
                for (int i = 0; i < configList.size(); i++) {
                    JSONObject part = configList.getJSONObject(i);
                    if (DateUtil.isNowBetween(part.getString("startTime"), part.getString("endTime"))) {
                        return part.getIntValue("target");
                    }
                }
            }
        }
        return -1;
    }

    @PutMapping("/{state}")
    @ApiOperation(value = "设置用户状态")
    public ResponseModel changeUserState(@PathVariable Integer state, Integer check) {
        UserDomain user = getUser();
        RLock lock = redissonClient.getLock(RedisKey.LOCK_KEY + "user_state:"+ getUser().getEmail());
        try {
            boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
            if (res) {
                if (null == state || state < 1 || state > 7) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "状态不合法");
                }
                Date now = new Date();
                UserStateEntity u = userStateRepo.findByOrgIdAndEmail(user.getOrganizationId(), user.getEmail());
                if (null != u && DateUtils.isSameDay(u.getCreateTime(), now) && (Objects.equals(state, u.getState()))) {
                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
                }
                if(!Objects.equals(check,0)) {
                    if (null != u && Objects.equals(u.getState(), UserStateEntity.STATE_ONLINE) && DateUtils.isSameDay(u.getCreateTime(), now)) {
                        int minCount = getMinCustomerServiceCount(user.getOrganizationId());
                        if (minCount > 0) {
                            Integer onlineCount = userStateRepo.countByOrgIdAndUserTypeAndStateAndCreateTimeGreaterThanEqual(user.getOrganizationId(), UserStateEntity.TYPE_NORMAL, UserStateEntity.STATE_ONLINE, DateUtils.truncate(now, Calendar.DATE));
                            if (null != onlineCount && onlineCount <= minCount) {
                                if(Objects.equals(check,1)){
                                    return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, String.format("目前在线人工数少于系统设置要求(%s人)，暂不能切换工作状态！", minCount),1);
                                }
                                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, String.format("目前在线人工数少于系统设置要求(%s人)，暂不能切换工作状态！", minCount));
                            }
                        }
                    }
                }
                if (null == u) {
                    u = new UserStateEntity();
                    u.setCreateTime(now);
                    u.setOrgId(user.getOrganizationId());
                    u.setEmail(user.getEmail());
                } else if (!DateUtils.isSameDay(u.getCreateTime(), now)) {
                    u.setCreateTime(now);
                }
                u.setUserType(getRoleType());
                u.setState(state);
                u.setUpdateTime(now);
                UserExtraField uef = getUserExtraField();
                String nickName = null;
                String workNumber = null;
                if (null != uef) {
                    nickName = uef.getNickName();
                    workNumber = uef.getWorkNumber();
                }
                u.setName(user.getFullName());
                u.setNickName(nickName);
                u.setWorkNumber(workNumber);
                userStateRepo.save(u);
                UserStateHistoryEntity history = new UserStateHistoryEntity();
                history.setCreateTime(now);
                history.setEmail(user.getEmail());
                history.setOrgId(user.getOrganizationId());
                history.setState(state);
                history.setUserType(u.getUserType());
                userStateHistoryRepo.save(history);
                if (Objects.equals(u.getState(), UserStateEntity.STATE_ONLINE)) {
                    //非管理员
                    if (!Objects.equals(u.getUserType(), UserStateEntity.TYPE_MANAGER)) {
                        String key = String.format(RedisKey.USER_ONLINE_LIST, getUser().getOrganizationId());
                        redisTemplate.opsForZSet().add(key, user.getEmail(), System.currentTimeMillis());
                        redisTemplate.expireAt(key, DateUtils.truncate(DateUtils.addDays(new Date(), 1), Calendar.DATE));
                    }
                } else {
                    redisTemplate.opsForZSet().remove(String.format(RedisKey.USER_ONLINE_LIST, getUser().getOrganizationId()), user.getEmail());
                }
                List<UserStateEntity> states = userStateRepo.findByOrgIdAndUserTypeAndCreateTimeGreaterThanEqual(user.getOrganizationId(), UserStateEntity.TYPE_NORMAL, DateUtils.truncate(new Date(), Calendar.DATE));
                int totalQueueSize = states.size();
                int currentQueueSize = (int) states.stream().filter(i -> Objects.equals(i.getState(),UserStateEntity.STATE_ONLINE)).count();
                redisTemplate.opsForList().rightPush(RedisKey.EVENT_SERVER_KEY, JSONObject.toJSONString(new Event(UUID.randomUUID().toString(),Event.USER_STATE_KEY,user.getOrganizationId(),
                        new JSONObject().fluentPut("email",user.getEmail()).fluentPut("state",u.getState()).fluentPut("currentQueueSize",currentQueueSize).fluentPut("totalQueueSize",totalQueueSize),new Date())));
            }
        }catch (Exception e){
            LOGGER.error("set userState error",e);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "设置当前客服状态操作繁忙，请稍后重试");
        }finally {
            if(lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }
    
    @PutMapping("/maxReception/update")
    @ApiOperation(value = "设置最大接待上限")
    public ResponseModel changeUserMaxReception(@Valid @RequestBody MaxReceptionUpdateModel model) {
        if (StringUtils.isBlank(model.getEmail())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "客服email不能为空");
        }
        if (null == model.getMaxReception() || model.getMaxReception() < 1) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "最大接待上限数值不能小于1");
        }
        Integer orgId = getUser().getOrganizationId();
        List<UserInfo> list = userRepo.find(orgId, appPropertyConfig.getProductId(), Arrays.asList(model.getEmail()));
        if (list.size() == 0) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "该客服邮箱不存在");
        }
        redisTemplate.opsForHash().delete(String.format(RedisKey.USER_MAX_RECEPTION, orgId), model.getEmail());
        configService.save(orgId, String.valueOf(model.getMaxReception()), CommonConfigEntity.TYPE_MAX_RECEPTION, model.getEmail());
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }
}
