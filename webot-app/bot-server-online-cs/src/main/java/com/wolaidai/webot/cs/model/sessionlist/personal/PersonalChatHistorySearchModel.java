package com.wolaidai.webot.cs.model.sessionlist.personal;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "个人聊天历史记录")
public class PersonalChatHistorySearchModel extends BaseModel {

    private long allCount = 0;
    private long csCount = 0;
    private long botCount = 0;
    private List<PersonalChatHistoryRowModel> allList = new ArrayList<>();
    private List<PersonalChatHistoryRowModel> csList = new ArrayList<>();
    private List<PersonalChatHistoryRowModel> botList = new ArrayList<>();
    private List<PersonalChatHistoryRowModel> mediaList = new ArrayList<>();

    public long getAllCount() {
        return allCount;
    }

    public void setAllCount(long allCount) {
        this.allCount = allCount;
    }

    public long getCsCount() {
        return csCount;
    }

    public void setCsCount(long csCount) {
        this.csCount = csCount;
    }

    public long getBotCount() {
        return botCount;
    }

    public void setBotCount(long botCount) {
        this.botCount = botCount;
    }

    public List<PersonalChatHistoryRowModel> getAllList() {
        return allList;
    }

    public void setAllList(List<PersonalChatHistoryRowModel> allList) {
        this.allList = allList;
    }

    public List<PersonalChatHistoryRowModel> getCsList() {
        return csList;
    }

    public void setCsList(List<PersonalChatHistoryRowModel> csList) {
        this.csList = csList;
    }

    public List<PersonalChatHistoryRowModel> getBotList() {
        return botList;
    }

    public void setBotList(List<PersonalChatHistoryRowModel> botList) {
        this.botList = botList;
    }

    public List<PersonalChatHistoryRowModel> getMediaList() {
        return mediaList;
    }

    public void setMediaList(List<PersonalChatHistoryRowModel> mediaList) {
        this.mediaList = mediaList;
    }
}
