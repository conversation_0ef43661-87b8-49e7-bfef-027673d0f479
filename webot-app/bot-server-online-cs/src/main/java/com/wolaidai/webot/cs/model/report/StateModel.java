package com.wolaidai.webot.cs.model.report;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.wolaidai.webot.cs.model.BaseModel;

public class StateModel extends BaseModel {

    private Set<String> emails = new HashSet<>();
    private Date beginDate;
    private Date endDate;
    private List<StateRowModel> list = new ArrayList<>();

    public Set<String> getEmails() {
        return emails;
    }

    public void setEmails(Set<String> emails) {
        this.emails = emails;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<StateRowModel> getList() {
        return list;
    }

    public void setList(List<StateRowModel> list) {
        this.list = list;
    }

}
