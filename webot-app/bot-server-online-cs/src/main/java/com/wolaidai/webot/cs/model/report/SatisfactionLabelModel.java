package com.wolaidai.webot.cs.model.report;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.wolaidai.webot.cs.model.BaseModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "满意度标签统计查询参数")
public class SatisfactionLabelModel extends BaseModel {

    @ApiModelProperty(value = "客服邮箱列表")
    private Set<String> emails = new HashSet<>();
    @ApiModelProperty(value = "开始时间")
    private Date beginDate;
    @ApiModelProperty(value = "结束时间")
    private Date endDate;
    private List<SatisfactionLabelRowModel> list = new ArrayList<>();

    public Set<String> getEmails() {
        return emails;
    }

    public void setEmails(Set<String> emails) {
        this.emails = emails;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<SatisfactionLabelRowModel> getList() {
        return list;
    }

    public void setList(List<SatisfactionLabelRowModel> list) {
        this.list = list;
    }

}
