package com.wolaidai.webot.cs.model.blacklist;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.blacklist.BlackListEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;

public class BlackListRowModel extends BaseModel {
    private Integer id;
    private String name;
    private String phone;
    private String clientId;
    private Integer clientType;
    private String remark;
    private String creator;
    private Integer status = BlackListEntity.STATUS_UNCHECK;
    private Integer validDays = -1;
    private String reviewRemark;
    private String reviewCreator;
    private String cancelCreator;
    private Date createTime;
    private Date updateTime;
    private Date expireTime;
    private Date cancelTime;

    public BlackListRowModel(BlackListEntity b, boolean markPhone, UserInfo creatorInfo, UserInfo cancelInfo) {
        this.id = b.getId();
        this.name = b.getName();
        this.phone = b.getPhone();
        this.clientId = b.getClientId();
        this.clientType = b.getClientType();
        this.remark = b.getRemark();
        this.creator = b.getCreator();
        this.status = b.getStatus();
        this.validDays = b.getValidDays();
        this.reviewRemark = b.getReviewRemark();
        this.reviewCreator = b.getReviewCreator();
        this.cancelCreator = b.getCancelCreator();
        this.createTime = b.getCreateTime();
        this.updateTime = b.getUpdateTime();
        this.expireTime = b.getExpireTime();
        this.cancelTime = b.getCancelTime();
        if (markPhone && StringUtils.isNotBlank(this.phone) && this.phone.length() > 6) {
            this.phone = CommonUtil.maskPhoneNum(this.phone);
        }
        UserExtraField uef = null, uef2 = null;
        if (null != creatorInfo && null != (uef = creatorInfo.getUserExtraField()) && StringUtils.isNotBlank(uef.getNickName())) {
            this.creator = uef.getNickName();
        }
        if (null != cancelInfo && null != (uef2 = cancelInfo.getUserExtraField()) && StringUtils.isNotBlank(uef2.getNickName())) {
            this.cancelCreator = uef2.getNickName();
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getValidDays() {
        return validDays;
    }

    public void setValidDays(Integer validDays) {
        this.validDays = validDays;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }

    public String getReviewCreator() {
        return reviewCreator;
    }

    public void setReviewCreator(String reviewCreator) {
        this.reviewCreator = reviewCreator;
    }

    public String getCancelCreator() {
        return cancelCreator;
    }

    public void setCancelCreator(String cancelCreator) {
        this.cancelCreator = cancelCreator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

}
