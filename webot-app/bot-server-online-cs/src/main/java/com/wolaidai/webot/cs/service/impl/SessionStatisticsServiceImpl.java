package com.wolaidai.webot.cs.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.service.SessionStatisticsService;
import com.wolaidai.webot.data.elastic.entity.ChatHistoryElasticEntity;
import com.wolaidai.webot.data.elastic.entity.SessionListElasticEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexChatHistoryElasticRepo;
import com.wolaidai.webot.data.elastic.repo.ComplexSessionListElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionTransferListEntity;
import com.wolaidai.webot.data.mysql.entity.report.SessionStatisticsEntity;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionStatisticsRepo;
import com.wolaidai.webot.data.mysql.repo.SessionTransferListRepo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class SessionStatisticsServiceImpl implements SessionStatisticsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SessionStatisticsService.class);

    @Autowired
    private SessionListRepo sessionListRepo;

    @Autowired
    private ComplexChatHistoryElasticRepo complexChatHistoryElasticRepo;

    @Autowired
    private ComplexSessionListElasticRepo complexSessionListElasticRepo;

    @Autowired
    private SessionStatisticsRepo sessionStatisticsRepo;

    @Autowired
    private SessionTransferListRepo sessionTransferListRepo;

    @Override
    @Async
    public void saveSessionStatistics(SessionListEntity entity) {
        if (entity == null) {
            return;
        }
        SessionStatisticsEntity statisticsEntity = convertEntity(entity);
        if (statisticsEntity != null) {
            statisticsEntity.setCreateTime(new Date());
            sessionStatisticsRepo.save(statisticsEntity);
        }
    }

    @Override
    public SessionStatisticsEntity getSessionStatistics(SessionListElasticEntity elasticEntity) {
        SessionListEntity entity = new SessionListEntity();
        BeanUtils.copyProperties(elasticEntity, entity);
        return convertEntity(entity);
    }

    @Override
    public void generateReportByDate(Integer orgId, Date startTime, Date endTime) {
        if (startTime.after(endTime)) {
            LOGGER.error("公共历史会话统计生成错误,开始时间不能大于结束时间");
            return;
        }
        List<SessionListEntity> sessionCountList = sessionListRepo.findByOrgIdAndStatusAndCreateTime(orgId, SessionListEntity.STATUS_OFFLINE, startTime, endTime);
        for (SessionListEntity sessionList : sessionCountList) {
            SessionStatisticsEntity statisticsEntity = sessionStatisticsRepo.findBySessionId(sessionList.getId());
            if (statisticsEntity != null) {
                continue;
            }
            saveSessionStatistics(sessionList);
        }
    }

    @Override
    public String getReportName() {
        return "公共历史会话统计";
    }

    //转换组装实体
    private SessionStatisticsEntity convertEntity(SessionListEntity entity) {
        Integer sessionId = entity.getId();
        String gcid = entity.getGcid();
        Integer orgId = entity.getOrgId();
        String clientId = entity.getClientId();
        Date createTime = entity.getCreateTime();
        Date offlineTime = entity.getOfflineTime();
        SessionStatisticsEntity statisticsEntity = new SessionStatisticsEntity();
        statisticsEntity.setOrgId(orgId);
        statisticsEntity.setSessionId(sessionId);
        try {
            //咨询机器人消息数
            statisticsEntity.setSeekBotCnt((int) complexChatHistoryElasticRepo.countByGcidAndSenderAndSceneAndTypeNot(
                    gcid, ChatHistoryElasticEntity.SENDER_TYPE_USER, ChatHistoryElasticEntity.SCENE_TYPE_BOT, ChatHistoryElasticEntity.TYPE_EVENT));
            //机器人回复数
            statisticsEntity.setBotAnswerCnt((int) complexChatHistoryElasticRepo.countByGcidAndSenderAndTypeNotAndRecallNot(gcid,
                    ChatHistoryElasticEntity.SENDER_TYPE_BOT, ChatHistoryElasticEntity.TYPE_EVENT));
            //咨询人工消息数
            statisticsEntity.setSeekManualCnt((int) complexChatHistoryElasticRepo.countByGcidAndSenderAndSceneAndTypeNot(
                    gcid, ChatHistoryElasticEntity.SENDER_TYPE_USER, ChatHistoryElasticEntity.SCENE_TYPE_CS, ChatHistoryElasticEntity.TYPE_EVENT));
            //人工回复数
            statisticsEntity.setManualAnswerCnt((int) complexChatHistoryElasticRepo.countByGcidAndSenderAndTypeNotAndRecallNot(gcid,
                    ChatHistoryElasticEntity.SENDER_TYPE_MANUAL, ChatHistoryElasticEntity.TYPE_EVENT));
            //撤回消息数
            statisticsEntity.setRecallCnt((int) complexChatHistoryElasticRepo.countByGcidAndRecall(gcid, true));
            //转接次数
            statisticsEntity.setTransferCnt((int) sessionTransferListRepo
                    .countByOrgIdAndSessionIdAndStatus(orgId, sessionId, SessionTransferListEntity.STATUS_OK));
            JSONObject customerDetail = entity.getCustomerDetail();
            String mobile = null;
            if (customerDetail != null) {
                JSONArray customers = customerDetail.getJSONArray("customers");
                if (customers != null && customers.size() > 0) {
                    JSONObject customer = customers.getJSONObject(0);
                    mobile = customer.getString("mobile");
                }
            }
            long beforeCome, currentDayCome;
            if (StringUtils.isBlank(mobile)) { //访客手机号为空
                beforeCome = complexSessionListElasticRepo.countSessionCnt(orgId, clientId, createTime);
                currentDayCome = complexSessionListElasticRepo.countSessionCnt(
                        orgId, clientId, offlineTime, DateUtils.addHours(offlineTime, 24));
            } else {
                beforeCome = complexSessionListElasticRepo.countSessionByMobileCnt(orgId, mobile, createTime);
                currentDayCome = complexSessionListElasticRepo.countSessionByMobileCnt(
                        orgId, mobile, offlineTime, DateUtils.addHours(offlineTime, 24));
            }
            //新老客户 0-新客户,1-老客户
            statisticsEntity.setNewOldCustomer(beforeCome > 0 ? 1 : 0);
            //24小时首解 0-否, 1-是
            statisticsEntity.setTwentyFourFirst(currentDayCome > 0 ? 0 : 1);
            return statisticsEntity;
        } catch (Exception e) {
            LOGGER.error("转换会话统计记录异常,{},{}", sessionId, e);
        }
        return null;
    }
}
