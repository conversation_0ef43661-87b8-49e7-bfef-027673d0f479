package com.wolaidai.webot.cs.service.impl;

import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.service.*;
import com.wolaidai.webot.data.mysql.entity.report.ReportHistoryEntity;
import com.wolaidai.webot.data.mysql.repo.ReportHistoryRepo;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class ReportHistoryServiceImpl implements ReportHistoryService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AppPropertyConfig appPropertyConfig;

    @Autowired
    private UserRepo userRepo;

    @Autowired
    private ReportHistoryRepo reportHistoryRepo;

    @Autowired
    private AttendanceReportService attendanceReportService;

    @Autowired
    private SatisfactionReportService satisfactionReportService;

    @Autowired
    private StateReportService stateReportService;

    @Autowired
    private SessionReportService sessionReportService;

    @Autowired
    private SessionDetailReportService sessionDetailReportService;

    @Autowired
    private ServiceDataReportService serviceDataReportService;

    @Autowired
    private SessionStatisticsService sessionStatisticsService;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void initReportRecord() {
        String redisTaskKey = RedisKey.LOCK_KEY + "initReportRecord";
        RLock lock = redissonClient.getLock(redisTaskKey);
        try {
            boolean res = lock.tryLock(0, 10, TimeUnit.SECONDS);
            if (!res) {
                return;
            }
            LOGGER.info("start initReportRecord...");
            long start = System.currentTimeMillis();
            Date dateNow = new Date();
            List<Integer> validOrgIds = userRepo.findValidOrgIds(appPropertyConfig.getProductId(), dateNow);
            validOrgIds.forEach(orgId->{
                ReportHistoryEntity reportHistory = new ReportHistoryEntity();
                reportHistory.setOrgId(orgId);
                reportHistory.setStatus(ReportHistoryEntity.NOT_START_STATUS);
                //today 00:00:00
                Date truncateNow = DateUtils.truncate(dateNow, Calendar.DATE);
                reportHistory.setStartTime(truncateNow);
                //today 23:59:59
                reportHistory.setEndTime(DateUtils.addSeconds(DateUtils.addDays(truncateNow,1),-1));
                reportHistory.setCreateTime(dateNow);
                reportHistory.setUpdateTime(reportHistory.getCreateTime());
                reportHistoryRepo.save(reportHistory);
            });
            LOGGER.info("end checkQueueList,cost {} ms",System.currentTimeMillis()-start);
        }catch (Exception e){
            LOGGER.error("execute initReportRecord schedule fail",e);
        }finally {
            if(lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Async
    public void generateReport() {
        String redisTaskKey = RedisKey.LOCK_KEY+"generateReport";
        RLock lock = redissonClient.getLock(redisTaskKey);
        try {
            boolean res = lock.tryLock(0, 30, TimeUnit.MINUTES);
            if (!res) {
                return;
            }
            List<String> errorMsgs = new ArrayList<>();
            LOGGER.info("start generateReport...");
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            List<ReportHistoryEntity> reportHistoryEntities = reportHistoryRepo.findAllByStatusIn(Arrays.asList(ReportHistoryEntity.NOT_START_STATUS, ReportHistoryEntity.FAIL_STATUS));
            for (ReportHistoryEntity history : reportHistoryEntities) {
                if (history.getEndTime().after(DateUtils.truncate(new Date(), Calendar.DATE))) {
                    LOGGER.warn("createTime error");
                    continue;
                }
                LOGGER.info("start generateReport, orgId:{}, startTime:{}, endTime:{}", history.getOrgId(), DateFormatUtils.format(history.getStartTime(), "yyyy-MM-dd HH:mm:ss"), DateFormatUtils.format(history.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
                StopWatch stopWatch1 = new StopWatch(UUID.randomUUID().toString());
                boolean isSuccess = true;
                //指定报表名称则跑全量
                if (StringUtils.isNotBlank(history.getName())) {
                    CommonReportService service = (CommonReportService) applicationContext.getBean(history.getName().concat("ServiceImpl"));
                    String reportName = service.getReportName();
                    stopWatch1.start(String.format("start generate %s all record",reportName));
                    try {
                        service.generateReportByDate(history.getOrgId(), history.getStartTime(), history.getEndTime());
                    } catch (Exception e) {
                        LOGGER.error("execute {} error",service.getClass().getName(),e);
                        errorMsgs.add(reportName+":"+e.getMessage());
                        isSuccess = false;
                    }
                    stopWatch1.stop();
                } else {
                    stopWatch1.start(attendanceReportService.getReportName());
                    try {
                        attendanceReportService.generateReportByDate(history.getOrgId(), history.getStartTime(), history.getEndTime());
                    } catch (Exception e) {
                        LOGGER.error("execute attendanceReportService error", e);
                        errorMsgs.add(attendanceReportService.getReportName() + ":" + e.getMessage());
                        isSuccess = false;
                    }
                    stopWatch1.stop();

                    stopWatch1.start(satisfactionReportService.getReportName());
                    try {
                        satisfactionReportService.generateReportByDate(history.getOrgId(), history.getStartTime(), history.getEndTime());
                    } catch (Exception e) {
                        LOGGER.error("execute satisfactionReportService error", e);
                        errorMsgs.add(satisfactionReportService.getReportName() + ":" + e.getMessage());
                        isSuccess = false;
                    }
                    stopWatch1.stop();

                    stopWatch1.start(stateReportService.getReportName());
                    try {
                        stateReportService.generateReportByDate(history.getOrgId(), history.getStartTime(), history.getEndTime());
                    } catch (Exception e) {
                        LOGGER.error("execute stateReportService error", e);
                        errorMsgs.add(stateReportService.getReportName() + ":" + e.getMessage());
                        isSuccess = false;
                    }
                    stopWatch1.stop();

                    stopWatch1.start(sessionReportService.getReportName());
                    try {
                        sessionReportService.generateReportByDate(history.getOrgId(), history.getStartTime(), history.getEndTime());
                    } catch (Exception e) {
                        LOGGER.error("execute sessionReportService error", e);
                        errorMsgs.add(sessionReportService.getReportName() + ":" + e.getMessage());
                        isSuccess = false;
                    }
                    stopWatch1.stop();

                    stopWatch1.start(sessionDetailReportService.getReportName());
                    try {
                        sessionDetailReportService.generateReportByDate(history.getOrgId(), history.getStartTime(), history.getEndTime());
                    } catch (Exception e) {
                        LOGGER.error("execute sessionDetailReportService error", e);
                        errorMsgs.add(sessionDetailReportService.getReportName() + ":" + e.getMessage());
                        isSuccess = false;
                    }
                    stopWatch1.stop();

                    stopWatch1.start(serviceDataReportService.getReportName());
                    try {
                        serviceDataReportService.generateReportByDate(history.getOrgId(), history.getStartTime(), history.getEndTime());
                    } catch (Exception e) {
                        LOGGER.error("execute serviceDataReportService error", e);
                        errorMsgs.add(serviceDataReportService.getReportName() + ":" + e.getMessage());
                        isSuccess = false;
                    }
                    stopWatch1.stop();

                    stopWatch1.start(sessionStatisticsService.getReportName());
                    try {
                        sessionStatisticsService.generateReportByDate(history.getOrgId(), history.getStartTime(), history.getEndTime());
                    } catch (Exception e) {
                        LOGGER.error("execute sessionStatisticsService error", e);
                        errorMsgs.add(sessionStatisticsService.getReportName() + ":" + e.getMessage());
                        isSuccess = false;
                    }
                    stopWatch1.stop();
                }
                LOGGER.info(stopWatch1.prettyPrint());
                if(isSuccess){
                    history.setStatus(ReportHistoryEntity.SUCCESS_STATUS);
                }else{
                    history.setStatus(ReportHistoryEntity.FAIL_STATUS);
                }
                if(!errorMsgs.isEmpty()){
                    history.setErrorMsg(String.join("|",errorMsgs));
                }
                history.setUpdateTime(new Date());
                reportHistoryRepo.save(history);
            }
            stopWatch.stop();
            LOGGER.info("end generateReport,cost {} ms", stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            LOGGER.error("execute generateReport fail",e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
