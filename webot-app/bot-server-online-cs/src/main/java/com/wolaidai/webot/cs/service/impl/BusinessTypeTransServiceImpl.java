package com.wolaidai.webot.cs.service.impl;

import com.wolaidai.webot.cs.exception.ValidationException;
import com.wolaidai.webot.cs.model.servicesummary.BusinessTypeRequestModel;
import com.wolaidai.webot.cs.service.BusinessTypeTransService;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;
import com.wolaidai.webot.data.mysql.repo.ServiceSummaryRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessTypeRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessUnitRepo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Service("businessTypeTransService")
public class BusinessTypeTransServiceImpl implements BusinessTypeTransService {

    @Autowired
    private BusinessUnitRepo businessUnitRepo;
    @Autowired
    private BusinessTypeRepo businessTypeRepo;
    @Autowired
    private ServiceSummaryRepo serviceSummaryRepo;

    @Override
    @Transactional
    public void saveBusinessType(Integer orgId, Integer unitId, BusinessTypeRequestModel model) throws ValidationException {
        BusinessUnitEntity entity = businessUnitRepo.findByIdAndOrgId(unitId, orgId);
        if (entity == null) {
            throw new ValidationException("业务单元不存在");
        }
        //先删除如失败,则不进行后续操作
        for (Integer typeId : model.getDeleteIds()) {
            deleteType(typeId, unitId, orgId);
        }
        if (Objects.nonNull(model.getStatus()) && !model.getStatus().equals(entity.getStatus())) {
            entity.setStatus(model.getStatus());
            entity.setUpdateTime(new Date());
            businessUnitRepo.save(entity);
        }
        for (int i = 0; i < model.getTypeInfoList().size(); i++) {
            BusinessTypeRequestModel.BusinessTypeInfo typeInfo = model.getTypeInfoList().get(i);
            save(i, typeInfo.getParentId(), orgId, entity, typeInfo, model.getUpdStatusIds());
        }
    }

    /**
     * 保存
     * @param index
     * @param parentId
     * @param orgId
     * @param unitEntity
     * @param typeInfo
     * @param updStatusIds
     * @return
     */
    public void save(int index, Integer parentId, Integer orgId, BusinessUnitEntity unitEntity,
                                           BusinessTypeRequestModel.BusinessTypeInfo typeInfo, Set<Integer> updStatusIds) {
        String title = typeInfo.getTitle();
        if (StringUtils.isBlank(title)) {
            throw new ValidationException("业务类型名称为空");
        }
        BusinessTypeEntity typeEntity;
        String infoId = typeInfo.getId();
        Integer unitEntityId = unitEntity.getId();
        //新增,和前端约定好add_
        if (infoId == null || infoId.startsWith("add_")) {
            int level = 1; //默认一级
            typeEntity = new BusinessTypeEntity();
            if (parentId != null) { //父类型ID
                BusinessTypeEntity sameTitle = businessTypeRepo.findByOrgIdAndParentTypeIdAndTitle(orgId, parentId, title);
                if (sameTitle != null) {
                    throw new ValidationException("同名业务类型已存在：".concat(title));
                }
                BusinessTypeEntity parent = businessTypeRepo.findById(parentId).orElse(null);
                if (parent != null) {
                    typeEntity.setParentType(parent);
                    level = parent.getLevel() + 1; //父层级+1
                }
            } else {
                BusinessTypeEntity sameTitle = businessTypeRepo.findByOrgIdAndUnitIdAndTitleAndParentTypeIsNull(orgId, unitEntityId, title);
                if (sameTitle != null) {
                    throw new ValidationException("同名业务类型已存在：".concat(title));
                }
            }
            if (level > 4) {
                throw new ValidationException("业务类型最大支持4级");
            }
            typeEntity.setLevel(level);
            typeEntity.setTitle(title);
            typeEntity.setOrgId(orgId);
            typeEntity.setUnit(unitEntity);
            typeEntity.setPosition(index);
            //新增默认有效
            typeEntity.setStatus(BusinessTypeEntity.ACTIVE_STATUS);
            typeEntity.setUpdateTime(new Date());
            typeEntity.setCreateTime(typeEntity.getUpdateTime());
            businessTypeRepo.save(typeEntity);
        } else { //修改
            Date dateNow = new Date();
            Integer typeId = Integer.valueOf(infoId);
            typeEntity = businessTypeRepo.findByIdAndOrgIdAndUnitId(typeId, orgId, unitEntityId);
            //不存在
            if (typeEntity == null) {
                throw new ValidationException("业务类型不存在");
            } else {
                BusinessTypeEntity sameTitle;
                if (typeEntity.getParentType() != null) {
                    sameTitle = businessTypeRepo.findByOrgIdAndParentTypeIdAndTitle(orgId, typeEntity.getParentType().getId(), title);
                } else {
                    sameTitle = businessTypeRepo.findByOrgIdAndUnitIdAndTitleAndParentTypeIsNull(orgId, unitEntityId, title);
                }
                if (sameTitle != null && !sameTitle.getId().equals(typeId)) {
                    throw new ValidationException("同名业务类型已存在：".concat(title));
                }
                Integer status = typeInfo.getStatus();
                //状态发生变更
                if (updStatusIds.contains(typeId) && !typeEntity.getStatus().equals(status)) {
                    //更新子节点
                    cascadeChild(status, dateNow, typeEntity);
                    //如果是启用,需要级联激活父节点
                    if (BusinessTypeEntity.ACTIVE_STATUS.equals(status)) {
                        cascadeActive(dateNow, typeEntity);
                    }
                }
                typeEntity.setTitle(title);
                typeEntity.setPosition(index);
                typeEntity.setUpdateTime(dateNow);
                businessTypeRepo.save(typeEntity);
            }
        }
        for (int i = 0; i < typeInfo.getChildren().size(); i++) {
            save(i, typeEntity.getId(), orgId, unitEntity, typeInfo.getChildren().get(i), updStatusIds);
        }
    }

    /**
     * 删除业务类型
     * @param typeId
     * @param unitId
     * @param orgId
     * @return
     */
    public void deleteType(Integer typeId, Integer unitId, Integer orgId) {
        BusinessTypeEntity businessTypeEntity = businessTypeRepo.findByIdAndOrgIdAndUnitId(typeId, orgId, unitId);
        if (businessTypeEntity != null) {
            Set<Integer> typeIds = new HashSet<>();
            getAllChildTypeId(businessTypeEntity, typeIds);
            long ref = serviceSummaryRepo.countByOrgIdAndUnitIdAndTypeIdIn(orgId, unitId, typeIds);
            if (ref > 0) {
                throw  new ValidationException("业务类型有关联数据，无法删除");
            }
            businessTypeRepo.deleteAllById(typeIds);
        }
    }

    /**
     * 获取包括自己及所有子类型ID
     * @param entity
     * @param typeIds
     */
    private void getAllChildTypeId(BusinessTypeEntity entity, Set<Integer> typeIds) {
        typeIds.add(entity.getId());
        if (CollectionUtils.isNotEmpty(entity.getChildTypes())) {
            for (BusinessTypeEntity childType : entity.getChildTypes()) {
                getAllChildTypeId(childType, typeIds);
            }
        }
    }

    /**
     * 级联更新子节点
     * @param status
     * @param dateNow
     * @param businessType
     */
    private void cascadeChild(Integer status, Date dateNow, BusinessTypeEntity businessType) {
        if (CollectionUtils.isNotEmpty(businessType.getChildTypes())) {
            for (BusinessTypeEntity child : businessType.getChildTypes()) {
                cascadeChild(status, dateNow, child);
            }
        }
        businessType.setStatus(status);
        businessType.setUpdateTime(dateNow);
    }
    /**
     * 级联激活父节点
     * @param dateNow
     * @param businessType
     */
    private void cascadeActive(Date dateNow, BusinessTypeEntity businessType) {
        if (businessType.getParentType() != null
                && BusinessTypeEntity.INACTIVE_STATUS.equals(businessType.getParentType().getStatus())) {
            cascadeActive(dateNow, businessType.getParentType());
        }
        businessType.setStatus(BusinessTypeEntity.ACTIVE_STATUS);
        businessType.setUpdateTime(dateNow);
    }

}
