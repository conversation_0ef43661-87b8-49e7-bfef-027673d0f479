package com.wolaidai.webot.cs.model.quickreply;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "快捷回复文本")
public class QuickReplyTextUpdModel extends BaseModel {

    @NotBlank(message = "{NotBlack.quickReply.content}")
    @ApiModelProperty(value = "内容")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

}
