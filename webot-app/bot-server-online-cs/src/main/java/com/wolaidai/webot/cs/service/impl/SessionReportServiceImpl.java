package com.wolaidai.webot.cs.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.model.report.SessionReportReqModel;
import com.wolaidai.webot.cs.service.SessionReportService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.entity.common.SkillGroupEntity;
import com.wolaidai.webot.data.mysql.entity.report.SessionReportEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.repo.QueueListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionReportRepo;
import com.wolaidai.webot.data.mysql.repo.SkillGroupRepo;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsAnalyzeEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsCountEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeAnalyzeEntity;
import com.wolaidai.webot.data.mysql.resultEntity.StatisticsTimeCountEntity;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Service
public class SessionReportServiceImpl implements SessionReportService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SessionReportServiceImpl.class);

    @Autowired
    private QueueListRepo queueListRepo;

    @Autowired
    private SessionListRepo sessionListRepo;

    @Autowired
    private SessionReportRepo sessionReportRepo;

    @Autowired
    private SkillGroupRepo skillGroupRepo;
    @Autowired
    protected AppPropertyConfig appPropertyConfig;

    public static Long WAIT_SECONDS = 5L;


    @Override
    public void generateReportByDate(Integer orgId, Date startTime, Date endTime) {
        if (startTime.after(endTime)) {
            LOGGER.error("generate session report fail, startTime can't greater than endTime");
            return;
        }

        Date start = startTime;
        Date end = DateUtils.addSeconds(DateUtils.addDays(start, 1), -1);
        List<SessionReportEntity> reportList = new ArrayList<>();
        while (start.before(endTime)) {
            List<SessionReportEntity> businessList = getSessionListInfoByBusiness(orgId, start, end);
            List<SessionReportEntity> timeList = getSessionListInfoByTime(orgId, start, end);
            if (businessList != null && businessList.size() > 0) {
                reportList.addAll(businessList);
            }
            if (timeList != null && timeList.size() > 0) {
                reportList.addAll(timeList);
            }
            start = DateUtils.addDays(start, 1);
            end = DateUtils.addSeconds(DateUtils.addDays(start, 1), -1);
        }
        sessionReportRepo.deleteAllByOrgIdAndDateGreaterThanEqualAndDateLessThanEqual(orgId, startTime, endTime);
        if (reportList != null && reportList.size() > 0) {
            sessionReportRepo.saveAll(reportList);
        }
        LOGGER.info("generate session report success, orgId:{}, startTime:{}, endTime:{}", orgId, startTime, endTime);
    }


    @Override
    public String getReportName() {
        return "会话统计";
    }


    public List<Map<String, Object>> getSessionReport(Integer orgId, Integer type, Date startTime, Date endTime) {
        if(startTime.after(new Date())) {
            return null;
        }
        Date todayStart = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        Date reportEndTime = endTime;
        Date realTimeStart = startTime;
        boolean realTime = false;
        if (!endTime.before(todayStart)) {
            reportEndTime = DateUtils.addSeconds(todayStart, -1);
            realTime = true;
            realTimeStart = startTime.after(todayStart)? startTime: todayStart;
        }

        //1-按日统计，2-按周期统计，3-按半小时段统计
        if (type == 1) {
            List<SessionReportEntity> reportList = sessionReportRepo.findByOrgIdAndBusinessIdNotAndDateGreaterThanEqualAndDateLessThanEqual(orgId, -1, startTime, reportEndTime);

            //当天实时数据
            if (realTime) {
                List<SessionReportEntity> todayInfoList = getSessionListInfoByBusiness(orgId, realTimeStart, endTime);
                if (todayInfoList != null && todayInfoList.size() > 0) {
                    reportList.addAll(todayInfoList);
                }
            }
            if (reportList == null || reportList.size() == 0) {
                return null;
            }
            Map<Date, List<SessionReportEntity>> reportResultMap = reportList.stream().collect(groupingBy(SessionReportEntity::getDate));
            List<Map<String, Object>> infoList = new ArrayList();
            reportResultMap.forEach((k, v) -> {
                String dateInfo = DateFormatUtils.format(k, "yyyy-MM-dd");
                SessionReportEntity total = getSessionReportSumInfo(v);
                List<Map<String, Object>> detailList = new ArrayList<>();
                v.stream().forEach(t -> {
                    Map<String, Object> infoMap = convertSessionInfo(t);
                    infoMap.put("date", dateInfo);
                    detailList.add(infoMap);
                });
                List<Map<String, Object>> detailSortList = detailList.stream().sorted(Comparator.comparing(m -> (Long) m.get("manualCount"), Comparator.reverseOrder())).collect(Collectors.toList());
                Map<String, Object> result = new HashMap<>();
                result.put("date", dateInfo);
                result.put("detail", detailSortList);
                result.put("total", convertSessionInfo(total));
                infoList.add(result);
            });
            List<Map<String, Object>> infoSortList = infoList.stream().sorted(Comparator.comparing(m -> (String) m.get("date"))).collect(Collectors.toList());
            return infoSortList;
        } else if (type == 2) {
            List<SessionReportEntity> reportList = sessionReportRepo.findByOrgIdAndBusinessIdNotAndDateGreaterThanEqualAndDateLessThanEqual(orgId, -1, startTime, reportEndTime);
            //当天实时数据
            if (realTime) {
                List<SessionReportEntity> todayInfoList = getSessionListInfoByBusiness(orgId, realTimeStart, endTime);
                if (todayInfoList != null && todayInfoList.size() > 0) {
                    reportList.addAll(todayInfoList);
                }
            }
            if (reportList == null || reportList.size() == 0) {
                return null;
            }

            Map<Integer, List<SessionReportEntity>> reportResultMap = reportList.stream().collect(groupingBy(SessionReportEntity::getBusinessId));
            List<SessionReportEntity> infoList = new ArrayList<>();
            reportResultMap.forEach((k, v) -> {
                SessionReportEntity info = getSessionReportSumInfo(v);
                info.setBusinessId(k);
                info.setBusinessName(v.get(0).getBusinessName());
                infoList.add(info);
            });

            SessionReportEntity totalInfo = getSessionReportSumInfo(infoList);
            Map<String, Object> result = new HashMap<>();
            String dateInfo = DateFormatUtils.format(startTime, "yyyy-MM-dd") + " ~ " + DateFormatUtils.format(endTime, "yyyy-MM-dd");
            List<Map<String, Object>> detailList = new ArrayList<>();
            infoList.stream().forEach(t -> {
                Map<String, Object> infoMap = convertSessionInfo(t);
                infoMap.put("date", dateInfo);
                detailList.add(infoMap);
            });
            List<Map<String, Object>> detailSortList = detailList.stream().sorted(Comparator.comparing(m -> (Long) m.get("manualCount"), Comparator.reverseOrder())).collect(Collectors.toList());
            result.put("date", dateInfo);
            result.put("detail", detailSortList);
            result.put("total", convertSessionInfo(totalInfo));
            List resultList = new ArrayList();
            resultList.add(result);
            return resultList;
        } else if (type == 3) {
            List<SessionReportEntity> reportList = sessionReportRepo.findByOrgIdAndBusinessIdAndDateGreaterThanEqualAndDateLessThanEqual(orgId, -1, startTime, reportEndTime);
            if (realTime) {
                List<SessionReportEntity> todayInfoList = getSessionListInfoByTime(orgId, realTimeStart, endTime);
                if (todayInfoList != null && todayInfoList.size() > 0) {
                    reportList.addAll(todayInfoList);
                }
            }
            if (reportList == null || reportList.size() == 0) {
                return null;
            }
            Map<String, List<SessionReportEntity>> reportResultMap = reportList.stream().collect(groupingBy(sessionReportEntity -> DateFormatUtils.format(sessionReportEntity.getDate(), "yyyy-MM-dd")));
            List<Map<String, Object>> infoList = new ArrayList();
            reportResultMap.forEach((k, v) -> {
                SessionReportEntity total = getSessionReportSumInfo(v);
                List<Map<String, Object>> detailList = new ArrayList<>();
                v.stream().forEach(t -> {
                    Map<String, Object> infoMap = convertSessionInfo(t);
                    infoMap.put("date", k);
                    String timeInfo = DateFormatUtils.format(t.getDate(), "HH:mm") + "-" + DateFormatUtils.format(DateUtils.addMinutes(t.getDate(), 30), "HH:mm");
                    infoMap.put("time", timeInfo);
                    detailList.add(infoMap);
                });
                List<Map<String, Object>> detailSortList = detailList.stream().sorted(Comparator.comparing(m -> (String) m.get("time"))).collect(Collectors.toList());
                Map<String, Object> result = new HashMap<>();
                result.put("date", k);
                result.put("detail", detailSortList);
                result.put("total", convertSessionInfo(total));
                infoList.add(result);
            });
            List<Map<String, Object>> infoSortList = infoList.stream().sorted(Comparator.comparing(m -> (String) m.get("date"))).collect(Collectors.toList());
            return infoSortList;
        }
        return null;
    }


    public SessionReportEntity getSessionReportSumInfo(List<SessionReportEntity> infoList) {
        if (infoList == null || infoList.size() == 0) {
            return null;
        }
        Long manualCountSum = infoList.stream().mapToLong(v -> v.getManualCount()).sum();
        Long answerCountSum = infoList.stream().mapToLong(v -> v.getAnswerCount()).sum();
        Long quitCountSum = infoList.stream().mapToLong(v -> v.getQuitCount()).sum();
        Long waitCountSum = infoList.stream().mapToLong(v -> v.getWaitCount()).sum();
        Long sessionSecondSum = infoList.stream().mapToLong(v -> v.getSessionSecond()).sum();
        Long sessionCountSum = infoList.stream().mapToLong(v -> v.getSessionCount()).sum();
        Long waitSecondSum = infoList.stream().mapToLong(v -> v.getWaitSecond()).sum();

        Long avgSessionSecondSum = sessionCountSum > 0 ? sessionSecondSum / sessionCountSum : 0;
        Long avgWaitSecondSum = waitCountSum > 0 ? waitSecondSum / waitCountSum : 0;
        BigDecimal connectRateSum = manualCountSum > 0 ? BigDecimal.valueOf(answerCountSum * 100).divide(BigDecimal.valueOf(manualCountSum), 2, RoundingMode.HALF_UP) : new BigDecimal("0.00");
        SessionReportEntity totalInfo = new SessionReportEntity();
        totalInfo.setManualCount(manualCountSum);
        totalInfo.setAnswerCount(answerCountSum);
        totalInfo.setQuitCount(quitCountSum);
        totalInfo.setWaitCount(waitCountSum);
        totalInfo.setSessionSecond(sessionSecondSum);
        totalInfo.setSessionCount(sessionCountSum);
        totalInfo.setWaitSecond(waitSecondSum);
        totalInfo.setConnectRate(connectRateSum);
        totalInfo.setAvgSessionSecond(avgSessionSecondSum);
        totalInfo.setAvgWaitSecond(avgWaitSecondSum);
        return totalInfo;
    }


    public List<SessionReportEntity> getSessionListInfoByBusiness(Integer orgId, Date startTime, Date endTime) {
        List<StatisticsCountEntity> queueCountList = queueListRepo.getBusinessCountByTime(orgId, startTime, endTime);
        if (null == queueCountList || queueCountList.size()==0) {
            return null;
        }
        Date currentTime = new Date();
        List<SkillGroupEntity> skillList = skillGroupRepo.findByOrgId(orgId);
        Map<Integer, String> businessMap = skillList.stream().collect(Collectors.toMap(SkillGroupEntity::getId, SkillGroupEntity::getName));
        Map<Integer, Long> queueCountMap = queueCountList.stream().collect(Collectors.toMap(StatisticsCountEntity::getId, StatisticsCountEntity::getTotal));
        List<StatisticsCountEntity> queueQuitCountList = queueListRepo.getBusinessCountByTimeAndStatus(orgId, startTime, endTime, QueueListEntity.DEQUEUE_STATUS);
        Map<Integer, Long> queueQuitCountMap = queueQuitCountList.stream().collect(Collectors.toMap(StatisticsCountEntity::getId, StatisticsCountEntity::getTotal));
        List<StatisticsCountEntity> queueAnswerCountList = queueListRepo.getBusinessCountByTimeAndStatus(orgId, startTime, endTime, QueueListEntity.QUEUED_STATUS);
        Map<Integer, Long> queueAnswerCountMap = queueAnswerCountList.stream().collect(Collectors.toMap(StatisticsCountEntity::getId, StatisticsCountEntity::getTotal));
        List<StatisticsAnalyzeEntity> queueWaitList = queueListRepo.getBusinessCountByTimeAndWaitSecond(orgId, startTime, endTime, WAIT_SECONDS);
        Map<Integer, Long> queueWaitCountMap = queueWaitList.stream().collect(Collectors.toMap(StatisticsAnalyzeEntity::getId, StatisticsAnalyzeEntity::getTotal));
        Map<Integer, Long> queueWaitSecondMap = queueWaitList.stream().collect(Collectors.toMap(StatisticsAnalyzeEntity::getId, StatisticsAnalyzeEntity::getAnalyze));
        List<StatisticsAnalyzeEntity> sessionList = sessionListRepo.getBusinessStatisticsByTime(orgId, startTime, endTime);
        Map<Integer, Long> sessionCountMap = sessionList.stream().collect(Collectors.toMap(StatisticsAnalyzeEntity::getId, StatisticsAnalyzeEntity::getTotal));
        Map<Integer, Long> sessionDurationMap = sessionList.stream().collect(Collectors.toMap(StatisticsAnalyzeEntity::getId, StatisticsAnalyzeEntity::getAnalyze));

        Set<Integer> bussinessIdSet = queueCountMap.keySet();
        List<SessionReportEntity> sessionReportEntityList = new ArrayList<>();
        bussinessIdSet.forEach(k -> {
            Long manualCount = queueCountMap.getOrDefault(k, 0L);
            Long answerCount = queueAnswerCountMap.getOrDefault(k, 0L);
            Long quitCount = queueQuitCountMap.getOrDefault(k, 0L);
            Long waitCount = queueWaitCountMap.getOrDefault(k, 0L);
            Long waitSecond = queueWaitSecondMap.getOrDefault(k, 0L);
            Long avgWaitSecond = waitCount > 0 ? waitSecond / waitCount : 0;
            Long sessionSecond = sessionDurationMap.getOrDefault(k, 0L);
            Long sessionCount = sessionCountMap.getOrDefault(k, 0L);
            Long avgSessionSecond = sessionCount > 0 ? sessionSecond / sessionCount : 0;
            BigDecimal connectRate = manualCount > 0 ? BigDecimal.valueOf(answerCount * 100).divide(BigDecimal.valueOf(manualCount), 2, RoundingMode.HALF_UP) : new BigDecimal("0.00");

            SessionReportEntity sessionReportEntity = new SessionReportEntity();
            sessionReportEntity.setDate(startTime);
            sessionReportEntity.setBusinessId(k);
            sessionReportEntity.setBusinessName(businessMap.get(k));
            sessionReportEntity.setManualCount(manualCount);
            sessionReportEntity.setAnswerCount(answerCount);
            sessionReportEntity.setQuitCount(quitCount);
            sessionReportEntity.setWaitCount(waitCount);
            sessionReportEntity.setConnectRate(connectRate);
            sessionReportEntity.setSessionSecond(sessionSecond);
            sessionReportEntity.setSessionCount(sessionCount);
            sessionReportEntity.setAvgSessionSecond(avgSessionSecond);
            sessionReportEntity.setWaitSecond(waitSecond);
            sessionReportEntity.setAvgWaitSecond(avgWaitSecond);
            sessionReportEntity.setCreatTime(currentTime);
            sessionReportEntity.setOrgId(orgId);
            sessionReportEntityList.add(sessionReportEntity);
        });
        return sessionReportEntityList;
    }


    public List<SessionReportEntity> getSessionListInfoByTime(Integer orgId, Date startTime, Date endTime) {
        List<StatisticsTimeCountEntity> queueCountList = queueListRepo.getTimeCountByTime(orgId, startTime, endTime);
        if ((null == queueCountList || queueCountList.size()==0)) {
            return null;
        }
        Date currentTime = new Date();
        Map<Date, Long> queueCountMap = queueCountList.stream().collect(Collectors.toMap(StatisticsTimeCountEntity::getTime, StatisticsTimeCountEntity::getTotal));
        List<StatisticsTimeCountEntity> queueQuitCountList = queueListRepo.getTimeCountByTimeAndStatus(orgId, startTime, endTime, QueueListEntity.DEQUEUE_STATUS);
        Map<Date, Long> queueQuitCountMap = queueQuitCountList.stream().collect(Collectors.toMap(StatisticsTimeCountEntity::getTime, StatisticsTimeCountEntity::getTotal));
        List<StatisticsTimeCountEntity> queueAnswerCountList = queueListRepo.getTimeCountByTimeAndStatus(orgId, startTime, endTime, QueueListEntity.QUEUED_STATUS);
        Map<Date, Long> queueAnswerCountMap = queueAnswerCountList.stream().collect(Collectors.toMap(StatisticsTimeCountEntity::getTime, StatisticsTimeCountEntity::getTotal));
        List<StatisticsTimeAnalyzeEntity> queueWaitList = queueListRepo.getTimeCountByTimeAndWaitSecond(orgId, startTime, endTime, WAIT_SECONDS);
        Map<Date, Number> queueWaitCountMap = queueWaitList.stream().collect(Collectors.toMap(StatisticsTimeAnalyzeEntity::getTime, StatisticsTimeAnalyzeEntity::getTotal));
        Map<Date, Number> queueWaitSecondMap = queueWaitList.stream().collect(Collectors.toMap(StatisticsTimeAnalyzeEntity::getTime, StatisticsTimeAnalyzeEntity::getAnalyze));
        List<StatisticsTimeAnalyzeEntity> sessionList = sessionListRepo.getTimeStatisticsByTime(orgId, startTime, endTime);
        Map<Date, Number> sessionCountMap = sessionList.stream().collect(Collectors.toMap(StatisticsTimeAnalyzeEntity::getTime, StatisticsTimeAnalyzeEntity::getTotal));
        Map<Date, Number> sessionDurationMap = sessionList.stream().collect(Collectors.toMap(StatisticsTimeAnalyzeEntity::getTime, StatisticsTimeAnalyzeEntity::getAnalyze));

        Set<Date> timeSet = queueCountMap.keySet();
        List<SessionReportEntity> sessionReportEntityList = new ArrayList<>();
        timeSet.forEach(k -> {
            Long manualCount = queueCountMap.getOrDefault(k, 0L);
            Long answerCount = queueAnswerCountMap.getOrDefault(k, 0L);
            Long quitCount = queueQuitCountMap.getOrDefault(k, 0L);
            Long waitCount = queueWaitCountMap.getOrDefault(k, 0L).longValue();
            Long waitSecond = queueWaitSecondMap.getOrDefault(k, 0L).longValue();
            Long avgWaitSecond = waitCount > 0 ? waitSecond / waitCount : 0;
            Long sessionSecond = sessionDurationMap.getOrDefault(k, 0L).longValue();
            Long sessionCount = sessionCountMap.getOrDefault(k, 0L).longValue();
            Long avgSessionSecond = sessionCount > 0 ? sessionSecond / sessionCount : 0;
            BigDecimal connectRate = manualCount > 0 ? BigDecimal.valueOf(answerCount * 100).divide(BigDecimal.valueOf(manualCount), 2, RoundingMode.HALF_UP) : new BigDecimal("0.00");

            SessionReportEntity sessionReportEntity = new SessionReportEntity();
            sessionReportEntity.setDate(k);
            //businessId作为联合主键不能为空，设置半小时段的businessId为-1
            sessionReportEntity.setBusinessId(-1);
            sessionReportEntity.setManualCount(manualCount);
            sessionReportEntity.setAnswerCount(answerCount);
            sessionReportEntity.setQuitCount(quitCount);
            sessionReportEntity.setWaitCount(waitCount);
            sessionReportEntity.setConnectRate(connectRate);
            sessionReportEntity.setSessionSecond(sessionSecond);
            sessionReportEntity.setSessionCount(sessionCount);
            sessionReportEntity.setAvgSessionSecond(avgSessionSecond);
            sessionReportEntity.setWaitSecond(waitSecond);
            sessionReportEntity.setAvgWaitSecond(avgWaitSecond);
            sessionReportEntity.setCreatTime(currentTime);
            sessionReportEntity.setOrgId(orgId);
            sessionReportEntityList.add(sessionReportEntity);
        });
        return sessionReportEntityList;
    }


    private Map<String, Object> convertSessionInfo(SessionReportEntity t) {
        JSONObject info = (JSONObject) JSON.toJSON(t);
        info.put("connectRate", t.getConnectRate() + "%");
        info.put("sessionTime", DurationFormatUtils.formatDuration(t.getSessionSecond() * 1000, "HH:mm:ss"));
        info.put("avgSessionTime", DurationFormatUtils.formatDuration(t.getAvgSessionSecond() * 1000, "HH:mm:ss"));
        info.put("waitTime", DurationFormatUtils.formatDuration(t.getWaitSecond() * 1000, "HH:mm:ss"));
        info.put("avgWaitTime", DurationFormatUtils.formatDuration(t.getAvgWaitSecond() * 1000, "HH:mm:ss"));
        return info;
    }

    public boolean run(TaskEntity task) throws Exception {
        String[] commonTitleArray = new String[]{"日期", "产品", "进入人工请求数", "人工应答数", "放弃数", "等待数", "接通率", "会话总时长", "会话均长", "等待总时长", "等待均长"};
        String[] commonTitleFieldArray = new String[]{"date", "businessName", "manualCount", "answerCount", "quitCount", "waitCount", "connectRate", "sessionTime", "avgSessionTime", "waitTime", "avgWaitTime"};
        String[] timeTitleArray = new String[]{"日期", "时段", "进入人工请求数", "人工应答数", "放弃数", "等待数", "接通率", "会话总时长", "会话均长", "等待总时长", "等待均长"};
        String[] timeTitleFieldArray = new String[]{"date", "time", "manualCount", "answerCount", "quitCount", "waitCount", "connectRate", "sessionTime", "avgSessionTime", "waitTime", "avgWaitTime"};
        SessionReportReqModel model = JSON.toJavaObject(task.getExtraParams(), SessionReportReqModel.class);
        Integer type = model.getType();
        Date startTime = model.getStartTime(), endTime = model.getEndTime(); 
        String[] titleArray = type == 3 ? timeTitleArray : commonTitleArray;
        String[] titleFieldArray = type == 3 ? timeTitleFieldArray : commonTitleFieldArray;
        String typeName = "";
        if (type == 1) {
            typeName = "按日";
        } else if (type == 2) {
            typeName = "按周期";
        } else if (type == 3) {
            typeName = "按半小时段";
        }
        String fileName = "会话统计_" + typeName + "_" + DateFormatUtils.format(startTime, "yyyy-MM-dd") + "_" + DateFormatUtils.format(endTime, "yyyy-MM-dd") + ".xlsx";
        task.setName(fileName);
        CommonUtil.saveTaskProgress(task, 0);
        Integer orgId = task.getOrgId();
        
        List<Map<String, Object>> reportData = getSessionReport(orgId, type, startTime, endTime);
        if (reportData == null || reportData.size() == 0) {
            CommonUtil.saveTaskProgress(task, -1);
            return false;
        }
        CommonUtil.saveTaskProgress(task, 75);
        XSSFWorkbook workbook = new XSSFWorkbook();
        reportData.forEach(v -> {
            List<Map<String, Object>> detailList = (List<Map<String, Object>>) v.get("detail");
            Map<String, Object> total = (Map<String, Object>) v.get("total");
            String date = (String) v.get("date");
            Sheet sheet = workbook.createSheet(date);
            sheet.setDefaultColumnWidth(14);
            Row header = sheet.createRow(0);

            for (int i = 0; i < titleArray.length; i++) {
                Cell headerCell = header.createCell(i);
                headerCell.setCellValue(titleArray[i]);
            }

            for (int j = 0; j < detailList.size(); j++) {
                Map<String, Object> detailMap = detailList.get(j);
                Row row = sheet.createRow(j + 1);
                for (int t = 0; t < titleFieldArray.length; t++) {
                    Cell rowCell = row.createCell(t);
                    Object value = detailMap.get(titleFieldArray[t]);
                    if (value == null) {
                        rowCell.setCellValue("");
                    } else if (value instanceof Long) {
                        rowCell.setCellValue((Long) value);
                    } else {
                        rowCell.setCellValue(String.valueOf(value));
                    }
                }
            }

            int totalRowIndex = detailList.size() + 1;
            Row totalRow = sheet.createRow(totalRowIndex);
            Cell totalCell = totalRow.createCell(0);
            totalCell.setCellValue("总计");
            totalRow.createCell(1).setCellValue("");
            sheet.addMergedRegion(new CellRangeAddress(totalRowIndex, totalRowIndex, 0, 1));
            for (int s = 2; s < titleFieldArray.length; s++) {
                Cell rowCell = totalRow.createCell(s);
                Object value = total.get(titleFieldArray[s]);
                if (value == null) {
                    rowCell.setCellValue("");
                } else if (value instanceof Long) {
                    rowCell.setCellValue((Long) value);
                } else {
                    rowCell.setCellValue(String.valueOf(value));
                }
            }
        });
        LOGGER.info("export session report success, orgId:{}, type:{}, startTime:{}, endTime:{}", orgId, type, startTime, endTime);
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(workbook, appPropertyConfig.getOssBucketName(), task);
        return true;
    }


}
