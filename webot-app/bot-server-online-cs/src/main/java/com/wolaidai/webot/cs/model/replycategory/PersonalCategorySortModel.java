package com.wolaidai.webot.cs.model.replycategory;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.LinkedHashMap;
import java.util.Map;

@ApiModel(description = "快捷回复排序")
public class PersonalCategorySortModel extends BaseModel {

    @ApiModelProperty(value = "拖拽节点类型：0-分类,1-内容")
    private Integer nodeType;
    @NotNull(message = "{NotNull.category.type}")
    @ApiModelProperty(value = "分类类型：0-文本,1-附件")
    private Integer type;
    private Integer parentCategoryId;

    @ApiModelProperty(value = "排序: 其中key = id-type, value = position")
    private Map<String, Integer> sortMap = new LinkedHashMap<>();

    public Integer getNodeType() {
        return nodeType;
    }

    public void setNodeType(Integer nodeType) {
        this.nodeType = nodeType;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getParentCategoryId() {
        return parentCategoryId;
    }

    public void setParentCategoryId(Integer parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
    }

    public Map<String, Integer> getSortMap() {
        return sortMap;
    }

    public void setSortMap(Map<String, Integer> sortMap) {
        this.sortMap = sortMap;
    }
}
