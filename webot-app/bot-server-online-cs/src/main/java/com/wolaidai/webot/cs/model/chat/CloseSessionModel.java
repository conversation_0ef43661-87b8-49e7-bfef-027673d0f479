package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;

@ApiModel(description = "关闭会话详情")
public class CloseSessionModel extends BaseModel {
    private Boolean existsServiceSummary;

    public CloseSessionModel(Boolean existsServiceSummary) {
        this.existsServiceSummary = existsServiceSummary;
    }

    public Boolean getExistsServiceSummary() {
        return existsServiceSummary;
    }

    public void setExistsServiceSummary(Boolean existsServiceSummary) {
        this.existsServiceSummary = existsServiceSummary;
    }

}
