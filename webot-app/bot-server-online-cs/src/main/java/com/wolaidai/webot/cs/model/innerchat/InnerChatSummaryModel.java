package com.wolaidai.webot.cs.model.innerchat;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.chat.*;

import java.util.*;

public class InnerChatSummaryModel extends BaseModel {

    private List<ChatSessionRow> rowList = new ArrayList<>();

    public List<ChatSessionRow> getRowList() {
        return rowList;
    }

    public void setRowList(List<ChatSessionRow> rowList) {
        this.rowList = rowList;
    }

    public static class ChatSessionRow {
        private Integer sessionKey;
        private Integer roomId;
        private String roomName;
        private Integer unreadMsgCount;
        private String lastMsg;
        private String lastMsgSender;
        private Date lastMsgTime;
        private boolean inRoom = true;
        @JsonIgnore
        private Integer status = ChatMembersEntity.STATUS_ACTIVE; //默认激活
        private List<InnerServiceUserModel> members = new ArrayList<>();

        public Integer getSessionKey() {
            return sessionKey;
        }

        public void setSessionKey(Integer sessionKey) {
            this.sessionKey = sessionKey;
        }

        public Integer getRoomId() {
            return roomId;
        }

        public void setRoomId(Integer roomId) {
            this.roomId = roomId;
        }

        public String getRoomName() {
            return roomName;
        }

        public void setRoomName(String roomName) {
            this.roomName = roomName;
        }

        public Integer getUnreadMsgCount() {
            return unreadMsgCount;
        }

        public void setUnreadMsgCount(Integer unreadMsgCount) {
            this.unreadMsgCount = unreadMsgCount;
        }

        public String getLastMsg() {
            return lastMsg;
        }

        public void setLastMsg(String lastMsg) {
            this.lastMsg = lastMsg;
        }

        public String getLastMsgSender() {
            return lastMsgSender;
        }

        public void setLastMsgSender(String lastMsgSender) {
            this.lastMsgSender = lastMsgSender;
        }

        public Date getLastMsgTime() {
            return lastMsgTime;
        }

        public void setLastMsgTime(Date lastMsgTime) {
            this.lastMsgTime = lastMsgTime;
        }

        public boolean isInRoom() {
            return inRoom;
        }

        public void setInRoom(boolean inRoom) {
            this.inRoom = inRoom;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public List<InnerServiceUserModel> getMembers() {
            return members;
        }

        public void setMembers(List<InnerServiceUserModel> members) {
            this.members = members;
        }


        public ChatSessionRow() {

        }

        public ChatSessionRow(String self, ChatSessionEntity entity, Comparator comparator) {
            this.sessionKey = entity.getId();
            ChatRoomEntity room = entity.getRoom();
            if (room != null) {
                this.roomId = room.getId();
                this.roomName = room.getName();
                InnerMemberInfoModel owner = new InnerMemberInfoModel();
                Set<ChatRoomUsersEntity> roomUsers = room.getRoomUsers();
                for (ChatRoomUsersEntity chatRoomUsers : roomUsers) {
                    UserStateEntity user = chatRoomUsers.getUser();
                    InnerMemberInfoModel innerMemberInfoModel = new InnerMemberInfoModel(user);
                    if (room.getCreator().equals(user.getEmail())) {
                        innerMemberInfoModel.setOwner(true);
                        owner = innerMemberInfoModel;
                        continue;
                    }
                    this.members.add(innerMemberInfoModel);
                }
                //按照首字母排序,群主在第一位
                Collections.sort(members, (m1, m2) -> comparator.compare(m1.getNickName(), m2.getNickName()));
                members.add(0, owner);
                //是否在群里
                if (roomUsers.stream().noneMatch(cru -> cru.getUser().getEmail().equals(self))) {
                    inRoom = false;
                }
            }
            List<ChatMembersEntity> members = entity.getMembers();
            for (ChatMembersEntity membersEntity : members) {
                UserStateEntity user = membersEntity.getUser();
                if (self.equals(user.getEmail())) {
                    this.unreadMsgCount = membersEntity.getUnreadMsgCount();
                    this.status = membersEntity.getStatus();
                } else if (room == null) { //单聊
                    this.members.add(new InnerServiceUserModel(user));
                }
                if (user.getEmail().equals(entity.getLastMsgSender())) {
                    this.lastMsgSender = user.getNickName();
                }
            }
            this.lastMsg = entity.getLastMsg();
            this.lastMsgTime = entity.getLastMsgTime();
        }
    }
}
