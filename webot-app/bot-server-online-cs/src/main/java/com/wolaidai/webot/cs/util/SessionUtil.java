package com.wolaidai.webot.cs.util;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.security.domain.UserDomain;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

public class SessionUtil {
    public static UserDomain getUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() != null && !(authentication instanceof AnonymousAuthenticationToken)) {
            return JSON.parseObject(JSON.toJSONString(authentication.getPrincipal()), UserDomain.class);
        }
        return null;
    }
}
