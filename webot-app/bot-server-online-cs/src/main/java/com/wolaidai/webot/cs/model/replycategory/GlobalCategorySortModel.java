package com.wolaidai.webot.cs.model.replycategory;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.LinkedHashMap;
import java.util.Map;

@ApiModel(description = "快捷回复排序")
public class GlobalCategorySortModel extends BaseModel {

    @NotNull(message = "{NotNull.category.type}")
    @ApiModelProperty(value = "分类类型:0-文本, 1-附件")
    private Integer type;

    private Integer parentCategoryId;

    private Map<Integer, Integer> sortMap = new LinkedHashMap<>();

    public Integer getParentCategoryId() {
        return parentCategoryId;
    }

    public void setParentCategoryId(Integer parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Map<Integer, Integer> getSortMap() {
        return sortMap;
    }

    public void setSortMap(Map<Integer, Integer> sortMap) {
        this.sortMap = sortMap;
    }
}
