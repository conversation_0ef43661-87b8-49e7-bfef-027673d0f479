package com.wolaidai.webot.cs.model.report;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.wolaidai.webot.cs.model.BaseModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "服务小结统计查询参数")
public class ServiceSummaryModel extends BaseModel {
    @ApiModelProperty(value = "业务类型id")
    private Integer businessId;
    @ApiModelProperty(value = "咨询渠道id")
    private Integer clientTypeId;
    @ApiModelProperty(value = "开始时间")
    private Date beginDate;
    @ApiModelProperty(value = "结束时间")
    private Date endDate;
    private List<ServiceSummaryRowModel> list = new ArrayList<>();

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<ServiceSummaryRowModel> getList() {
        return list;
    }

    public void setList(List<ServiceSummaryRowModel> list) {
        this.list = list;
    }

}
