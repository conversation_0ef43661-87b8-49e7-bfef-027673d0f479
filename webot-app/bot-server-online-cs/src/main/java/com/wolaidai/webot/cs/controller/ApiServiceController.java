package com.wolaidai.webot.cs.controller;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import com.wolaidai.webot.data.elastic.entity.ChatHistoryElasticEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexChatHistoryElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.ServiceSummaryEntity;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.repo.CommonConfigRepo;
import com.wolaidai.webot.data.mysql.repo.ServiceSummaryRepo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.sessionlist.UserSessionRowModel;
import com.wolaidai.webot.data.elastic.entity.ServiceSummaryElasticEntity;
import com.wolaidai.webot.data.elastic.entity.SessionListElasticEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexServiceSummaryElasticRepo;
import com.wolaidai.webot.data.elastic.repo.ComplexSessionListElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessTypeRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessUnitRepo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "api服务接口")
@RestController
@RequestMapping("/api")
public class ApiServiceController extends BaseController {

    protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ComplexSessionListElasticRepo complexSessionListElasticRepo;

    @Autowired
    private ComplexServiceSummaryElasticRepo complexServiceSummaryElasticRepo;

    @Autowired
    private ComplexChatHistoryElasticRepo complexChatHistoryElasticRepo;

    @Autowired 
    private BusinessTypeRepo businessTypeRepo;

    @Autowired
    private BusinessUnitRepo businessUnitRepo;

    @Autowired
    private UserStateRepo userStateRepo;

    @Autowired
    private CustomerInfoEncService customerInfoEncService;

    @Autowired
    private CommonConfigRepo commonConfigRepo;
    
    @Autowired
    private ServiceSummaryRepo serviceSummaryRepo;

    @GetMapping("/user/history")
    @ApiOperation(value = "获取用户历史会话")
    public ResponseModel getUserHistories(Integer userId) {
        SearchHits<SessionListElasticEntity> searchHits = complexSessionListElasticRepo.findRecordByUserId(userId);
        List<SessionListElasticEntity> sessionList = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
        List<UserSessionRowModel> userSessionList = new ArrayList<>();
        for (SessionListElasticEntity session : sessionList) {
            String lastServiceUser = session.getLastServiceUser();
            UserStateEntity userStateEntity = userStateRepo.findByOrgIdAndEmail(1, lastServiceUser);
            UserSessionRowModel userSessionrModel = new UserSessionRowModel();
            JSONArray customers = session.getCustomerDetail().getJSONArray("customers");
            for (int i = 0; i < customers.size(); i++) {
                JSONObject customer = customers.getJSONObject(i);
                if (customer.getInteger("userId").equals(userId)) {
                    userSessionrModel.setMobile(customerInfoEncService.decryptMobile(customer.getString("mobile")));
                    break;
                }
            }
            userSessionrModel.setStartTime(session.getCreateTime());
            userSessionrModel.setType("online");
            userSessionrModel.setLastServiceUser(userStateEntity.getNickName());
            userSessionrModel.setWorkNo(userStateEntity.getWorkNumber());
            userSessionrModel.setGroup("online");
            ServiceSummaryElasticEntity summary = complexServiceSummaryElasticRepo.findLatestServiceSummary(session.getId());
            if (summary != null) {
                BusinessUnitEntity businessUnitEntity = businessUnitRepo.findById(summary.getUnitId()).get();
                String summaryInfo = "";
                if (summary.getTypeId() != null) {
                    BusinessTypeEntity businessTypeEntity = businessTypeRepo.findById(summary.getTypeId()).get();
                    summaryInfo = businessTypeEntity.getTitle();
                    while (businessTypeEntity.getParentType() != null) {
                        businessTypeEntity = businessTypeEntity.getParentType();
                        summaryInfo = businessTypeEntity.getTitle() + "#" + summaryInfo;
                    }
                }
                summaryInfo = StringUtils.isEmpty(summaryInfo) ? businessUnitEntity.getName() : businessUnitEntity.getName() + "#" + summaryInfo;
                userSessionrModel.setSummaryTime(summary.getUpdateTime());
                userSessionrModel.setSummary(summaryInfo);
                userSessionrModel.setSummaryRemark(summary.getRemark());

                ServiceSummaryEntity summaryRepoBySessionId = serviceSummaryRepo.findBySessionId(session.getId());
                if (summaryRepoBySessionId != null) {
                    userSessionrModel.setAiSummary(summaryRepoBySessionId.getAiRemark());
                }
            }
            userSessionList.add(userSessionrModel);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "获取用户历史记录成功", userSessionList);
        
    }


    @PostMapping("/summary/sensitive")
    @ApiOperation(value = "接收服务小结敏感词推送")
    public ResponseModel receiveSummarySensitiveInfo(@RequestBody Map<String, Object> params) {
        LOGGER.info("接收服务小结敏感词推送，推送数据:{}", params);
        if(params == null || params.isEmpty() || params.get("data") == null || !(params.get("data") instanceof List) || ((List)params.get("data")).isEmpty()) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "推送数据为空");
        }
        List<String> dataList = (List<String>)params.get("data");
        if(dataList.size() > 500) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "推送数据请勿超过500条");
        }

        Integer orgId = -1;
        String type = CommonConfigEntity.TYPE_SUMMARY_SENSITIVE;
        String content = JSON.toJSONString(dataList);
        Date now = new Date();
        CommonConfigEntity configEntity = commonConfigRepo.findOneByOrgIdAndType(orgId, type);
        if(configEntity == null) {
            configEntity = new CommonConfigEntity();
            configEntity.setOrgId(orgId);
            configEntity.setType(type);
            configEntity.setCreateTime(now);
        }
        configEntity.setContent(content);
        configEntity.setUpdateTime(now);
        commonConfigRepo.save(configEntity);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "推送成功");
    }


    @GetMapping("/user/attachment")
    @ApiOperation(value = "获取用户上传的文件")
    public ResponseModel getUserAttachment(@RequestParam Integer userId, @RequestParam Integer interval) {
        if(userId == null || interval == null || interval <= 0) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "请提供正确的查询参数");
        }

        Date start = Date.from(LocalDate.now().minusDays(interval-1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        SearchHits<SessionListElasticEntity> searchHits = complexSessionListElasticRepo.findRecordByUserIdAndTime(1, userId, start, null);
        Set<String> gcidSet = searchHits.stream().map(v -> v.getContent().getGcid()).collect(Collectors.toSet());

        List<Map<String, Object>> resultList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(gcidSet)) {
            SearchHits<ChatHistoryElasticEntity> attachmentHits = complexChatHistoryElasticRepo.findUserAttachmentHistories(gcidSet);
            if (attachmentHits != null) {
                List<ChatHistoryElasticEntity> chatHistoryList = attachmentHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
                for (ChatHistoryElasticEntity elasticEntity : chatHistoryList) {
                    String medialUrl = buildMedialUrl("1", elasticEntity.getContent());
                    Map<String, Object> map = new HashMap<>();
                    map.put("url", medialUrl);
                    map.put("type", elasticEntity.getType());
                    map.put("date", elasticEntity.getDate());
                    resultList.add(map);
                }
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "获取成功", resultList);
    }

    
    
}
