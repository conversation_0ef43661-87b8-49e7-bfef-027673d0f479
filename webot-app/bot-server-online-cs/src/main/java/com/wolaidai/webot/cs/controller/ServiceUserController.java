package com.wolaidai.webot.cs.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.user.UserListModel;
import com.wolaidai.webot.cs.model.user.UserRowModel;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;
import com.wolaidai.webot.security.domain.UserDomain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "客服用户接口")
@RestController
@RequestMapping("/seviceUser")
public class ServiceUserController extends BaseController {

    @Autowired
    private UserRepo userRepo;

    @GetMapping("/list")
    @ApiOperation(value = "获取客服列表", response = UserListModel.class)
    public ResponseModel getUsers(UserListModel model) {
        UserDomain user = getUser();
        Integer status = model.getStatus();
        List<UserInfo> list = null;
        if (null == status || (status != 0 && status != 1)) {
            list = userRepo.find(user.getOrganizationId(), appPropertyConfig.getProductId());
        } else {
            list = userRepo.findByStatus(user.getOrganizationId(), appPropertyConfig.getProductId(), status);
        }
        for (UserInfo i : list) {
            model.getList().add(new UserRowModel(i));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

}
