package com.wolaidai.webot.cs.controller;

import java.util.Arrays;
import java.util.Base64;
import java.util.Base64.Decoder;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wolaidai.webot.common.util.OssFileClient;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.task.TaskRowModel;
import com.wolaidai.webot.cs.model.task.TaskSearchModel;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.repo.TaskRepo;
import com.wolaidai.webot.security.domain.UserDomain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "异步任务接口")
@RestController
@RequestMapping("/asyncTask")
public class AsyncTaskController extends BaseController {

    @Autowired
    private TaskRepo taskRepo;

    @GetMapping
    @ApiOperation(value = "获取任务列表")
    public ResponseModel getTasks(TaskSearchModel model) {
        Pageable page = model.getPageNumber() == -1 ? Pageable.unpaged() : PageRequest.of(model.getPageNumber(), model.getPageSize());
        UserDomain user = getUser();
        Page<TaskEntity> tasks = taskRepo.findByOrgIdAndCreatorAndCreateTimeGreaterThanEqualOrderByIdDesc(user.getOrganizationId(), user.getEmail(), DateUtils.addDays(new Date(), -10), page);
        model.setTotal(tasks.getTotalElements());
        if (tasks.hasContent()) {
            for (TaskEntity task : tasks.getContent()) {
                model.getList().add(new TaskRowModel(task));
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @DeleteMapping("/{ids}")
    @ApiOperation(value = "批量删除任务")
    public ResponseModel deleteTasks(@PathVariable Integer[] ids) {
        UserDomain user = getUser();
        List<TaskEntity> tasks = null;
        if (ids.length > 0 && (tasks = taskRepo.findByOrgIdAndIdInAndCreator(user.getOrganizationId(), Arrays.asList(ids), user.getEmail())).size() > 0) {
            Decoder decoder = Base64.getDecoder();
            for (TaskEntity task : tasks) {
                if (Objects.equals(TaskEntity.SUCCESS_STATUS, task.getStatus())) {
                    OssFileClient.deleteObject(appPropertyConfig.getOssBucketName(), new String(decoder.decode(task.getFileId())));
                }
            }
            taskRepo.deleteAll(tasks);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "删除成功");
    }
}
