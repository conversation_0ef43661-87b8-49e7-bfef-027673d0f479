package com.wolaidai.webot.cs.model.sessionlist.personal;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiModel(description = "个人聊天历史记录")
public class PersonalSessionSearchModel extends BaseModel {
    private String key;
    private Date startTime;
    private Date endTime;
    private List<PersonalSessionRowModel> list = new ArrayList<>();

    private Integer type;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<PersonalSessionRowModel> getList() {
        return list;
    }

    public void setList(List<PersonalSessionRowModel> list) {
        this.list = list;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
