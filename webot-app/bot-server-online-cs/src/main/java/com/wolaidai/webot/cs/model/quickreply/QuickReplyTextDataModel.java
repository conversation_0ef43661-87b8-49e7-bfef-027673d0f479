package com.wolaidai.webot.cs.model.quickreply;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.chat.QuickReplyEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ReplyCategoryEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "快捷回复文本返回数据")
public class QuickReplyTextDataModel extends BaseModel {

    private final int NODE_TYPE_CATEGORY_INT = 0;
    private final int NODE_TYPE_CONTENT_INT = 1;

    private Integer id;
    @ApiModelProperty(value = "分类名或文本内容")
    private String content;
    @ApiModelProperty(value = "类型,0:分类;1:文本")
    private Integer type;
    @ApiModelProperty(value = "节点Id,id-type")
    private String nodeId;
    private List<QuickReplyTextDataModel> children = new ArrayList<>();
    @JsonIgnore
    private Integer position; //排序用

    public QuickReplyTextDataModel(QuickReplyEntity quickReplyEntity) {
        this.id = quickReplyEntity.getId();
        this.content = quickReplyEntity.getContent();
        this.type = NODE_TYPE_CONTENT_INT;
        this.nodeId = quickReplyEntity.getId() + "-" + NODE_TYPE_CONTENT_INT;
        this.position = quickReplyEntity.getPosition();

    }

    public QuickReplyTextDataModel(ReplyCategoryEntity categoryEntity) {
        this.id = categoryEntity.getId();
        this.content = categoryEntity.getName();
        this.type = NODE_TYPE_CATEGORY_INT;
        this.nodeId = categoryEntity.getId() + "-" + NODE_TYPE_CATEGORY_INT;
        this.position = categoryEntity.getPosition();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public List<QuickReplyTextDataModel> getChildren() {
        return children;
    }

    public void setChildren(List<QuickReplyTextDataModel> children) {
        this.children = children;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public void getFullContent(List<String> fullContents) {
        List<QuickReplyTextDataModel> children = getChildren();
        for (QuickReplyTextDataModel child1 : children) {
            if (CollectionUtils.isNotEmpty(child1.getChildren())) {
                for (QuickReplyTextDataModel child2 : child1.getChildren()) {
                    fullContents.add(content + "," + child1.content + "," + child2.content);
                }
            } else {
                //没有内容的二级节点舍弃
                if (child1.type == 0) {
                    continue;
                }
                //补齐三层结构
                fullContents.add(content + ",," + child1.content);
            }
        }
    }
}
