package com.wolaidai.webot.cs.security;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.session.Session;
import org.springframework.session.SessionRepository;

public class SafeDeserializationRepository<S extends Session> implements SessionRepository<S> {
    private static final Logger logger = LogManager.getLogger(SafeDeserializationRepository.class);
    private final SessionRepository<S> delegate;
    private final StringRedisTemplate redisTemplate;

    public SafeDeserializationRepository(SessionRepository<S> delegate, StringRedisTemplate redisTemplate) {
        this.delegate = delegate;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public S createSession() {
        return delegate.createSession();
    }

    @Override
    public void save(S session) {
        delegate.save(session);
    }

    @Override
    public S findById(String id) {
        try {
            return delegate.findById(id);
        } catch (SerializationException e) {
            logger.warn("Deleting non-deserializable session with key {}", id);
            redisTemplate.delete("spring:session:sessions:" + id);
            return null;
        }
    }

    @Override
    public void deleteById(String id) {
        delegate.deleteById(id);
    }

}
