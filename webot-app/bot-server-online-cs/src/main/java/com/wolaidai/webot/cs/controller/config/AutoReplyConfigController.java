package com.wolaidai.webot.cs.controller.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.enums.AuditAction;
import com.wolaidai.webot.data.mysql.enums.AuditModule;
import com.wolaidai.webot.data.redis.constant.RedisKey;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "会话自动应答设置接口")
@RestController
@RequestMapping("/autoReply/config")
public class AutoReplyConfigController extends BaseController {
    @Autowired
    private ConfigService configService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @GetMapping
    @ApiOperation(value = "查询会话自动应答设置")
    public ResponseModel getAutoReplyConfig(@RequestParam String clientType, String configType) {
        Integer orgId = getUser().getOrganizationId();
        if (StringUtils.isBlank(configType)) {
            JSONObject configs = configService.readData(true, orgId, String.format(RedisKey.AUTOREPLY_CONFIG, orgId, clientType), CommonConfigEntity.TYPE_AUTOREPLY_CONFIG, clientType);
            JSONObject data = new JSONObject();
            configs.forEach((k, v) -> {
                if (null != v) {
                    data.put(k, JSONObject.parse(v.toString()));
                }
            });
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", data);
        }
        String config = configService.read(true, orgId, String.format(RedisKey.AUTOREPLY_CONFIG, orgId, clientType), configType, CommonConfigEntity.TYPE_AUTOREPLY_CONFIG, clientType, configType);
        if (null != config) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", JSONObject.parse(config));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功");
    }

    @PostMapping
    @ApiOperation(value = "更新会话自动应答设置")
    public ResponseModel updateAutoReplyConfig(@RequestBody JSONObject data, @RequestParam String clientType, @RequestParam String configType) {
        redisTemplate.opsForHash().delete(String.format(RedisKey.AUTOREPLY_CONFIG, getUser().getOrganizationId(), clientType), configType);
        configService.save(getUser().getOrganizationId(), data.toString(), CommonConfigEntity.TYPE_AUTOREPLY_CONFIG, clientType, configType);
        auditLog(AuditAction.UPDATE, AuditModule.AUTOREPLY_CONFIG, clientType + ":" + configType, "更新会话自动应答设置");
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }
}
