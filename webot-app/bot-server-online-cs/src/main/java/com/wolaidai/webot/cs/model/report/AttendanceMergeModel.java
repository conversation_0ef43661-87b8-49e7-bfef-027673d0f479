package com.wolaidai.webot.cs.model.report;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.mysql.entity.report.AttendanceReportEntity;

public class AttendanceMergeModel {

    private String email;
    private String date;
    private String workNumber;
    private String nickName;
    private int totalLogin = 0;
    private int totalOnline = 0;
    private int totalBusy = 0;
    private int totalRest = 0;
    private int totalLeave = 0;
    private int totalEat = 0;
    private int totalStudy = 0;
    private int sessionCount = 0;
    private long sessionDuration = 0;
    private long sessionCostAvg = 0;
    private int firstAnswerAvg = 0;
    private int answerAvg = 0;
    private int evaluationCount = 0;
    private float evaluationPercent = 0;
    private int noEvaluationCount = 0;
    private float noEvaluationPercent = 0;
    private int starOne = 0;
    private int starTwo = 0;
    private int starThree = 0;
    private int starFour = 0;
    private int starFive = 0;
    private float satisfactionPercent = 0;

    private int value(Integer n) {
        if (null == n) {
            return 0;
        }
        return n;
    }

    private long value(Long n) {
        if (null == n) {
            return 0;
        }
        return n;
    }

    private float value(Float n) {
        if (null == n) {
            return 0;
        }
        return n;
    }

    public AttendanceMergeModel(String date, List<AttendanceReportEntity> list) {
        this.date = date;
        if (null == list || list.size() == 0) {
            return;
        }
        long totalFirstResponseTime = 0;
        float totalAVGResponseTime = 0f;
        for (AttendanceReportEntity a : list) {
            if (null == workNumber) {
                this.email = a.getEmail();
                this.workNumber = a.getWorkNumber();
                this.nickName = a.getNickName();
            }
            totalLogin += value(a.getTotalLogin());
            totalOnline += value(a.getTotalOnline());
            totalBusy += value(a.getTotalBusy());
            totalRest += value(a.getTotalRest());
            totalLeave += value(a.getTotalLeave());
            totalEat += value(a.getTotalEat());
            totalStudy += value(a.getTotalStudy());
            sessionCount += value(a.getSessionCount());
            sessionDuration += value(a.getSessionDuration());
            evaluationCount += value(a.getEvaluationCount());
            evaluationPercent += value(a.getEvaluationPercent());
            noEvaluationCount += value(a.getNoEvaluationCount());
            noEvaluationPercent += value(a.getNoEvaluationPercent());
            starOne += value(a.getStarOne());
            starTwo += value(a.getStarTwo());
            starThree += value(a.getStarThree());
            starFour += value(a.getStarFour());
            starFive += value(a.getStarFive());
            satisfactionPercent += value(a.getSatisfactionPercent());
            totalFirstResponseTime += value(a.getTotalFirstRsp());
            totalAVGResponseTime += value(a.getTotalAvgRsp());
        }
        sessionCostAvg = (long) (sessionDuration / (float) sessionCount);
        firstAnswerAvg = (int) (totalFirstResponseTime / (float) sessionCount);
        answerAvg = (int) (totalAVGResponseTime / (float) sessionCount);
        evaluationPercent = ReportUtil.getPercent(evaluationCount, sessionCount);
        noEvaluationPercent = ReportUtil.getPercent(noEvaluationCount, sessionCount);
        satisfactionPercent = ReportUtil.getPercent(starFour + starFive, evaluationCount);
    }

    public AttendanceMergeModel(List<AttendanceMergeModel> list) {
        if (null == list || list.size() == 0) {
            return;
        }
        for (AttendanceMergeModel a : list) {
            totalLogin += a.totalLogin;
            totalOnline += a.totalOnline;
            totalBusy += a.totalBusy;
            totalRest += a.totalRest;
            totalLeave += a.totalLeave;
            totalEat += a.totalEat;
            totalStudy += a.totalStudy;
            sessionCount += a.sessionCount;
            sessionDuration += a.sessionDuration;
            sessionCostAvg += a.sessionCostAvg;
            firstAnswerAvg += a.firstAnswerAvg;
            answerAvg += a.answerAvg;
            evaluationCount += a.evaluationCount;
            evaluationPercent += a.evaluationPercent;
            noEvaluationCount += a.noEvaluationCount;
            noEvaluationPercent += a.noEvaluationPercent;
            starOne += a.starOne;
            starTwo += a.starTwo;
            starThree += a.starThree;
            starFour += a.starFour;
            starFive += a.starFive;
            satisfactionPercent += a.satisfactionPercent;
        }
        int count = list.size();
        totalLogin = totalLogin / count;
        totalOnline = totalOnline / count;
        totalBusy = totalBusy / count;
        totalRest = totalRest / count;
        totalLeave = totalLeave / count;
        totalEat = totalEat / count;
        totalStudy = totalStudy / count;
        sessionCount = sessionCount / count;
        sessionDuration = sessionDuration / count;
        sessionCostAvg = sessionCostAvg / count;
        firstAnswerAvg = firstAnswerAvg / count;
        answerAvg = answerAvg / count;
        evaluationCount = evaluationCount / count;
        noEvaluationCount = noEvaluationCount / count;
        starOne = starOne / count;
        starTwo = starTwo / count;
        starThree = starThree / count;
        starFour = starFour / count;
        starFive = starFive / count;
        evaluationPercent = BigDecimal.valueOf(evaluationPercent).divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP).floatValue();
        noEvaluationPercent = BigDecimal.valueOf(noEvaluationPercent).divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP).floatValue();
        satisfactionPercent = BigDecimal.valueOf(satisfactionPercent).divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP).floatValue();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public int getTotalLogin() {
        return totalLogin;
    }

    public void setTotalLogin(int totalLogin) {
        this.totalLogin = totalLogin;
    }

    public int getTotalOnline() {
        return totalOnline;
    }

    public void setTotalOnline(int totalOnline) {
        this.totalOnline = totalOnline;
    }

    public int getTotalBusy() {
        return totalBusy;
    }

    public void setTotalBusy(int totalBusy) {
        this.totalBusy = totalBusy;
    }

    public int getTotalRest() {
        return totalRest;
    }

    public void setTotalRest(int totalRest) {
        this.totalRest = totalRest;
    }

    public int getTotalLeave() {
        return totalLeave;
    }

    public void setTotalLeave(int totalLeave) {
        this.totalLeave = totalLeave;
    }

    public int getTotalEat() {
        return totalEat;
    }

    public void setTotalEat(int totalEat) {
        this.totalEat = totalEat;
    }

    public int getTotalStudy() {
        return totalStudy;
    }

    public void setTotalStudy(int totalStudy) {
        this.totalStudy = totalStudy;
    }

    public int getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(int sessionCount) {
        this.sessionCount = sessionCount;
    }

    public long getSessionDuration() {
        return sessionDuration;
    }

    public void setSessionDuration(long sessionDuration) {
        this.sessionDuration = sessionDuration;
    }

    public long getSessionCostAvg() {
        return sessionCostAvg;
    }

    public void setSessionCostAvg(long sessionCostAvg) {
        this.sessionCostAvg = sessionCostAvg;
    }

    public int getFirstAnswerAvg() {
        return firstAnswerAvg;
    }

    public void setFirstAnswerAvg(int firstAnswerAvg) {
        this.firstAnswerAvg = firstAnswerAvg;
    }

    public int getAnswerAvg() {
        return answerAvg;
    }

    public void setAnswerAvg(int answerAvg) {
        this.answerAvg = answerAvg;
    }

    public int getEvaluationCount() {
        return evaluationCount;
    }

    public void setEvaluationCount(int evaluationCount) {
        this.evaluationCount = evaluationCount;
    }

    public float getEvaluationPercent() {
        return evaluationPercent;
    }

    public void setEvaluationPercent(float evaluationPercent) {
        this.evaluationPercent = evaluationPercent;
    }

    public int getNoEvaluationCount() {
        return noEvaluationCount;
    }

    public void setNoEvaluationCount(int noEvaluationCount) {
        this.noEvaluationCount = noEvaluationCount;
    }

    public float getNoEvaluationPercent() {
        return noEvaluationPercent;
    }

    public void setNoEvaluationPercent(float noEvaluationPercent) {
        this.noEvaluationPercent = noEvaluationPercent;
    }

    public int getStarOne() {
        return starOne;
    }

    public void setStarOne(int starOne) {
        this.starOne = starOne;
    }

    public int getStarTwo() {
        return starTwo;
    }

    public void setStarTwo(int starTwo) {
        this.starTwo = starTwo;
    }

    public int getStarThree() {
        return starThree;
    }

    public void setStarThree(int starThree) {
        this.starThree = starThree;
    }

    public int getStarFour() {
        return starFour;
    }

    public void setStarFour(int starFour) {
        this.starFour = starFour;
    }

    public int getStarFive() {
        return starFive;
    }

    public void setStarFive(int starFive) {
        this.starFive = starFive;
    }

    public float getSatisfactionPercent() {
        return satisfactionPercent;
    }

    public void setSatisfactionPercent(float satisfactionPercent) {
        this.satisfactionPercent = satisfactionPercent;
    }

}
