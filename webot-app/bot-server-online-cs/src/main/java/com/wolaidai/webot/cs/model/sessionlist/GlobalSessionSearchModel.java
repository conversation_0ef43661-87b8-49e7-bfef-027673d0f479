package com.wolaidai.webot.cs.model.sessionlist;

import com.wolaidai.webot.cs.model.PageableModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@ApiModel(description = "公共聊天历史记录")
public class GlobalSessionSearchModel extends PageableModel {
    @ApiModelProperty(value = "会话内容")
    private String content;
    @ApiModelProperty(value = "会话建立时间")
    private Date startTime;
    @ApiModelProperty(value = "会话结束时间")
    private Date endTime;
    @ApiModelProperty(value = "客服email,逗号分隔")
    private List<String> staffEmails = new ArrayList<>();
    @ApiModelProperty(value = "业务类型")
    private List<Integer> businessIds = new ArrayList<>();
    @ApiModelProperty(value = "咨询渠道")
    private List<Integer> clientIds = new ArrayList<>();
    @ApiModelProperty(value = "评价,0-5,0-未评价")
    private List<Integer> appraiseLevels = new ArrayList<>();
    @ApiModelProperty(value = "是否有图片,0-无,1-有")
    private Integer hasPic;
    @ApiModelProperty(value = "超时条件,firstRespTimeout,avgRespTimeout,respTimeout")
    private Set<String> respKeys = new HashSet<>();
    @ApiModelProperty(value = "客户uuid")
    private String uuid;
    @ApiModelProperty(value = "客户userId")
    private Integer userId;
    @ApiModelProperty(value = "客户手机号")
    private String mobile;
    @ApiModelProperty(value = "客户昵称")
    private String customerName;
    @ApiModelProperty(value = "人脸验证")
    private Integer faceStatus;
    @ApiModelProperty(value = "客户消息数最小值")
    private Integer userMsgCountMin;
    @ApiModelProperty(value = "客户消息数最大值")
    private Integer userMsgCountMax;
    @ApiModelProperty(value = "客服消息数最小值")
    private Integer serviceMsgCountMin;
    @ApiModelProperty(value = "客服消息数最大值")
    private Integer serviceMsgCountMax;

    private List<GlobalSessionRowModel> list = new ArrayList<>();

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<String> getStaffEmails() {
        return staffEmails;
    }

    public void setStaffEmails(List<String> staffEmails) {
        this.staffEmails = staffEmails;
    }

    public List<Integer> getBusinessIds() {
        return businessIds;
    }

    public void setBusinessIds(List<Integer> businessIds) {
        this.businessIds = businessIds;
    }

    public List<Integer> getClientIds() {
        return clientIds;
    }

    public void setClientIds(List<Integer> clientIds) {
        this.clientIds = clientIds;
    }

    public List<Integer> getAppraiseLevels() {
        return appraiseLevels;
    }

    public void setAppraiseLevels(List<Integer> appraiseLevels) {
        this.appraiseLevels = appraiseLevels;
    }

    public Integer getHasPic() {
        return hasPic;
    }

    public void setHasPic(Integer hasPic) {
        this.hasPic = hasPic;
    }

    public Set<String> getRespKeys() {
        return respKeys;
    }

    public void setRespKeys(Set<String> respKeys) {
        this.respKeys = respKeys;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getFaceStatus() {
        return faceStatus;
    }

    public void setFaceStatus(Integer faceStatus) {
        this.faceStatus = faceStatus;
    }

    public List<GlobalSessionRowModel> getList() {
        return list;
    }

    public void setList(List<GlobalSessionRowModel> list) {
        this.list = list;
    }

    public Integer getUserMsgCountMin() {
        return userMsgCountMin;
    }

    public void setUserMsgCountMin(Integer userMsgCountMin) {
        this.userMsgCountMin = userMsgCountMin;
    }

    public Integer getUserMsgCountMax() {
        return userMsgCountMax;
    }

    public void setUserMsgCountMax(Integer userMsgCountMax) {
        this.userMsgCountMax = userMsgCountMax;
    }

    public Integer getServiceMsgCountMin() {
        return serviceMsgCountMin;
    }

    public void setServiceMsgCountMin(Integer serviceMsgCountMin) {
        this.serviceMsgCountMin = serviceMsgCountMin;
    }

    public Integer getServiceMsgCountMax() {
        return serviceMsgCountMax;
    }

    public void setServiceMsgCountMax(Integer serviceMsgCountMax) {
        this.serviceMsgCountMax = serviceMsgCountMax;
    }

    
}
