package com.wolaidai.webot.cs.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.monitor.CSMonitorResultModel;
import com.wolaidai.webot.cs.model.monitor.MonitorResultModel;
import com.wolaidai.webot.cs.model.monitor.QueueRowModel;
import com.wolaidai.webot.cs.model.monitor.UserStateRowModel;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.MergeUserStateDuration;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.elastic.entity.HistoryManualEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexChatHistoryElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.model.UserStateDuration;
import com.wolaidai.webot.data.mysql.repo.QueueListRepo;
import com.wolaidai.webot.data.mysql.repo.SatisfactionDataRepo;
import com.wolaidai.webot.data.mysql.repo.ServiceSummaryRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import com.wolaidai.webot.security.domain.UserDomain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "实时监控")
@RestController
@RequestMapping("/monitor")
public class MonitorController extends BaseController {

    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    private QueueListRepo queueListRepo;
    @Autowired
    private SessionListRepo sessionRepo;
    @Autowired
    private UserRepo userRepo;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private ComplexChatHistoryElasticRepo chatHistoryElasticRepo;
    @Autowired
    private SatisfactionDataRepo satisfactionDataRepo;
    @Autowired
    private ServiceSummaryRepo serviceSummaryRepo;
    @Autowired
    private ConfigService configService;

    @GetMapping
    @ApiOperation(value = "获取实时数据", response = MonitorResultModel.class)
    public ResponseModel getMonitorData() {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);
        Date endDate = DateUtils.addDays(today, 1);
        MonitorResultModel model = new MonitorResultModel();
        model.setRequestCount(queueListRepo.countByOrgIdAndCreateTimeGreaterThanEqual(orgId, today));
        model.setSessionCount(sessionRepo.countByOrgIdAndCreateTimeGreaterThanEqual(orgId, today));
        model.setSessionPercent(ReportUtil.getPercent(model.getSessionCount(), model.getRequestCount()));
        model.setQueueCount(queueListRepo.countByOrgIdAndStatusAndCreateTimeGreaterThanEqual(orgId, QueueListEntity.INQUEUE_STATUS, today));
        model.setFirstRespTimeout(sessionRepo.countByOrgIdAndFirstRespTimeoutAndCreateTimeGreaterThanEqual(orgId, 1, today));
        model.setAvgRespTimeout(sessionRepo.countByOrgIdAndAvgRespTimeoutAndCreateTimeGreaterThanEqual(orgId, 1, today));
        model.setRespTimeout(sessionRepo.countByOrgIdAndRespTimeoutAndCreateTimeGreaterThanEqual(orgId, 1, today));
        List<UserStateEntity> states = userStateRepo.findByOrgIdAndUserTypeAndCreateTimeGreaterThanEqualOrderByUpdateTimeDesc(orgId, 1, today);
        Map<String, UserInfo> infoMap = userRepo.find(orgId, appPropertyConfig.getProductId()).stream().collect(Collectors.toMap(UserInfo::getEmail, Function.identity()));
        int onlineCount = 0;
        for (UserStateEntity last : states) {
            String email = last.getEmail();
            UserInfo info = infoMap.get(email);
            if (null == info) {
                continue;
            }
            UserExtraField uef = info.getUserExtraField();
            if (Objects.equals(last.getState(), UserStateEntity.STATE_ONLINE)) {
                onlineCount++;
            }
            UserStateRowModel s = new UserStateRowModel();
            s.setEmail(email);
            if (null != uef) {
                s.setWorkNumber(uef.getWorkNumber());
                s.setNickName(uef.getNickName());
            }
            s.setState(CommonUtil.getStateName(last.getState()));
            Date d = last.getUpdateTime();
            if (null != d) {
                s.setDuration(DateUtil.formatSecondsTime((System.currentTimeMillis() - d.getTime()) / 1000, null));
            }
            HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
            s.setSessionCount(NumberUtils.toInt(hashOperations.get(String.format(RedisKey.USER_CURRENT_RECEPTION, orgId), email)));
            s.setMaxReception(configService.getMaxReception(orgId, email));
            model.getStates().add(s);
        }
        model.setOnelineCount(onlineCount);
        List<QueueListEntity> queueList = queueListRepo.findByOrgIdAndStatusAndCreateTimeGreaterThanEqualAndCreateTimeLessThan(orgId, QueueListEntity.INQUEUE_STATUS, today, endDate);
        for (QueueListEntity q : queueList) {
            model.getQueue().add(new QueueRowModel(q));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @GetMapping("/cs")
    @ApiOperation(value = "获取客服实时基础数据", response = CSMonitorResultModel.class)
    public ResponseModel getCSMonitorData() {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);
        Date endDate = DateUtils.addDays(today, 1);
        HashMap<Integer, HashMap<String, ArrayList<UserStateDuration>>> sMap = ReportUtil.getUserStateDuration(orgId, Arrays.asList(email), today, endDate);
        CSMonitorResultModel model = new CSMonitorResultModel();
        model.setSessionCount(sessionRepo.countByOrgIdAndServiceUserAndStatusAndCreateTime(orgId, email, SessionListEntity.STATUS_OFFLINE, today, endDate));
        HashMap<String, ArrayList<UserStateDuration>> m = sMap.get(orgId);
        ArrayList<UserStateDuration> l = null;
        if (null != m && null != (l = m.get(email))) {
            MergeUserStateDuration musd = new MergeUserStateDuration(l);
            UserStateDuration last = l.get(l.size() - 1);
            Integer total = musd.totalLogin;
            if (null == last.getEndTime() && !Objects.equals(last.getState(), UserStateEntity.STATE_OFFLINE)) {
                total += (int) ((System.currentTimeMillis() - last.getCreateTime().getTime()) / 1000l);
            }
            model.setLoginDuration(DateUtil.formatSecondsTime(total, null));
        }
        List<SessionListEntity> sessions = ReportUtil.getSessionList(false, orgId, Arrays.asList(email), today, endDate);
        Map<String, HistoryManualEntity> historyMap = chatHistoryElasticRepo.findManualHistories(orgId, false, email, today, endDate);
        long totalDuration = 0;
        float totalAVGResponseTime = 0;
        HashSet<Integer> sessionIds = new HashSet<>();
        for (SessionListEntity s : sessions) {
            if (!Objects.equals(s.getStatus(), SessionListEntity.STATUS_OFFLINE)) {
                continue;
            }
            totalDuration += ReportUtil.getSessionDurationSeconds(email, s);
            HistoryManualEntity history = historyMap.get(email + s.getSessionKey());
            if (null != history) {
                if (null != history.getAvgResponseTime()) {
                    totalAVGResponseTime += history.getAvgResponseTime();
                }
            }
            sessionIds.add(s.getId());
        }
        HashMap<Integer, Integer> levelMap = new HashMap<>();
        List<SatisfactionDataEntity> sds = satisfactionDataRepo.findByOrgIdAndSessionIdInAndCreateTimeGreaterThanEqualAndCreateTimeLessThan(orgId, sessionIds, today, endDate);
        int total = 0;
        for (SatisfactionDataEntity sd : sds) {
            if (null != sd.getLevel()) {
                total++;
                Integer count = levelMap.get(sd.getLevel());
                if (null == count) {
                    levelMap.put(sd.getLevel(), 1);
                } else {
                    levelMap.put(sd.getLevel(), count + 1);
                }
            }
        }
        int four = null != levelMap.get(4) ? levelMap.get(4) : 0;
        int five = null != levelMap.get(5) ? levelMap.get(5) : 0;
        model.setSatisfactionPercent(ReportUtil.getPercent(four + five, total) + "%");
        if (model.getSessionCount() > 0) {
            model.setResponseTimeAvg(DateUtil.formatSecondsTime(totalAVGResponseTime / model.getSessionCount(), null));
            model.setSessionDurationAvg(DateUtil.formatSecondsTime(totalDuration / model.getSessionCount(), null));
            model.setServiceSummaryPercent(ReportUtil.getPercent(serviceSummaryRepo.countBySessionIdIn(sessionIds), model.getSessionCount()) + "%");
        } else {
            model.setResponseTimeAvg(DateUtil.formatSecondsTime(0, null));
            model.setSessionDurationAvg(DateUtil.formatSecondsTime(0, null));
            model.setServiceSummaryPercent("0%");
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

}
