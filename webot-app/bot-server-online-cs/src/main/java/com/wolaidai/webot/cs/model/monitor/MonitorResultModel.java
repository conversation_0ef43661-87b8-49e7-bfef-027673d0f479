package com.wolaidai.webot.cs.model.monitor;

import java.util.ArrayList;
import java.util.List;

import com.wolaidai.webot.cs.model.BaseModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "在线监控返回")
public class MonitorResultModel extends BaseModel {

    @ApiModelProperty(value = "点击“人工客服”总数")
    private Integer requestCount;
    @ApiModelProperty(value = "会话总数")
    private Integer sessionCount;
    @ApiModelProperty(value = "接通率")
    private Float sessionPercent;
    @ApiModelProperty(value = "排队数")
    private Integer queueCount;
    @ApiModelProperty(value = "在线客服数")
    private Integer onelineCount;
    @ApiModelProperty(value = "首次响应超时")
    private Integer firstRespTimeout = 0;
    @ApiModelProperty(value = "平均响应超时")
    private Integer avgRespTimeout = 0;
    @ApiModelProperty(value = "会话响应超时")
    private Integer respTimeout = 0;
    @ApiModelProperty(value = "当前坐席接待概况")
    List<UserStateRowModel> states = new ArrayList<>();
    @ApiModelProperty(value = "系统当前排队情况")
    List<QueueRowModel> queue = new ArrayList<>();

    public Integer getRequestCount() {
        return requestCount;
    }

    public void setRequestCount(Integer requestCount) {
        this.requestCount = requestCount;
    }

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public Float getSessionPercent() {
        return sessionPercent;
    }

    public void setSessionPercent(Float sessionPercent) {
        this.sessionPercent = sessionPercent;
    }

    public Integer getQueueCount() {
        return queueCount;
    }

    public void setQueueCount(Integer queueCount) {
        this.queueCount = queueCount;
    }

    public Integer getOnelineCount() {
        return onelineCount;
    }

    public void setOnelineCount(Integer onelineCount) {
        this.onelineCount = onelineCount;
    }

    public Integer getFirstRespTimeout() {
        return firstRespTimeout;
    }

    public void setFirstRespTimeout(Integer firstRespTimeout) {
        this.firstRespTimeout = firstRespTimeout;
    }

    public Integer getAvgRespTimeout() {
        return avgRespTimeout;
    }

    public void setAvgRespTimeout(Integer avgRespTimeout) {
        this.avgRespTimeout = avgRespTimeout;
    }

    public Integer getRespTimeout() {
        return respTimeout;
    }

    public void setRespTimeout(Integer respTimeout) {
        this.respTimeout = respTimeout;
    }

    public List<UserStateRowModel> getStates() {
        return states;
    }

    public void setStates(List<UserStateRowModel> states) {
        this.states = states;
    }

    public List<QueueRowModel> getQueue() {
        return queue;
    }

    public void setQueue(List<QueueRowModel> queue) {
        this.queue = queue;
    }

}
