package com.wolaidai.webot.cs.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.welab.privacy.crypto.SpringCryptoHelper;
import com.welab.privacy.manager.PrivacyBean;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class CustomerInfoEncServiceImpl implements CustomerInfoEncService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SpringCryptoHelper springCryptoHelper;

    
    @Override
    public String encryptMobile(String mobile) {
        PrivacyBean mobilePrivacyBean = springCryptoHelper.getConfig("cs_bot", "session_list", "mobile");
        if(StringUtils.isNotBlank(mobile) && mobile.length()==11 && mobilePrivacyBean != null){
            String encMobile = springCryptoHelper.encrypt(mobilePrivacyBean.getDatabase(), mobilePrivacyBean.getTable(), mobilePrivacyBean.getOrigin(), mobile);
            return encMobile;
        }
        return mobile;
    }


    @Override
    public String decryptMobile(String mobile) {
        PrivacyBean mobilePrivacyBean = springCryptoHelper.getConfig("cs_bot", "session_list", "mobile");
        if(StringUtils.isNotBlank(mobile) && mobile.length()>11 && mobilePrivacyBean != null){
            String decMobile = springCryptoHelper.decrypt(mobilePrivacyBean.getDatabase(), mobilePrivacyBean.getTable(), mobilePrivacyBean.getOrigin(), mobile);
            return decMobile;
        }
        return mobile;
    }

    @Override
    public String encryptCnid(String cnid) {
        PrivacyBean cnidPrivacyBean = springCryptoHelper.getConfig("cs_bot", "session_list", "cnid");
        if(StringUtils.isNotBlank(cnid) && cnid.length()<=18 && cnidPrivacyBean != null){
            String encCnid = springCryptoHelper.encrypt(cnidPrivacyBean.getDatabase(), cnidPrivacyBean.getTable(), cnidPrivacyBean.getOrigin(), cnid);
            return encCnid;
        }
        return cnid;
    }

    @Override
    public String decryptCnid(String cnid) {
        PrivacyBean cnidPrivacyBean = springCryptoHelper.getConfig("cs_bot", "session_list", "cnid");
        if(StringUtils.isNotBlank(cnid) && cnid.length()>18 && cnidPrivacyBean != null){
            String decCnid = springCryptoHelper.decrypt(cnidPrivacyBean.getDatabase(), cnidPrivacyBean.getTable(), cnidPrivacyBean.getOrigin(), cnid);
            return decCnid;
        }
        return cnid;
    }


    @Override
    public void encryptCustomerInfo(JSONArray customers) {
        if(customers == null || customers.size()==0) {
            return;
        }
        for(int i=0; i<customers.size(); i++) {
            JSONObject customer = customers.getJSONObject(i);
            String mobile = encryptMobile(customer.getString("mobile"));
            if(StringUtils.isNotBlank(mobile)) {
                customer.put("mobile", mobile);
            }
            String cnid = encryptCnid(customer.getString("cnid"));
            if(StringUtils.isNotBlank(cnid)) {
                customer.put("cnid", cnid);
            }
        }
    }


    @Override
    public void decryptCustomerInfo(JSONArray customers) {
        if(customers == null || customers.size()==0) {
            return;
        }
        for(int i=0; i<customers.size(); i++) {
            JSONObject customer = customers.getJSONObject(i);
            String mobile = decryptMobile(customer.getString("mobile"));
            if(StringUtils.isNotBlank(mobile)) {
                customer.put("mobile", mobile);
            }
            String cnid = decryptCnid(customer.getString("cnid"));
            if(StringUtils.isNotBlank(cnid)) {
                customer.put("cnid", cnid);
            }
        }
    }
    

    @Override
    public String getDecryptMobile(JSONObject customerDetail) {
        if(customerDetail == null || customerDetail.getJSONArray("customers") == null || customerDetail.getJSONArray("customers").size() == 0) {
            return null;
        }
        JSONArray customers = customerDetail.getJSONArray("customers");
        String mobile = customers.getJSONObject(0).getString("mobile");
        String decMobile = decryptMobile(mobile);
        return decMobile;
    }


}
