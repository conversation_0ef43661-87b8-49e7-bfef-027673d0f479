package com.wolaidai.webot.cs.model.task;

import java.util.Date;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;

public class TaskRowModel extends BaseModel {

    private Integer id;
    private String name;
    private String type;
    private Integer port;
    private Integer status;
    private Integer progress;
    private String fileId;
    private JSONObject importInfo;
    private JSONArray errorLog;
    private String creator;
    private Date createTime;
    private Date updateTime;

    public TaskRowModel(TaskEntity t) {
        this.id = t.getId();
        this.name = t.getName();
        this.type = t.getType();
        this.port = t.getPort();
        this.status = t.getStatus();
        this.progress = t.getProgress();
        this.fileId = t.getFileId();
        this.importInfo = t.getImportInfo();
        this.errorLog = t.getErrorLog();
        this.creator = t.getCreator();
        this.createTime = t.getCreateTime();
        this.updateTime = t.getUpdateTime();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public JSONObject getImportInfo() {
        return importInfo;
    }

    public void setImportInfo(JSONObject importInfo) {
        this.importInfo = importInfo;
    }

    public JSONArray getErrorLog() {
        return errorLog;
    }

    public void setErrorLog(JSONArray errorLog) {
        this.errorLog = errorLog;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
