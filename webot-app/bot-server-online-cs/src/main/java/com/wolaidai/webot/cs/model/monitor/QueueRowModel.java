package com.wolaidai.webot.cs.model.monitor;

import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "在线监控排队")
public class QueueRowModel extends BaseModel {

    @ApiModelProperty(value = "客户端类型名称")
    private String clientTypeName;
    @ApiModelProperty(value = "业务名")
    private String businessName;
    @ApiModelProperty(value = "客户姓名")
    private String customerName;
    @ApiModelProperty(value = "排队时长")
    private String duration;

    public QueueRowModel(QueueListEntity q) {
        this.clientTypeName = CommonUtil.getClientTypeName(q.getClientTypeId());
        this.businessName = q.getBusinessName();
        this.customerName = q.getCustomerName();
        long offset = 0;
        if (null != q.getCreateTime()) {
            offset = (System.currentTimeMillis() - q.getCreateTime().getTime()) / 1000;
        }
        this.duration = DateUtil.formatSecondsTime(offset, null);
    }

    public String getClientTypeName() {
        return clientTypeName;
    }

    public void setClientTypeName(String clientTypeName) {
        this.clientTypeName = clientTypeName;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

}
