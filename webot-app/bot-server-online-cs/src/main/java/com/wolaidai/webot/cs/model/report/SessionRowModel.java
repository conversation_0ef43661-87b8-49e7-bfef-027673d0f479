package com.wolaidai.webot.cs.model.report;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.mysql.entity.report.SessionDetailReportEntity;

import io.swagger.annotations.ApiModelProperty;

public class SessionRowModel extends BaseModel {
    @ApiModelProperty(value = "数据日期")
    private String date;
    @ApiModelProperty(value = "业务名")
    private String businessName = "";
    @ApiModelProperty(value = "客户姓名")
    private String customerName = "";
    @ApiModelProperty(value = "uuid")
    private String uuid = "";
    @ApiModelProperty(value = "userid")
    private String userid = "";
    @ApiModelProperty(value = "客户手机号码")
    private String customerPhone = "";
    @ApiModelProperty(value = "客户端类型ID，1:H5;2:微信;3:企业微信")
    private Integer clientTypeId;
    @ApiModelProperty(value = "客户类型名称")
    private String clientTypeName = "";
    private String email;
    private String workNumber = "";
    private String nickName = "";
    private String queueTime;
    private String waitSeconds;
    private Boolean handle;// 0：未接待；1：已接待
    private String handleStr;
    private String beginTime;
    private String endTime;
    private String durationSeconds;
    private String firstResponse;
    private String responseAvg;
    private Date createTime;

    public SessionRowModel(SessionDetailReportEntity s, SimpleDateFormat sdf) {
        this.date = sdf.format(s.getDataTime());
        this.businessName = s.getBusinessName();
        this.customerName = s.getCustomerName();
        if (null != s.getUuid()) {
            this.uuid = s.getUuid();
        }
        if (null != s.getUserid()) {
            this.userid = String.valueOf(s.getUserid());
        }
        this.customerPhone = CommonUtil.maskPhoneNum(s.getCustomerPhone());
        if (null == this.customerPhone) {
            this.customerPhone = "";
        }
        this.clientTypeId = s.getClientTypeId();
        this.clientTypeName = CommonUtil.getClientTypeName(this.clientTypeId);
        this.email = s.getEmail();
        this.workNumber = s.getWorkNumber();
        if (null == this.workNumber) {
            this.workNumber = "";
        }
        this.nickName = s.getNickName();
        if (null == this.nickName) {
            this.nickName = "";
        }
        this.queueTime = DateUtil.formatDateTime(s.getQueueTime(), "");
        this.waitSeconds = DateUtil.formatSecondsTime(s.getWaitSeconds(), "");
        this.handle = Objects.equals(s.getHandle(), 1);
        this.handleStr = this.handle ? "已接待" : "未接待";
        this.beginTime = DateUtil.formatDateTime(s.getBeginTime(), "");
        this.endTime = DateUtil.formatDateTime(s.getEndTime(), "");
        this.durationSeconds = DateUtil.formatSecondsTime(s.getDurationSeconds(), "");
        this.firstResponse = DateUtil.formatSecondsTime(s.getFirstResponse(), "");
        this.responseAvg = DateUtil.formatSecondsTime(s.getResponseAvg(), "");
        this.createTime = s.getCreateTime();
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public Boolean getHandle() {
        return handle;
    }

    public void setHandle(Boolean handle) {
        this.handle = handle;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public String getClientTypeName() {
        return clientTypeName;
    }

    public void setClientTypeName(String clientTypeName) {
        this.clientTypeName = clientTypeName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getQueueTime() {
        return queueTime;
    }

    public void setQueueTime(String queueTime) {
        this.queueTime = queueTime;
    }

    public String getWaitSeconds() {
        return waitSeconds;
    }

    public void setWaitSeconds(String waitSeconds) {
        this.waitSeconds = waitSeconds;
    }

    public String getHandleStr() {
        return handleStr;
    }

    public void setHandleStr(String handleStr) {
        this.handleStr = handleStr;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getDurationSeconds() {
        return durationSeconds;
    }

    public void setDurationSeconds(String durationSeconds) {
        this.durationSeconds = durationSeconds;
    }

    public String getFirstResponse() {
        return firstResponse;
    }

    public void setFirstResponse(String firstResponse) {
        this.firstResponse = firstResponse;
    }

    public String getResponseAvg() {
        return responseAvg;
    }

    public void setResponseAvg(String responseAvg) {
        this.responseAvg = responseAvg;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
