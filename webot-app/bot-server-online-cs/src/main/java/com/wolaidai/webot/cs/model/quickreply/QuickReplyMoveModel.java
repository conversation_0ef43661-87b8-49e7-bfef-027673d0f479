package com.wolaidai.webot.cs.model.quickreply;

import com.wolaidai.webot.cs.model.BaseModel;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

public class QuickReplyMoveModel extends BaseModel {

    @NotNull(message = "{NotNull.quickReply.category}")
    private Integer categoryId;
    private List<Integer> replyIds = new ArrayList<>();

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public List<Integer> getReplyIds() {
        return replyIds;
    }

    public void setReplyIds(List<Integer> replyIds) {
        this.replyIds = replyIds;
    }
}


