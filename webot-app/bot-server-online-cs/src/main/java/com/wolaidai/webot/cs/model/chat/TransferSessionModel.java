package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "会话转接")
public class TransferSessionModel extends BaseModel {
    @NotBlank(message = "{NotBlank.transfer.email}")
    private String email;
    private String remark;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
