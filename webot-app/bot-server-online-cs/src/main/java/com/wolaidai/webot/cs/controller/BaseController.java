package com.wolaidai.webot.cs.controller;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.IpUtil;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.service.AuditLogService;
import com.wolaidai.webot.cs.util.SessionUtil;
import com.wolaidai.webot.data.mysql.enums.AuditAction;
import com.wolaidai.webot.data.mysql.enums.AuditModule;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.security.domain.ProductInfo;
import com.wolaidai.webot.security.domain.UserDomain;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.util.UrlPathHelper;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

public abstract class BaseController {

    protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    protected final List<Integer> boolTypeValue = Arrays.asList(0, 1);

    @Autowired
    protected AppPropertyConfig appPropertyConfig;

    protected UrlPathHelper urlPathHelper = new UrlPathHelper();

    @Autowired
    protected HttpServletRequest request;

    @Autowired
    private AuditLogService auditLogService;

    protected UserDomain getUser() {
        return SessionUtil.getUser();
    }

    protected void auditLog(AuditAction action, AuditModule module, Object sourceId, String remark) {
        auditLog(null, action, module, sourceId, remark);
    }

    protected void auditLog(UserDomain user, AuditAction action, AuditModule module, Object sourceId, String remark) {
        if (user == null) {
            user = getUser();
        }
        String supportUsername = null;
        if (user!=null) {
            supportUsername = user.getSupportUser();
        }
        auditLogService.auditLog(user, action, module, sourceId, request.getServletPath(), IpUtil.getIpAddress(request), remark, supportUsername);
    }
    
    protected ProductInfo getProductInfo() {
        UserDomain user = getUser();
        if (null == user) {
            return null;
        }
        return user.getProductInfoMap().get(appPropertyConfig.getProductId());
    }

    protected Integer getDataLat() {
        ProductInfo info = getProductInfo();
        if (null != info) {
            return info.getDataLat();
        }
        return null;
    }

    protected UserExtraField getUserExtraField() {
        ProductInfo info = getProductInfo();
        if (null != info) {
            JSONObject extra = info.getExtraFieldJson();
            if (null != extra) {
                return new UserExtraField(extra);
            }
        }
        return null;
    }

    protected Integer getRoleType() {
        ProductInfo info = getProductInfo();
        if (null != info) {
            return info.getRoleType();
        }
        return null;
    }

    protected String buildMedialUrl(String type, String fileId) {
        return appPropertyConfig.getFileServerUrl() + "/" + type + "/" + fileId;
    }
}
