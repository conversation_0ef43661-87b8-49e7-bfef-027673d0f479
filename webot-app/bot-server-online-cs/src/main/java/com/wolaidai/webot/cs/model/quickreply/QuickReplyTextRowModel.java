package com.wolaidai.webot.cs.model.quickreply;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.chat.QuickReplyEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ReplyCategoryEntity;

import java.util.Date;

public class QuickReplyTextRowModel extends BaseModel {

    private Integer id;
    private String content;
    private Integer categoryId;
    private String categoryName;
    private String creator;
    private Date createTime;
    private Date updateTime;

    public QuickReplyTextRowModel() {

    }

    public QuickReplyTextRowModel(QuickReplyEntity quickReplyEntity) {
        this.id = quickReplyEntity.getId();
        this.content = quickReplyEntity.getContent();
        ReplyCategoryEntity category = quickReplyEntity.getCategory();
        if (category != null) {
            this.categoryId = category.getId();
            this.categoryName = category.fullPath();
        }
        this.creator = quickReplyEntity.getCreator();
        this.createTime = quickReplyEntity.getCreateTime();
        this.updateTime = quickReplyEntity.getUpdateTime();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
