package com.wolaidai.webot.cs.model.report;

import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.report.StateReportEntity;

public class StateRowModel extends BaseModel {

    private String email;
    private String date;
    private String workNumber;
    private String nickName;
    private JSONArray onlineData;
    private JSONArray busyData;
    private JSONArray restData;
    private JSO<PERSON>rray leaveData;
    private JSONArray eatData;
    private JSONArray studyData;
    private Date createTime;

    private void formatSeconds(JSONArray arr) {
        if (null == arr || arr.size() == 0) {
            return;
        }
        for (int i = 0; i < arr.size(); i++) {
            JSONObject jo = arr.getJSONObject(i);
            Integer seconds = jo.getInteger("seconds");
            if (null != seconds) {
                jo.put("seconds", DateUtil.formatSecondsTime(seconds, ""));
            } else {
                jo.put("seconds", "");
            }
        }
        Comparator<Object> comparator = new Comparator<Object>() {
            @SuppressWarnings("unchecked")
            @Override
            public int compare(Object o1, Object o2) {
                Map<String, Object> jo1 = (Map<String, Object>) o1;
                Map<String, Object> jo2 = (Map<String, Object>) o2;
                return String.valueOf(jo1.get("begin")).compareTo(String.valueOf(jo2.get("begin")));
            }
        };
        arr.sort(comparator);
    }

    private JSONArray copy(JSONArray arr) {
        if (null != arr && arr.size() > 0) {
            JSONArray arr2 = new JSONArray();
            for (int i = 0; i < arr.size(); i++) {
                arr2.add(new JSONObject().fluentPutAll(arr.getJSONObject(i)));
            }
            return arr2;
        }
        return null;
    }

    public StateRowModel(StateReportEntity s, SimpleDateFormat sdf) {
        this.email = s.getEmail();
        this.date = sdf.format(s.getDataTime());
        this.workNumber = s.getWorkNumber();
        this.nickName = s.getNickName();
        this.onlineData = copy(s.getOnlineData());
        this.busyData = copy(s.getBusyData());
        this.restData = copy(s.getRestData());
        this.leaveData = copy(s.getLeaveData());
        this.eatData = copy(s.getEatData());
        this.studyData = copy(s.getStudyData());
        this.createTime = s.getCreateTime();
        formatSeconds(this.onlineData);
        formatSeconds(this.busyData);
        formatSeconds(this.restData);
        formatSeconds(this.leaveData);
        formatSeconds(this.eatData);
        formatSeconds(this.studyData);
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public JSONArray getOnlineData() {
        return onlineData;
    }

    public void setOnlineData(JSONArray onlineData) {
        this.onlineData = onlineData;
    }

    public JSONArray getBusyData() {
        return busyData;
    }

    public void setBusyData(JSONArray busyData) {
        this.busyData = busyData;
    }

    public JSONArray getRestData() {
        return restData;
    }

    public void setRestData(JSONArray restData) {
        this.restData = restData;
    }

    public JSONArray getLeaveData() {
        return leaveData;
    }

    public void setLeaveData(JSONArray leaveData) {
        this.leaveData = leaveData;
    }

    public JSONArray getEatData() {
        return eatData;
    }

    public void setEatData(JSONArray eatData) {
        this.eatData = eatData;
    }

    public JSONArray getStudyData() {
        return studyData;
    }

    public void setStudyData(JSONArray studyData) {
        this.studyData = studyData;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public int getMaxRows() {
        int rows = 0;
        if (null != this.onlineData) {
            rows = Math.max(rows, this.onlineData.size());
        }
        if (null != this.busyData) {
            rows = Math.max(rows, this.busyData.size());
        }
        if (null != this.restData) {
            rows = Math.max(rows, this.restData.size());
        }
        if (null != this.leaveData) {
            rows = Math.max(rows, this.leaveData.size());
        }
        if (null != this.eatData) {
            rows = Math.max(rows, this.eatData.size());
        }
        if (null != this.studyData) {
            rows = Math.max(rows, this.studyData.size());
        }
        return rows;
    }

}
