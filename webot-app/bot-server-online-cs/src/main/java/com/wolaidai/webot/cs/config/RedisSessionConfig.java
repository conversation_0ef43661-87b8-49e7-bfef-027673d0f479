package com.wolaidai.webot.cs.config;

import com.wolaidai.webot.cs.security.SafeDeserializationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.session.RedisSessionProperties;
import org.springframework.boot.autoconfigure.session.SessionProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.session.Session;
import org.springframework.session.SessionRepository;
import org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration;
import org.springframework.session.web.http.SessionRepositoryFilter;

import java.time.Duration;

@Configuration
@EnableConfigurationProperties({RedisSessionProperties.class})
public class RedisSessionConfig extends RedisHttpSessionConfiguration {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Bean
    @Override
    public <S extends Session> SessionRepositoryFilter<? extends Session> springSessionRepositoryFilter(SessionRepository<S> sessionRepository) {
        return super.springSessionRepositoryFilter(new SafeDeserializationRepository<>(sessionRepository, redisTemplate));
    }

    @Autowired
    public void customize(SessionProperties sessionProperties, RedisSessionProperties redisSessionProperties) {
        Duration timeout = sessionProperties.getTimeout();
        if (timeout != null) {
            this.setMaxInactiveIntervalInSeconds((int) timeout.getSeconds());
        }

        this.setRedisNamespace(redisSessionProperties.getNamespace());
        this.setFlushMode(redisSessionProperties.getFlushMode());
        this.setCleanupCron(redisSessionProperties.getCleanupCron());
    }
}
