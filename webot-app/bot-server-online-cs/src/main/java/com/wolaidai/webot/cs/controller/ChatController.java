package com.wolaidai.webot.cs.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.entity.Event;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.chat.*;
import com.wolaidai.webot.cs.service.ApiService;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import com.wolaidai.webot.cs.service.ServiceSummaryService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.data.elastic.repo.ComplexSessionListElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.*;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;
import com.wolaidai.webot.data.mysql.entity.face.FaceDetectionEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.repo.*;
import com.wolaidai.webot.data.mysql.repo.account.UserRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessTypeRepo;
import com.wolaidai.webot.data.mysql.repo.config.BusinessUnitRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import com.wolaidai.webot.security.domain.UserDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Api(tags = "聊天会话相关接口")
@RestController
@RequestMapping("/chat")
public class ChatController extends BaseController {

    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    private SessionListRepo sessionListRepo;
    @Autowired
    private QueueListRepo queueListRepo;
    @Autowired
    private ServiceSummaryRepo serviceSummaryRepo;
    @Autowired
    private BusinessUnitRepo businessUnitRepo;
    @Autowired
    private BusinessTypeRepo businessTypeRepo;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private SessionTransferListRepo sessionTransferListRepo;
    @Autowired
    private UserRepo userRepo;
    @Autowired
    private ComplexSessionListElasticRepo sessionListElasticRepo;
    @Autowired
    private ApiService apiService;
    @Autowired
    private BusinessTypeCollectRepo businessTypeCollectRepo;
    @Autowired
    private FaceDetectionRepo faceDetectionRepo;
    @Autowired
    private ConfigService configService;
    @Autowired
    private CustomerInfoEncService customerInfoEncService;
    @Autowired
    private ServiceSummaryService serviceSummaryService;

    @GetMapping("/summary")
    @ApiOperation(value = "聊天概要信息", response = ChatSummaryResultModel.class)
    public ResponseModel summary(ChatSummaryModel model) {
        UserDomain user = getUser();
        ChatSummaryResultModel chatSummaryResultModel = new ChatSummaryResultModel();
        if (null != user) {
            Integer orgId = user.getOrganizationId();
            Date today = DateUtils.truncate(new Date(), Calendar.DATE);
            chatSummaryResultModel.setEmail(user.getEmail());
            UserExtraField uef = getUserExtraField();
            if (null != uef) {
                chatSummaryResultModel.setName(uef.getNickName());
            }
            UserStateEntity u = userStateRepo.findByOrgIdAndEmailAndCreateTimeGreaterThanEqual(orgId, user.getEmail(), today);
            if (null != u) {
                //非管理员
                if(!Objects.equals(u.getUserType(),UserStateEntity.TYPE_MANAGER)) {
                    chatSummaryResultModel.setMaxReception(configService.getMaxReception(u.getOrgId(), u.getEmail()));
                }
            } 
            List<UserStateEntity> states = userStateRepo.findByOrgIdAndUserTypeAndCreateTimeGreaterThanEqual(orgId, UserStateEntity.TYPE_NORMAL, today);
            chatSummaryResultModel.setTotalQueueSize(states.size());
            chatSummaryResultModel.setCurrentQueueSize((int) states.stream().filter(i -> Objects.equals(i.getState(),UserStateEntity.STATE_ONLINE)).count());
            Object[] orderBy = new String[] { "customerLastReplyTime", "desc" };
            HashMap<String, String> sessionOrder = new HashMap<>();
            sessionOrder.put("waitSecond", "desc");//等待最久  空最后
            sessionOrder.put("createTimeAsc", "asc");//最早开始对话
            sessionOrder.put("createTimeDesc", "desc");//最晚开始对话
            sessionOrder.put("customerLastReplyTime", "desc");//客户最新消息
            if (null != model.getSessionSort() && sessionOrder.containsKey(model.getSessionSort())) {
                if ("createTimeAsc".equals(model.getSessionSort())) {
                    orderBy = new String[] { "id", sessionOrder.get(model.getSessionSort()) };
                } else if ("createTimeDesc".equals(model.getSessionSort())) {
                    orderBy = new String[] { "id", sessionOrder.get(model.getSessionSort()) };
                } else if ("waitSecond".equals(model.getSessionSort())) {
                    orderBy = new Object[] { "customerFirstReplyTime", "asc", new Date() };
                } else {
                    orderBy = new String[] { model.getSessionSort(), sessionOrder.get(model.getSessionSort()) };
                }
            }
            Page<SessionListEntity> page = sessionListRepo.find(orgId, null, new Object[][] { { "lastServiceUser", user.getEmail() } }, -1, 0, "createTime", DateUtils.addHours(new Date(), -24), null, new String[] { "mark", "desc" }, orderBy);
            HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
            for (SessionListEntity s : page) {
                SessionInfoModel sessionInfoModel = new SessionInfoModel(s);
                //处理用户敏感信息
                String content = CommonUtil.processUserSensitiveInfo(sessionInfoModel.getLastMsg(), false);
                sessionInfoModel.setLastMsg(content);
                if (Objects.equals(s.getStatus(), SessionListEntity.STATUS_ONLINE)) {
                    String csRespDetailKey = String.format(RedisKey.CS_RESP_DETAIL, s.getSessionKey());
                    sessionInfoModel.setHasFirstReplyTimeout(hashOperations.hasKey(csRespDetailKey,"firstReplyTimeout"));
                    sessionInfoModel.setHasSessionReplyTimeout(hashOperations.hasKey(csRespDetailKey,"sessionReplyTimeout"));
                    chatSummaryResultModel.getOnlineSessions().add(sessionInfoModel);
                } else if (Objects.equals(s.getStatus(), SessionListEntity.STATUS_OFFLINE) && null != s.getOfflineTime() && s.getOfflineTime().after(today)) {
                    chatSummaryResultModel.getOfflineSessions().add(sessionInfoModel);
                }
            }
            Comparator<SessionInfoModel> c = Comparator.comparing(i -> i.getMark(), Comparator.reverseOrder());
            chatSummaryResultModel.getOfflineSessions().sort(c.thenComparing(Comparator.comparing(i -> i.getOfflineTimeMs(), Comparator.reverseOrder())));
            String[] orderBy2 = null;
            if ("waitSecondDesc".equals(model.getQueueSort())) {
                orderBy2 = new String[] { "createTime", "asc" };
            } else if ("waitSecondAsc".equals(model.getQueueSort())) {
                orderBy2 = new String[] { "createTime", "desc" };
            }
            Page<QueueListEntity> page2 = queueListRepo.find(orgId, null, new Object[][] { { "status", QueueListEntity.INQUEUE_STATUS } }, -1, 0, null, null, null, orderBy2);
            for (QueueListEntity q : page2) {
                chatSummaryResultModel.getLines().add(new LineInfoModel(q));
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", chatSummaryResultModel);
    }

    @GetMapping("/sessionDetail/{sessionId}")
    @ApiOperation(value = "会话详情", response = SessionDetailModel.class)
    public ResponseModel sessionDetail(@PathVariable Integer sessionId) {
       return convertSessionDetail(sessionId, true);
    }

    @GetMapping("/userDetail/{sessionId}")
    @ApiOperation(value = "客户详情", response = SessionDetailModel.class)
    public ResponseModel userDetail(@PathVariable Integer sessionId) {
        return convertSessionDetail(sessionId, true);
    }

    /**
     * 转换会话详情
     * @param sessionId
     * @param mask
     * @return
     */
    private ResponseModel convertSessionDetail(Integer sessionId, boolean mask) {
        UserDomain user = getUser();
        SessionListEntity session = sessionListRepo.findByOrgIdAndId(user.getOrganizationId(), sessionId);
        if (null == session) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        JSONObject detail = session.getCustomerDetail();
        JSONArray customers = null != detail ? detail.getJSONArray("customers") : null;
        String phone = null != customers && customers.size() > 0 ? customers.getJSONObject(0).getString("mobile") : null;
        long count = 0;
        if (StringUtils.isBlank(phone)) {
            count = sessionListElasticRepo.countSessionCnt(user.getOrganizationId(), session.getClientId(), null);
        } else {
            count = sessionListElasticRepo.countSessionByMobileCnt(user.getOrganizationId(), phone, null);
        }
        //解密用户敏感数据
        customerInfoEncService.decryptCustomerInfo(customers);
        //需要脱敏
        if (mask && customers != null) {
            for (int i=0; i < customers.size(); i++) {
                JSONObject customer = customers.getJSONObject(i);
                if (customer.containsKey("mobile")) {
                    String mobile = customer.getString("mobile");
                    if (StringUtils.isNotBlank(mobile) && mobile.length() > 10) {
                        mobile = CommonUtil.maskPhoneNum(mobile);
                        customer.put("mobile", mobile);
                    }
                }
                if (customer.containsKey("cnid") && !customer.getString("cnid").contains("****")) {
                    String cnid = customer.getString("cnid");
                    if (StringUtils.isNotBlank(cnid) && cnid.length() > 17) {
                        cnid = CommonUtil.maskIdNum(cnid);
                        customer.put("cnid", cnid);
                    }
                }
            }
        }
        SessionDetailModel sessionDetailModel = new SessionDetailModel(session, count);
        List<FaceDetectionEntity> faceList = faceDetectionRepo.findBySessionId(sessionId);
        if (CollectionUtils.isNotEmpty(faceList)) {
            Set<Integer> faceResultSet = faceList.stream().collect(Collectors.mapping(FaceDetectionEntity::getCode, Collectors.toSet()));
            if (faceResultSet.contains(0)) {
                sessionDetailModel.setFaceDetectionResult(1);
            } else if (faceResultSet.stream().anyMatch(v -> v!=null && v>0)){
                sessionDetailModel.setFaceDetectionResult(2);
                Integer failTimes = faceDetectionRepo.getFailTimesBySessionId(sessionId);
                sessionDetailModel.setFailureTimes(failTimes);
            } 
        }
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        if(!Objects.equals(session.getStatus(),SessionListEntity.STATUS_OFFLINE)&&Objects.equals(session.getClientTypeId(),1)){
            if (hashOperations.hasKey(String.format(RedisKey.GLOBAL_CONVERSATION_KEY, session.getClientId(), session.getBotId()),"socketId")){
                sessionDetailModel.setClientStatus(1);
            } else {
                sessionDetailModel.setClientStatus(0);
            }
        }
        if (isRiskClient(session)) {
            sessionDetailModel.setRiskRegCustomer(true);
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", sessionDetailModel);
    }

    private boolean isRiskClient(SessionListEntity sessionList) {
        Date createTime = sessionList.getCreateTime();
        Date beginTime = DateUtils.truncate(DateUtils.addDays(createTime, -6), Calendar.DATE);
        Integer regCustomerCount = sessionListRepo.countRegCustomerByOrgIdAndClientIdAndTime(sessionList.getOrgId(), sessionList.getClientId(), beginTime, createTime);
        if(regCustomerCount>=2){
            return true;
        }
        return false;
    }

    @PostMapping("/serviceSummary/{sessionId}")
    @ApiOperation(value = "设置服务小结")
    public ResponseModel serviceSummary(@PathVariable Integer sessionId, @Valid @RequestBody ServiceSummaryModel model) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        String summaryKey = model.getSummaryKey();
        if (StringUtils.isBlank(summaryKey)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "请填写服务小结");
        }
        SessionListEntity session = sessionListRepo.findByOrgIdAndId(orgId, sessionId);
        if (null == session) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        ServiceSummaryEntity ss = serviceSummaryRepo.findByOrgIdAndSession(orgId, session);
        Date todayBeginTime = java.sql.Date.valueOf(LocalDate.now());
        if (session.getCreateTime().before(todayBeginTime)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "只能操作当天会话的服务小结");
        }
        Integer id = Integer.valueOf(summaryKey.substring(2));
        BusinessUnitEntity bu;
        BusinessTypeEntity bt = null;
        BusinessTypeCollectEntity typeCollect = null;
        if (summaryKey.startsWith("t_")) {
            bt = businessTypeRepo.findByIdAndOrgId(id, orgId);
            if (null == bt) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "业务类型不存在");
            }
            if (model.isCollect()) {
                typeCollect = businessTypeCollectRepo.findByOrgIdAndEmailAndBusinessTypeId(orgId, email, id);
            }
            bu = bt.getUnit();
        } else {
            bu = businessUnitRepo.findByIdAndOrgId(id, orgId);
            if (null == bu) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "业务不存在");
            }
            if (model.isCollect()) {
                typeCollect = businessTypeCollectRepo.findByOrgIdAndEmailAndBusinessUnitId(orgId, email, id);
            }
        }
        Date dateNow = new Date();
        //收藏且未收藏
        if (model.isCollect() && typeCollect == null) {
            typeCollect = new BusinessTypeCollectEntity();
            typeCollect.setOrgId(orgId);
            typeCollect.setEmail(email);
            typeCollect.setBusinessUnitId(bu.getId());
            if (bt != null) {
                typeCollect.setBusinessTypeId(bt.getId());
            }
            typeCollect.setPosition(0);
            typeCollect.setCreateTime(dateNow);
            typeCollect.setUpdateTime(dateNow);
            businessTypeCollectRepo.save(typeCollect);
        }

        if (null == ss) {
            ss = new ServiceSummaryEntity();
            ss.setCreateTime(dateNow);
            ss.setOrgId(orgId);
            ss.setSession(session);
            ss.setClientTypeId(session.getClientTypeId());
            ss.setBusinessId(session.getBusinessId());
        }
        ss.setRemark(model.getRemark());
        ss.setUnit(bu);
        ss.setType(bt);
        ss.setUpdateTime(dateNow);
        session.setServiceSummary(ss);
        session.setServiceSummaryStatus(1);
        session.setUpdateTime(dateNow);
        sessionListRepo.save(session);
        //处理小结敏感信息
        serviceSummaryService.processSummarySensitive(session, model.getRemark());
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功", new ServiceSummaryDetailModel(ss));
    }

    @DeleteMapping("/session/{sessionId}/close")
    @ApiOperation(value = "关闭会话", response = CloseSessionModel.class)
    public ResponseModel closeSession(@PathVariable Integer sessionId) {
        UserDomain user = getUser();
        SessionListEntity session = sessionListRepo.findByOrgIdAndId(user.getOrganizationId(), sessionId);
        if (null == session) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        if(!Objects.equals(session.getStatus(),SessionListEntity.STATUS_OFFLINE)) {
            redisTemplate.opsForList().rightPush(RedisKey.EVENT_SERVER_KEY, JSONObject.toJSONString(new Event(UUID.randomUUID().toString(), Event.CLOSE_SESSION_KEY, user.getOrganizationId(),
                    new JSONObject().fluentPut("sessionKey", session.getSessionKey()).fluentPut("closeType", SessionListEntity.CLOSE_TYPE_MANUAL), new Date())));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功", new CloseSessionModel(null != serviceSummaryRepo.findByOrgIdAndSession(user.getOrganizationId(), session)));
    }

    @PostMapping("/session/{sessionId}/mark/{status}")
    @ApiOperation(value = "标记会话")
    public ResponseModel markSession(@PathVariable Integer sessionId, @PathVariable Integer status) {
        UserDomain user = getUser();
        SessionListEntity session = sessionListRepo.findByOrgIdAndId(user.getOrganizationId(), sessionId);
        if (null == session) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话不存在");
        }
        if (null == status || (status != 0 && status != 1)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "标记状态不合法");
        }
        if (status.equals(session.getMark())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
        }
        session.setMark(status);
        session.setUpdateTime(new Date());
        sessionListRepo.save(session);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }

    @PostMapping("/session/{sessionId}/transfer")
    @ApiOperation(value = "转接会话")
    public ResponseModel transferSession(@PathVariable Integer sessionId, @Valid @RequestBody TransferSessionModel model) {
        UserDomain user = getUser();
        SessionListEntity session = sessionListRepo.findByOrgIdAndId(user.getOrganizationId(), sessionId);
        if (null == session) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "该会话不存在");
        }
        if(Objects.equals(session.getStatus(),SessionListEntity.STATUS_OFFLINE)){
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "该会话已关闭");
        }
        if(redisTemplate.opsForZSet().score(String.format(RedisKey.USER_ONLINE_LIST, user.getOrganizationId()),model.getEmail())==null){
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "该客服不在线，请转接其他客服");
        }

        RLock lock = redissonClient.getLock(RedisKey.LOCK_KEY+"transSessionId:"+sessionId);
        try {
            boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
            if (!res) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "正在转接中，请勿重复操作");
            }
            List<SessionTransferListEntity> sessionTransferList = session.getSessionTransferList();
            if(!sessionTransferList.isEmpty()&&sessionTransferList.get(0).getStatus() == SessionTransferListEntity.STATUS_START){
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "正在转接中,请稍候");
            }
            if(Objects.equals(session.getLastServiceUser(),model.getEmail())){
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "已转接给该客服");
            }
            if(!Objects.equals(session.getLastServiceUser(),user.getEmail())){
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "会话已转接给其他客服");
            }
            SessionTransferListEntity st = new SessionTransferListEntity();
            st.setFromServiceUser(user.getEmail());
            st.setToServiceUser(model.getEmail());
            st.setOrgId(user.getOrganizationId());
            st.setRemark(model.getRemark());
            st.setCreateTime(new Date());
            st.setSession(session);
            st.setUpdateTime(st.getCreateTime());
            sessionTransferListRepo.save(st);
            String fromName = null;
            UserExtraField userExtraField = getUserExtraField();
            if(userExtraField !=null){
                fromName = userExtraField.getNickName();
            }
            String toName = null;
            String toExtraField = userRepo.findExtraField(user.getOrganizationId(), appPropertyConfig.getProductId(), model.getEmail());
            if (StringUtils.isNotBlank(toExtraField)) {
                toName = new UserExtraField(toExtraField).getNickName();
            }
            redisTemplate.opsForList().rightPush(RedisKey.EVENT_SERVER_KEY, JSONObject.toJSONString(new Event(UUID.randomUUID().toString(),Event.TRANS_SESSION_KEY,user.getOrganizationId(),
                    new JSONObject().fluentPut("sessionKey",session.getSessionKey()).fluentPut("transId",st.getId()).fluentPut("from",user.getEmail()).fluentPut("fromName",fromName).fluentPut("to",model.getEmail()).fluentPut("toName",toName).fluentPut("remark",model.getRemark()),new Date())));
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
        }catch (Exception e) {
            LOGGER.error("assignServiceUser error",e);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "转接失败");
        }finally {
            if(lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    @PostMapping("/line/{lineId}/invite")
    @ApiOperation(value = "邀请队列")
    public ResponseModel inviteLine(@PathVariable Integer lineId) {
        UserDomain user = getUser();
        UserStateEntity userState = userStateRepo.findByOrgIdAndEmail(user.getOrganizationId(), user.getEmail());
        if(userState==null||Objects.equals(userState.getState(),UserStateEntity.STATE_LOGIN)||Objects.equals(userState.getState(),UserStateEntity.STATE_OFFLINE)){
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "当前客服状态无法进行邀请操作");
        }
        RLock lock = redissonClient.getLock(RedisKey.LOCK_KEY+"queueId:"+lineId);
        try {
            boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
            if (!res) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "该客户正在分配给其他客服，请稍后再试");
            }
            QueueListEntity queueListEntity = queueListRepo.findById(lineId).orElse(null);
            if (queueListEntity == null || !Objects.equals(queueListEntity.getOrgId(), user.getOrganizationId()) || !Objects.equals(queueListEntity.getStatus(), QueueListEntity.INQUEUE_STATUS)) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "排队信息不存在");
            }
            SessionListEntity sessionListEntity = new SessionListEntity();
            sessionListEntity.setBotId(queueListEntity.getBotId());
            sessionListEntity.setGcid(queueListEntity.getGcid());
            sessionListEntity.setGcTime(queueListEntity.getGcTime());
            sessionListEntity.setBusinessId(queueListEntity.getBusinessId());
            sessionListEntity.setBusinessName(queueListEntity.getBusinessName());
            sessionListEntity.setClientId(queueListEntity.getClientId());
            sessionListEntity.setClientTypeId(queueListEntity.getClientTypeId());
            sessionListEntity.setOrigin(queueListEntity.getOrigin());
            sessionListEntity.setServiceUser(user.getEmail());
            sessionListEntity.setLastServiceUser(user.getEmail());
            sessionListEntity.setCustomerDetail(queueListEntity.getCustomerDetail());
            sessionListEntity.setCustomerName(queueListEntity.getCustomerName());
            sessionListEntity.setCustomerType(queueListEntity.getCustomerType());
            sessionListEntity.setFromQueue(queueListEntity);
            sessionListEntity.setOrgId(user.getOrganizationId());
            sessionListEntity.setQueueTime(queueListEntity.getCreateTime());
            sessionListEntity.setSessionKey(UUID.randomUUID().toString());
            sessionListEntity.setStatus(SessionListEntity.STATUS_INIT);
            sessionListEntity.setAssignType(SessionListEntity.ASSIGN_TYPE_INVITE);
            sessionListEntity.setCreateTime(new Date());
            sessionListEntity.setUpdateTime(sessionListEntity.getUpdateTime());
            sessionListEntity.setWaitSecond((System.currentTimeMillis()-queueListEntity.getCreateTime().getTime())/1000);
            sessionListRepo.save(sessionListEntity);
            queueListEntity.setStatus(QueueListEntity.QUEUED_STATUS);
            queueListEntity.setUpdateTime(sessionListEntity.getCreateTime());
            queueListEntity.setWaitSecond(sessionListEntity.getWaitSecond());
            queueListRepo.save(queueListEntity);
            //清除排队缓存
            Stream.of(RedisKey.QUEUE_VIP, RedisKey.QUEUE_NORMAL).forEach(v -> {
                String key = String.format(v, queueListEntity.getOrgId());
                Set<String> queueDatas = redisTemplate.opsForZSet().range(key, 0, -1);
                for (String queueData : queueDatas) {
                    JSONObject queueJson = JSON.parseObject(queueData);
                    if(Objects.equals(queueListEntity.getId(),queueJson.getInteger("queueId"))) {
                        redisTemplate.opsForZSet().remove(key, queueData);
                        break;
                    }
                }
            });
            String currentReceptionKey = String.format(RedisKey.USER_CURRENT_RECEPTION, user.getOrganizationId());
            redisTemplate.opsForHash().increment(currentReceptionKey, user.getEmail(),1);
            redisTemplate.expireAt(currentReceptionKey,DateUtils.truncate(DateUtils.addDays(new Date(), 1), Calendar.DATE));
            String nickName = null;
            UserExtraField userExtraField = getUserExtraField();
            if(userExtraField !=null){
                nickName = userExtraField.getNickName();
            }
            redisTemplate.opsForList().rightPush(RedisKey.EVENT_SERVER_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.QUEUE_DONE_KEY, user.getOrganizationId(), new JSONObject().fluentPut("nickName", nickName).fluentPut("queueId", queueListEntity.getId()).fluentPut("clientId", queueListEntity.getClientId()).fluentPut("sessionKey", sessionListEntity.getSessionKey()), new Date())));
        } catch (Exception e) {
            LOGGER.error("inviteLine error",e);
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "操作失败");
        } finally {
            if(lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "操作成功");
    }
    
    @PostMapping("/userInfo")
    @ApiOperation(value = "绑定用户信息")
    public ResponseModel bindUserInfo(@Valid @RequestBody BindUserInfoModel model) {
        if (StringUtils.isBlank(model.getMobile())) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "手机号码不能为空");
        }
        SessionListEntity session = null;
        if (StringUtils.isBlank(model.getSessionKey()) || null == (session = sessionListRepo.findByOrgIdAndSessionKey(getUser().getOrganizationId(), model.getSessionKey()))) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "找不到会话");
        }
        JSONObject customerInfo = apiService.getCustomerInfoByMobile(model.getMobile());
        if (customerInfo != null && Objects.equals(customerInfo.getInteger("code"), 0)) {
            JSONArray result = customerInfo.getJSONArray("result");
            if (result != null && !result.isEmpty()) {
                String customerName = result.getJSONObject(0).getString("name");
                int customerType = "VIP".equals(result.getJSONObject(0).getString("isVip")) ? 1 : 0;
                if (StringUtils.isNotBlank(customerName)) {
                    session.setCustomerName(customerName);
                }
                //处理用户敏感信息
                JSONArray customers = result;
                customerInfoEncService.encryptCustomerInfo(customers);
                session.setCustomerType(customerType);
                session.getCustomerDetail().put("customers", customers);
                session.setUpdateTime(new Date());
                sessionListRepo.save(session);
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "绑定成功");
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "未查询到手机号码");
    }

    @GetMapping("/trans")
    @ApiOperation(value = "获取未接受转接记录", response = TransHistoryModel.class)
    public ResponseModel getTransHistory(TransHistoryModel model) {
        UserDomain user = getUser();
        List<SessionTransferListEntity> byToServiceUser
                = sessionTransferListRepo.findByToServiceUser(user.getEmail(), SessionTransferListEntity.STATUS_START);
        Set<String> emails = new HashSet<>();
        emails.add(user.getEmail());
        if (CollectionUtils.isNotEmpty(byToServiceUser)) {
            emails.addAll(byToServiceUser.stream().map(SessionTransferListEntity::getToServiceUser).collect(Collectors.toSet()));
        }
        Map<String, UserInfo> infoMap = userRepo.find(user.getOrganizationId(), appPropertyConfig.getProductId(), emails).stream().collect(Collectors.toMap(UserInfo::getEmail, Function.identity()));
        for (SessionTransferListEntity entity : byToServiceUser) {
            String transTimerKey = String.format(RedisKey.CS_SESSION_TRANS_KEY, entity.getId());
            ZSetOperations<String, String> zSetOperations = redisTemplate.opsForZSet();
            Double score = zSetOperations.score(RedisKey.EXPIRE_KEY_ZSET, transTimerKey);
            if (score != null && score > 0) {
                long expire = (score.longValue()-System.currentTimeMillis())/1000;
                if(expire>0) {
                    long remaining = expire > 60 ? 60 : expire;
                    //处理敏感数据解密
                    JSONObject detail = entity.getSession().getCustomerDetail();
                    JSONArray customers = null != detail ? detail.getJSONArray("customers") : null;
                    customerInfoEncService.decryptCustomerInfo(customers);
                    model.getRowList().add(new TransHistoryModel.Manual(new TransHistoryModel.TransHistoryRow(remaining, entity, infoMap)));
                }
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", model);
    }

    @PutMapping("/collect/{summaryKey}")
    @ApiOperation(value = "业务类型-收藏管理")
    public ResponseModel businessTypeCollectOp(@PathVariable String summaryKey, @RequestBody BusTypeCollectOpModel model) {
        Integer opType = model.getOpType();
        if (opType == null || !Arrays.asList(1,2).contains(opType)) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "操作类型不合法");
        }
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        Integer id = Integer.valueOf(summaryKey.substring(2));
        BusinessTypeCollectEntity typeCollect;
        //删除(取消收藏)
        if (opType == 1) {
            if (summaryKey.startsWith("t_")) {
                typeCollect = businessTypeCollectRepo.findByOrgIdAndEmailAndBusinessTypeId(orgId, email, id);
            } else {
                typeCollect = businessTypeCollectRepo.findByOrgIdAndEmailAndBusinessUnitId(orgId, email, id);
            }
            if (typeCollect != null) {
                businessTypeCollectRepo.delete(typeCollect);
            }
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "删除成功");
        } else { //排序
            List<String> summaryKeys = model.getSummaryKeys();
            if (CollectionUtils.isEmpty(summaryKeys)) {
                return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "排序对象为空");
            }
            for (int i=0; i < summaryKeys.size(); i++) {
                String key = summaryKeys.get(i);
                id = Integer.valueOf(key.substring(2));
                if (key.startsWith("t_")) {
                    businessTypeCollectRepo.updatePositionByTypeId(orgId, email, id, i);
                } else {
                    businessTypeCollectRepo.updatePositionByUnitId(orgId, email, id, i);
                }
            }
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "排序成功");
        }
    }

    @GetMapping("/collect")
    @ApiOperation(value = "业务类型-收藏查询", response = BusTypeCollectSearchModel.class)
    public ResponseModel businessTypeCollectSearch(BusTypeCollectSearchModel searchModel) {
        UserDomain user = getUser();
        Integer orgId = user.getOrganizationId();
        String email = user.getEmail();
        List<BusinessTypeCollectEntity> typeIdList = businessTypeCollectRepo.findByOrgIdAndEmailOrderByPositionAndId(orgId, email);
        if (CollectionUtils.isNotEmpty(typeIdList)) {
            List<BusinessTypeEntity> typeEntities = businessTypeRepo
                    .findByOrgIdAndIdIn(orgId, typeIdList.stream().map(BusinessTypeCollectEntity::getBusinessTypeId).collect(Collectors.toList()));
            Map<Integer, BusinessTypeEntity> typeEntityMap = typeEntities.stream().collect(Collectors.toMap(BusinessTypeEntity::getId, Function.identity()));
            List<BusinessUnitEntity> unitEntities = businessUnitRepo
                    .findByOrgIdAndIdIn(orgId, typeIdList.stream().map(BusinessTypeCollectEntity::getBusinessUnitId).collect(Collectors.toList()));
            Map<Integer, BusinessUnitEntity> unitEntityMap = unitEntities.stream().collect(Collectors.toMap(BusinessUnitEntity::getId, Function.identity()));
            for (BusinessTypeCollectEntity entity : typeIdList) {
                searchModel.getRowList().add(new BusTypeCollectSearchModel.BusinessTypeInfoRow(
                        entity.getBusinessUnitId() == null ? null : unitEntityMap.get(entity.getBusinessUnitId()),
                        entity.getBusinessTypeId() == null ? null : typeEntityMap.get(entity.getBusinessTypeId())));
            }
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", searchModel);
    }
}
