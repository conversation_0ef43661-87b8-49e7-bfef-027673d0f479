package com.wolaidai.webot.cs.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.entity.Event;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import com.wolaidai.webot.cs.service.QueueListService;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.repo.QueueListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionListRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class QueueListServiceImpl implements QueueListService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Autowired
    private SessionListRepo sessionListRepo;

    @Autowired
    private QueueListRepo queueListRepo;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private UserStateRepo userStateRepo;

    @Autowired
    private AppPropertyConfig appPropertyConfig;

    @Autowired
    private ConfigService configService;

    @Autowired
    private CustomerInfoEncService customerInfoEncService;

    @Override
    @Async
    public void checkQueueList() {
        String redisTaskKey = RedisKey.LOCK_KEY + "checkQueueList";
        RLock lock = redissonClient.getLock(redisTaskKey);
        try {
            boolean res = lock.tryLock(0, 30, TimeUnit.SECONDS);
            if (!res) {
                return;
            }
            LOGGER.info("start checkQueueList...");
            long start = System.currentTimeMillis();
            Stream.of(RedisKey.QUEUE_VIP, RedisKey.QUEUE_NORMAL).forEach(v -> {
                Set<String> keys = redisTemplate.keys(String.format(v, "*"));
                if (!keys.isEmpty()) {
                    keys.forEach(key -> {
                        int orgId = Integer.valueOf(key.split(":")[2]);
                        Long size = redisTemplate.opsForZSet().size(String.format(RedisKey.USER_ONLINE_LIST, orgId));
                        //无在线客服可分配
                        if (size == 0) {
                            if (!isWorkTime(orgId)) {
                                Set<String> queueDataSet = redisTemplate.opsForZSet().range(key, 0, -1);
                                for (String queueData : queueDataSet) {
                                    JSONObject queueJson = JSON.parseObject(queueData);
                                    int queueId = queueJson.getIntValue("queueId");
                                    QueueListEntity queueListEntity = queueListRepo.findById(Integer.valueOf(queueId)).orElse(null);
                                    if (queueListEntity != null) {
                                        queueListEntity.setStatus(QueueListEntity.QUEUE_TIMEOUT_STATUS);
                                        queueListEntity.setUpdateTime(new Date());
                                        queueListRepo.save(queueListEntity);
                                        redisTemplate.opsForList().rightPush(RedisKey.EVENT_SERVER_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.QUEUE_TIMEOUT_KEY, orgId, new JSONObject().fluentPut("queueId", queueListEntity.getId()).fluentPut("clientId", queueListEntity.getClientId()).fluentPut("botId", queueListEntity.getBotId()), new Date())));
                                    }
                                    redisTemplate.opsForZSet().remove(key, queueData);
                                }
                            }
                            return;
                        }
                        JSONObject serviceUserConfigJson = getServiceUserConfig(orgId);
                        assignServiceUser(key, orgId, serviceUserConfigJson);
                    });
                }
            });
            LOGGER.info("end checkQueueList,cost {} ms",System.currentTimeMillis()-start);
        }catch (Exception e) {
            LOGGER.error("execute checkQueueList fail",e);
        }finally{
            if(lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private int getFrequentBetweenMinutes(JSONObject serviceUserConfigJson) {
        boolean frequentFlag = getFrequentFlag(serviceUserConfigJson);
        if(frequentFlag){
            JSONObject frequentConfig = serviceUserConfigJson.getJSONObject("frequentConfig");
            if(frequentConfig!=null){
                if(frequentConfig.getIntValue("type")==1){
                    int hours = frequentConfig.getIntValue("hours");
                    int minutes = frequentConfig.getIntValue("minutes");
                    return hours*60+minutes;
                }
            }
        }
        return 0;
    }

    private boolean getFrequentFlag(JSONObject serviceUserConfigJson){
        return serviceUserConfigJson.getBooleanValue("frequentFlag");
    }

    private JSONObject getServiceUserConfig(int orgId) {
        String serviceUserConfig = configService.read(orgId, String.format(RedisKey.SERVICEUSER_CONFIG, orgId), null, CommonConfigEntity.TYPE_SERVICEUSER_CONFIG);
        JSONObject serviceUserConfigJson = new JSONObject();
        if(serviceUserConfig!=null){
            serviceUserConfigJson.putAll(JSON.parseObject(serviceUserConfig));
        }
        return serviceUserConfigJson;
    }

    private void assignServiceUser(String key, int orgId, JSONObject serviceUserConfigJson) {
        //是否开启熟客优先
        boolean frequentFlag = getFrequentFlag(serviceUserConfigJson);
        //熟客时间范围
        int frequentBetweenMinutes = getFrequentBetweenMinutes(serviceUserConfigJson);
        String queueData = null;
        while((queueData=redisTemplate.opsForZSet().range(key, 0, 0).stream().findFirst().orElse(null))!=null){
            JSONObject queueJson = JSON.parseObject(queueData);
            int queueId = queueJson.getIntValue("queueId");
            RLock lock = redissonClient.getLock(RedisKey.LOCK_KEY+"queueId:"+queueId);
            try {
                boolean res = lock.tryLock(0, 3, TimeUnit.SECONDS);
                if (!res) {
                    return;
                }
                QueueListEntity queueListEntity = queueListRepo.findById(Integer.valueOf(queueId)).orElse(null);
                if (queueListEntity == null || !Objects.equals(queueListEntity.getStatus(), QueueListEntity.INQUEUE_STATUS)) {
                    LOGGER.error("队列已分配,queueId:{},clientId:{}",queueId);
                    redisTemplate.opsForZSet().remove(key, queueData);
                    continue;
                }
                String onlineSessionClientId = sessionListRepo.findClientIdByOnlineStatusAndCreateTime(queueListEntity.getOrgId(), queueListEntity.getClientId(), DateUtils.truncate(new Date(), Calendar.DATE));
                if(onlineSessionClientId!=null){
                    LOGGER.error("会话已在线,queueId:{},clientId:{}",queueId,queueListEntity.getClientId());
                    queueListRepo.delete(queueListEntity);
                    redisTemplate.opsForZSet().remove(key, queueData);
                    continue;
                }
                String serviceEmail = null;
                if (frequentFlag) {
                    if (frequentBetweenMinutes > 0) {
                        serviceEmail = sessionListRepo.findLastServiceEmailByOrgIdAndClientIdAndCreateTime(orgId, queueListEntity.getClientId(), DateUtils.addMinutes(new Date(), -frequentBetweenMinutes));
                    } else {
                        serviceEmail = sessionListRepo.findLastServiceEmailByOrgIdAndClientId(orgId, queueListEntity.getClientId());
                    }
                }
                HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
                if (serviceEmail == null || redisTemplate.opsForZSet().score(String.format(RedisKey.USER_ONLINE_LIST, orgId), serviceEmail) == null || !checkReception(orgId, serviceEmail,hashOperations)) {
                    //重新分配客服
                    serviceEmail = null;
                    Set<String> serviceEmails = redisTemplate.opsForZSet().range(String.format(RedisKey.USER_ONLINE_LIST, orgId), 0, -1);
                    if(!CollectionUtils.isEmpty(serviceEmails)) {
                        Integer portionType = serviceUserConfigJson.getInteger("portionType");
                        if(portionType==null||portionType==1){ //按客服轮询分配
                            for (String email : serviceEmails) {
                                if (checkReception(orgId, email, hashOperations)) {
                                    if(!(isWhiteCustomer(queueListEntity)^isWhiteServiceEmail(email))){
                                        serviceEmail = email;
                                        break;
                                    }
                                }
                            }
                        }else if (Objects.equals(portionType,2)) { //按客服饱和度分配
                            Map<String,Double> validEmailMap = new HashMap<>();
                            serviceEmails = serviceEmails.stream().filter(email->{
                                        int currentReception = getCurrentReception(orgId, email, hashOperations);
                                        int maxReception = configService.getMaxReception(orgId, email);
                                        double receptionPercent =  BigDecimal.valueOf(currentReception).divide(BigDecimal.valueOf(maxReception),2,RoundingMode.HALF_UP).doubleValue();
                                        if(receptionPercent<1) {
                                            validEmailMap.put(email, receptionPercent);
                                            return true;
                                        }
                                        return false;
                                    }).sorted(Comparator.comparing(email->validEmailMap.get(email)))
                                    .collect(Collectors.toCollection(LinkedHashSet::new));
                            for (String email : serviceEmails) {
                                if(!(isWhiteCustomer(queueListEntity)^isWhiteServiceEmail(email))){
                                    serviceEmail = email;
                                    break;
                                }
                            }
                        }
                    }
                }

                if (serviceEmail != null) {
                    SessionListEntity sessionListEntity = new SessionListEntity();
                    sessionListEntity.setBotId(queueListEntity.getBotId());
                    sessionListEntity.setGcid(queueListEntity.getGcid());
                    sessionListEntity.setGcTime(queueListEntity.getGcTime());
                    sessionListEntity.setBusinessId(queueListEntity.getBusinessId());
                    sessionListEntity.setBusinessName(queueListEntity.getBusinessName());
                    sessionListEntity.setClientId(queueListEntity.getClientId());
                    sessionListEntity.setClientTypeId(queueListEntity.getClientTypeId());
                    sessionListEntity.setOrigin(queueListEntity.getOrigin());
                    sessionListEntity.setServiceUser(serviceEmail);
                    sessionListEntity.setLastServiceUser(serviceEmail);
                    sessionListEntity.setCustomerDetail(queueListEntity.getCustomerDetail());
                    sessionListEntity.setCustomerName(queueListEntity.getCustomerName());
                    sessionListEntity.setCustomerType(queueListEntity.getCustomerType());
                    sessionListEntity.setFromQueue(queueListEntity);
                    sessionListEntity.setOrgId(orgId);
                    sessionListEntity.setQueueTime(queueListEntity.getCreateTime());
                    sessionListEntity.setSessionKey(UUID.randomUUID().toString());
                    sessionListEntity.setStatus(SessionListEntity.STATUS_INIT);
                    sessionListEntity.setAssignType(SessionListEntity.ASSIGN_TYPE_AUTO);
                    sessionListEntity.setCreateTime(new Date());
                    sessionListEntity.setUpdateTime(sessionListEntity.getCreateTime());
                    sessionListEntity.setWaitSecond((System.currentTimeMillis()-queueListEntity.getCreateTime().getTime())/1000);
                    sessionListRepo.save(sessionListEntity);
                    queueListEntity.setStatus(QueueListEntity.QUEUED_STATUS);
                    queueListEntity.setUpdateTime(sessionListEntity.getCreateTime());
                    queueListEntity.setWaitSecond(sessionListEntity.getWaitSecond());
                    queueListRepo.save(queueListEntity);
                    String currentReceptionKey = String.format(RedisKey.USER_CURRENT_RECEPTION, orgId);
                    redisTemplate.opsForHash().increment(currentReceptionKey, serviceEmail,1);
                    redisTemplate.expireAt(currentReceptionKey,DateUtils.truncate(DateUtils.addDays(new Date(), 1), Calendar.DATE));
                    String nickName = null;
                    UserStateEntity userStateEntity = userStateRepo.findByOrgIdAndEmail(orgId, serviceEmail);
                    if(userStateEntity!=null){
                        nickName = userStateEntity.getNickName();
                    }
                    redisTemplate.opsForList().rightPush(RedisKey.EVENT_SERVER_KEY, JSON.toJSONString(new Event(UUID.randomUUID().toString(), Event.QUEUE_DONE_KEY, orgId, new JSONObject().fluentPut("nickName", nickName).fluentPut("queueId", queueListEntity.getId()).fluentPut("clientId", queueListEntity.getClientId()).fluentPut("sessionKey", sessionListEntity.getSessionKey()).fluentPut("botId", queueListEntity.getBotId()), new Date())));
                    redisTemplate.opsForZSet().remove(key, queueData);
                    redisTemplate.opsForZSet().add(String.format(RedisKey.USER_ONLINE_LIST, orgId), serviceEmail, System.currentTimeMillis());
                } else {
                    break;
                }
            }catch (Exception e) {
                LOGGER.error("assignServiceUser error",e);
            }finally {
                if(lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    private int getCurrentReception(Integer orgId,String email,HashOperations<String, String, String> hashOperations){
        return NumberUtils.toInt(hashOperations.get(String.format(RedisKey.USER_CURRENT_RECEPTION, orgId), email));
    }

    private boolean checkReception(Integer orgId,String email,HashOperations<String, String, String> hashOperations){
        int currentReception = NumberUtils.toInt(hashOperations.get(String.format(RedisKey.USER_CURRENT_RECEPTION, orgId), email));
        if(currentReception<configService.getMaxReception(orgId, email)){
            return true;
        }
        return false;
    }

    public boolean isWorkTime(int orgId){
        Date now = new Date();
        String config = configService.read(orgId, String.format(RedisKey.CS_WORKTIME_CONFIG, orgId), null, CommonConfigEntity.TYPE_WORKTIME_CONFIG);
        if (StringUtils.isNotBlank(config)) {
            String dateFormat = "yyyy/MM/dd HH:mm";
            JSONObject configJson = JSON.parseObject(config);
            JSONArray specificArray = configJson.getJSONArray("specific");
            if(!CollectionUtils.isEmpty(specificArray)){
                for (int i = 0; i < specificArray.size(); i++) {
                    JSONObject specificJson = specificArray.getJSONObject(i);
                    Date startTime = DateUtil.parseDate(specificJson.getString("startTime"),dateFormat);
                    Date endTime = DateUtil.parseDate(specificJson.getString("endTime"),dateFormat);
                    if(startTime!=null&&endTime!=null&&now.getTime()>=startTime.getTime()&&now.getTime()<endTime.getTime()){
                        return true;
                    }
                }
            }
            JSONArray holidaysArray = configJson.getJSONArray("holidays");
            if(!CollectionUtils.isEmpty(holidaysArray)){
                for (int i = 0; i < holidaysArray.size(); i++) {
                    JSONObject holidayJson = holidaysArray.getJSONObject(i);
                    Date startTime = DateUtil.parseDate(holidayJson.getString("startTime"),dateFormat);
                    Date endTime = DateUtil.parseDate(holidayJson.getString("endTime"),dateFormat);
                    if(startTime!=null&&endTime!=null&&now.getTime()>=startTime.getTime()&&now.getTime()<endTime.getTime()){
                        return false;
                    }
                }
            }
            JSONArray workdaysArray = configJson.getJSONArray("workdays");
            if(!CollectionUtils.isEmpty(workdaysArray)){
                for (int i = 0; i < workdaysArray.size(); i++) {
                    JSONObject workdaysJson = workdaysArray.getJSONObject(i);
                    JSONArray days = workdaysJson.getJSONArray("days");
                    for (int j = 0; j < days.size(); j++) {
                        int day = days.getIntValue(j);
                        day = (day==7?0:day);
                        if(day==now.getDay()){
                            String startTime = workdaysJson.getString("startTime");
                            String endTime = workdaysJson.getString("endTime");
                            if(startTime!=null&&endTime!=null){
                                String time = DateFormatUtils.format(now, "HH:mm");
                                if(time.compareTo(startTime)>=0&&time.compareTo(endTime)<0){
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    private String getCustomerMobile(JSONObject customerDetail){
        if(customerDetail!=null) {
            JSONArray customers = customerDetail.getJSONArray("customers");
            if (!CollectionUtils.isEmpty(customers)) {
                return customers.getJSONObject(0).getString("mobile");
            }
        }
        return null;
    }

    private boolean isWhiteCustomer(QueueListEntity queueListEntity){
        String customerMobile = getCustomerMobile(queueListEntity.getCustomerDetail());
        String decMobile = customerInfoEncService.decryptMobile(customerMobile);
        return StringUtils.isNotBlank(appPropertyConfig.getWhiteMobile())&&appPropertyConfig.getWhiteMobile().equals(decMobile);
    }

    private boolean isWhiteServiceEmail(String serviceEmail){
        return StringUtils.isNotBlank(appPropertyConfig.getWhiteEmail())&&appPropertyConfig.getWhiteEmail().equalsIgnoreCase(serviceEmail);
    }
}
