package com.wolaidai.webot.cs.service.impl;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.model.report.SessionModel;
import com.wolaidai.webot.cs.model.report.SessionRowModel;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import com.wolaidai.webot.cs.service.SessionDetailReportService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.elastic.entity.HistoryManualEntity;
import com.wolaidai.webot.data.elastic.repo.ComplexChatHistoryElasticRepo;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.report.SessionDetailReportEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.repo.QueueListRepo;
import com.wolaidai.webot.data.mysql.repo.SessionDetailReportRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;

@Service
public class SessionDetailReportServiceImpl implements SessionDetailReportService {

    final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ComplexChatHistoryElasticRepo chatHistoryElasticRepo;;
    @Autowired
    private SessionDetailReportRepo statisticsSessionRepo;
    @Autowired
    private QueueListRepo queueListRepo;
    @Autowired
    private AppPropertyConfig config;
    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    protected AppPropertyConfig appPropertyConfig;
    @Autowired
    private CustomerInfoEncService customerInfoEncService;

    @Override
    public List<SessionDetailReportEntity> generateSession(Integer orgId, Set<String> emails, Date startTime, Date endTime) {
        HashMap<String, UserInfo> infoMap = new HashMap<>();
        ReportUtil.getUserInfoList(orgId, config.getProductId(), infoMap, emails);
        ArrayList<SessionDetailReportEntity> sses = new ArrayList<>();
        for (Date[] arr : DateUtil.splitDateByDay(startTime, endTime)) {
            List<QueueListEntity> giveUp = null;
            if (null == orgId) {
                giveUp = queueListRepo.findByStatusAndCreateTimeGreaterThanEqualAndCreateTimeLessThan(QueueListEntity.DEQUEUE_STATUS, arr[0], arr[1]);
            } else {
                giveUp = queueListRepo.findByOrgIdAndStatusAndCreateTimeGreaterThanEqualAndCreateTimeLessThan(orgId, QueueListEntity.DEQUEUE_STATUS, arr[0], arr[1]);
            }
            if (null != giveUp && giveUp.size() > 0) {
                for (QueueListEntity q : giveUp) {
                    SessionDetailReportEntity s = new SessionDetailReportEntity();
                    s.setOrgId(q.getOrgId());
                    s.setDataTime(arr[0]);
                    s.setCustomerName(q.getCustomerName());
                    JSONObject detail = q.getCustomerDetail();
                    JSONArray customers = null != detail ? detail.getJSONArray("customers") : null;
                    JSONObject json = null != customers && customers.size() > 0 ? customers.getJSONObject(0) : null;
                    if (null != json) {
                        String decMobile = customerInfoEncService.decryptMobile(json.getString("mobile"));
                        s.setCustomerPhone(decMobile);
                        s.setUuid(json.getString("uuid"));
                        s.setUserid(json.getInteger("userId"));
                    }
                    s.setClientTypeId(q.getClientTypeId());
                    s.setBusinessId(q.getBusinessId());
                    s.setBusinessName(q.getBusinessName());
                    s.setQueueTime(q.getCreateTime());
                    s.setWaitSeconds(q.getWaitSecond());
                    s.setHandle(0);
                    s.setCreateTime(new Date());
                    sses.add(s);
                }
            }
            List<SessionListEntity> sessions = ReportUtil.getSessionList(true, orgId, emails, arr[0], arr[1]);
            if (null == emails || emails.size() == 0) {
                Set<String> lastEmails = sessions.stream().map(i -> i.getLastServiceUser()).collect(Collectors.toSet());
                if (null != lastEmails && lastEmails.size() > 0) {
                    ReportUtil.getUserInfoList(orgId, config.getProductId(), infoMap, lastEmails);
                }
            }
            Map<String, HistoryManualEntity> historyMap = chatHistoryElasticRepo.findManualHistories(orgId, true, arr[0], arr[1]);
            HashMap<String, SessionDetailReportEntity> emptyInfoMap = new HashMap<>();
            for (SessionListEntity sl : sessions) {
                SessionDetailReportEntity s = new SessionDetailReportEntity();
                s.setOrgId(sl.getOrgId());
                s.setDataTime(arr[0]);
                s.setSessionId(sl.getId());
                s.setCustomerName(sl.getCustomerName());
                JSONObject detail = sl.getCustomerDetail();
                JSONArray customers = null != detail ? detail.getJSONArray("customers") : null;
                JSONObject json = null != customers && customers.size() > 0 ? customers.getJSONObject(0) : null;
                if (null != json) {
                    String decMobile = customerInfoEncService.decryptMobile(json.getString("mobile"));
                    s.setCustomerPhone(decMobile);
                    s.setUuid(json.getString("uuid"));
                    s.setUserid(json.getInteger("userId"));
                }
                s.setClientTypeId(sl.getClientTypeId());
                s.setBusinessId(sl.getBusinessId());
                s.setBusinessName(sl.getBusinessName());
                s.setQueueTime(sl.getQueueTime());
                s.setWaitSeconds(sl.getWaitSecond());
                s.setHandle(1);
                s.setEmail(sl.getLastServiceUser());
                s.setBeginTime(sl.getCreateTime());
                s.setEndTime(sl.getOfflineTime());
                s.setDurationSeconds(sl.getDurationSecond());
                UserInfo info = infoMap.get(sl.getOrgId() + sl.getLastServiceUser());
                UserExtraField uef = null;
                if (null != info && null != (uef = info.getUserExtraField())) {
                    s.setNickName(uef.getNickName());
                    s.setWorkNumber(uef.getWorkNumber());
                } else {
                    emptyInfoMap.put(sl.getLastServiceUser(), s);
                }
                HistoryManualEntity history = historyMap.get(sl.getSessionKey());
                if (null != history) {
                    s.setFirstResponse(history.getFirstResponseTime());
                    if (null != history.getAvgResponseTime()) {
                        s.setResponseAvg((int) history.getAvgResponseTime().floatValue());
                    }
                }
                s.setCreateTime(new Date());
                sses.add(s);
            }
            if (emptyInfoMap.size() > 0) {
                List<UserStateEntity> list = userStateRepo.findByOrgIdAndEmailInAndCreateTimeGreaterThanEqual(orgId, emptyInfoMap.keySet(), arr[0]);
                for (UserStateEntity s : list) {
                    SessionDetailReportEntity ss = emptyInfoMap.get(s.getEmail());
                    ss.setWorkNumber(s.getWorkNumber());
                    ss.setNickName(s.getNickName());
                }
            }
        }
        return sses;
    }

    @Override
    public void generateReportByDate(Integer orgId, Date startTime, Date endTime) {
        if (null != orgId) {
            statisticsSessionRepo.deleteByOrgIdAndDataTime(orgId, startTime, endTime);
        } else {
            statisticsSessionRepo.deleteByDataTime(startTime, endTime);
        }
        List<SessionDetailReportEntity> sses = generateSession(orgId, null, startTime, endTime);
        if (sses.size() > 0) {
            statisticsSessionRepo.saveAll(sses);
        }
    }

    @Override
    public String getReportName() {
        return "会话明细统计";
    }

    @Override
    public void loadData(Integer orgId, Object m) {
        SessionModel model = (SessionModel) m;
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);
        List<SessionDetailReportEntity> result = new ArrayList<>();
        boolean flag = false;
        if (today.getTime() >= model.getBeginDate().getTime() && today.before(model.getEndDate())) {
            result = generateSession(orgId, model.getEmails(), today, new Date());
            flag = result.size() > 0;
        }
        if (model.getBeginDate().before(today)) {
            Date endDate = model.getEndDate().after(today) ? DateUtils.addMilliseconds(today, -1) : model.getEndDate();
            Pageable page = model.getPageNumber() == -1 ? Pageable.unpaged() : PageRequest.of(model.getPageNumber(), model.getPageSize());
            Page<SessionDetailReportEntity> p = null;
            if (model.getEmails().size() > 0) {
                p = statisticsSessionRepo.findByOrgIdAndEmailInAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByQueueTimeDesc(orgId, model.getEmails(), model.getBeginDate(), endDate, page);
            } else {
                p = statisticsSessionRepo.findByOrgIdAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByQueueTimeDesc(orgId, model.getBeginDate(), endDate, page);
            }
            model.setTotal(p.getTotalElements());
            result.addAll(p.getContent());
        }
        if (flag && model.getPageNumber() > -1) {
            model.setTotal(result.size());
            result.sort(Comparator.comparing(SessionDetailReportEntity::getQueueTime, Comparator.reverseOrder()));
            if (result.size() > model.getPageSize()) {
                int from = model.getPageNumber() * model.getPageSize();
                result = result.subList(from, Math.min(from + model.getPageSize(), result.size()));
            }
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (SessionDetailReportEntity s : result) {
            model.getList().add(new SessionRowModel(s, sdf));
        }
    }

    @Override
    public boolean run(TaskEntity task) throws IOException {
        SessionModel model = JSON.toJavaObject(task.getExtraParams(), SessionModel.class);
        model.setPageNumber(-1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = String.format("会话明细_%s_%s.xlsx", sdf.format(model.getBeginDate()), sdf.format(model.getEndDate()));
        task.setName(fileName);
        CommonUtil.saveTaskProgress(task, 0);
        loadData(task.getOrgId(), model);
        CommonUtil.saveTaskProgress(task, 75);
        Map<String, List<SessionRowModel>> map = model.getList().stream().collect(Collectors.groupingBy(i -> i.getDate()));
        Map<String, JSONArray> map2 = new TreeMap<>(Comparator.comparing(i -> i));
        for (Map.Entry<String, List<SessionRowModel>> entry : map.entrySet()) {
            map2.put(entry.getKey(), (JSONArray) JSON.toJSON(entry.getValue()));
        }
        LinkedHashMap<String, String> headerMap = new LinkedHashMap<>();
        headerMap.put("date", "日期");
        headerMap.put("customerName", "用户名");
        headerMap.put("uuid", "uuid");
        headerMap.put("userid", "userid");
        headerMap.put("customerPhone", "手机号");
        headerMap.put("clientTypeName", "渠道");
        headerMap.put("businessName", "业务类型");
        headerMap.put("queueTime", "聊天请求开始时间");
        headerMap.put("waitSeconds", "请求等待时长");
        headerMap.put("handleStr", "接待状态");
        headerMap.put("workNumber", "客服工号");
        headerMap.put("nickName", "客服昵称");
        headerMap.put("beginTime", "聊天开始时间");
        headerMap.put("endTime", "聊天结束时间");
        headerMap.put("durationSeconds", "聊天总时长");
        headerMap.put("firstResponse", "首次响应时长");
        headerMap.put("responseAvg", "平均响应时长");
        XSSFWorkbook sheets = ReportUtil.convertToExcel(map2, headerMap);
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(sheets, appPropertyConfig.getOssBucketName(), task);
        return true;
    }
}
