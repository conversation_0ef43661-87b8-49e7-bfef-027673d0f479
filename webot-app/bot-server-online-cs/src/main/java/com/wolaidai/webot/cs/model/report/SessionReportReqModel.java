package com.wolaidai.webot.cs.model.report;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "会话统计请求数据")
public class SessionReportReqModel extends BaseModel {

    @ApiModelProperty(required = true, value = "开始时间")
    private Date startTime;

    @ApiModelProperty(required = true, value = "结束时间")
    private Date endTime;

    @ApiModelProperty(required = true, value = "类型，1-按日统计，2-按周期统计，3-按半小时段统计")
    private Integer type;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
