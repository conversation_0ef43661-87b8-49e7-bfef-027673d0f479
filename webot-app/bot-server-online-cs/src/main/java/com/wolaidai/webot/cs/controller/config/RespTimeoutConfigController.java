package com.wolaidai.webot.cs.controller.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.service.ConfigService;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.enums.AuditAction;
import com.wolaidai.webot.data.mysql.enums.AuditModule;
import com.wolaidai.webot.data.redis.constant.RedisKey;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "响应超时设置")
@RestController
@RequestMapping("/respTimeout/config")
public class RespTimeoutConfigController extends BaseController {
    @Autowired
    private ConfigService configService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @GetMapping
    @ApiOperation(value = "查询响应超时设置")
    public ResponseModel getRespTimeoutConfig() {
        Integer orgId = getUser().getOrganizationId();
        JSONObject data = configService.readData(true, orgId, String.format(RedisKey.RESP_TIMEOUT_CONFIG, orgId), CommonConfigEntity.TYPE_RESP_TIMEOUT_CONFIG);
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", data);
    }

    @PostMapping
    @ApiOperation(value = "更新响应超时设置")
    public ResponseModel updateRespTimeoutConfig(@RequestBody JSONObject config) {
        Integer orgId = getUser().getOrganizationId();
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        config.keySet().forEach(k -> {
            hashOperations.delete(String.format(RedisKey.RESP_TIMEOUT_CONFIG, orgId), k);
            configService.save(orgId, config.getString(k), CommonConfigEntity.TYPE_RESP_TIMEOUT_CONFIG, k);
        });
        auditLog(AuditAction.UPDATE, AuditModule.RESP_TIMEOUT_CONFIG, null, "更新响应超时设置");
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "保存成功");
    }
}
