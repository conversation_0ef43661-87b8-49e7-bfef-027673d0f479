package com.wolaidai.webot.cs.service;

import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.data.mysql.entity.config.CommonConfigEntity;
import com.wolaidai.webot.data.mysql.repo.CommonConfigRepo;
import com.wolaidai.webot.data.redis.constant.RedisKey;

@Service
public class ConfigService {
    private final long TIMEOUT_MINUTES = 3;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private CommonConfigRepo commonConfigRepo;

    public String read(Integer orgId, String key, String hashKey, String... type) {
        return read(false, orgId, key, hashKey, type);
    }

    public String read(boolean skipRedis, Integer orgId, String key, String hashKey, String... type) {
        Object value = null;
        if (!skipRedis) {
            if (StringUtils.isNotBlank(hashKey)) {
                value = redisTemplate.opsForHash().get(key, hashKey);
            } else {
                value = redisTemplate.opsForValue().get(key);
            }
        }
        int len = 0;
        if (null == value && null != type && (len = type.length) > 0) {
            CommonConfigEntity conf = null;
            if (len > 2 && StringUtils.isNotBlank(type[2])) {
                conf = commonConfigRepo.findOneByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], type[2]);
            } else if (len > 1 && StringUtils.isNotBlank(type[1])) {
                conf = commonConfigRepo.findOneByOrgIdAndTypeAndSubType1(orgId, type[0], type[1]);
            } else if (StringUtils.isNotBlank(type[0])) {
                conf = commonConfigRepo.findOneByOrgIdAndType(orgId, type[0]);
            }
            if (null != conf) {
                String content = conf.getContent();
                if (null == content) {
                    content = "";
                }
                if (!skipRedis) {
                    if (StringUtils.isNotBlank(hashKey)) {
                        redisTemplate.opsForHash().put(key, hashKey, content);
                        redisTemplate.expire(key, Duration.ofMinutes(TIMEOUT_MINUTES));
                    } else {
                        redisTemplate.opsForValue().set(key, content, Duration.ofMinutes(TIMEOUT_MINUTES));
                    }
                }
                return content;
            }
        }
        return null == value ? null : value.toString();
    }

    public JSONObject readData(Integer orgId, String key, String... type) {
        return readData(false, orgId, key, type);
    }

    public JSONObject readData(boolean skipRedis, Integer orgId, String key, String... type) {
        HashOperations<String, String, Object> hash = redisTemplate.opsForHash();
        if (!skipRedis) {
            Map<String, Object> map = hash.entries(key);
            if (null != map && map.size() > 0) {
                return new JSONObject(map);
            }
        }
        JSONObject json = new JSONObject();
        int len = 0;
        if (null != type && (len = type.length) > 0) {
            List<CommonConfigEntity> list = null;
            if (len > 2 && StringUtils.isNotBlank(type[2])) {
                list = commonConfigRepo.findByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], type[2]);
            } else if (len > 1 && StringUtils.isNotBlank(type[1])) {
                list = commonConfigRepo.findByOrgIdAndTypeAndSubType1(orgId, type[0], type[1]);
            } else if (StringUtils.isNotBlank(type[0])) {
                list = commonConfigRepo.findByOrgIdAndType(orgId, type[0]);
            }
            if (null != list && list.size() > 0) {
                for (CommonConfigEntity conf : list) {
                    String subType1 = conf.getSubType1();
                    String subType2 = conf.getSubType2();
                    String content = conf.getContent();
                    if (null == content) {
                        content = "";
                    }
                    if (StringUtils.isNotBlank(subType2)) {
                        Object o = json.get(subType1);
                        if (null != o && o instanceof JSONObject) {
                            ((JSONObject) o).put(subType2, content);
                        } else {
                            JSONObject item = new JSONObject();
                            item.put(subType2, content);
                            o = item;
                        }
                        json.put(subType1, o);
                        if (!skipRedis) {
                            hash.put(key, subType2, content);
                        }
                    } else {
                        json.put(subType1, content);
                        if (!skipRedis) {
                            hash.put(key, subType1, content);
                        }
                    }
                }
                if (!skipRedis) {
                    redisTemplate.expire(key, Duration.ofMinutes(TIMEOUT_MINUTES));
                }
            }
        }
        if (len > 1 && StringUtils.isNotBlank(type[1])) {
            return json.getJSONObject(type[1]);
        }
        return json;
    }

    public boolean save(Integer orgId, String content, String... type) {
        int len = 0;
        if (null == type || (len = type.length) < 1) {
            return false;
        }
        CommonConfigEntity conf = null;
        String subType1 = null, subType2 = null;
        if (len > 2 && StringUtils.isNotBlank(type[2])) {
            conf = commonConfigRepo.findOneByOrgIdAndTypeAndSubType1AndSubType2(orgId, type[0], type[1], type[2]);
            subType1 = type[1];
            subType2 = type[2];
        } else if (len > 1 && StringUtils.isNotBlank(type[1])) {
            conf = commonConfigRepo.findOneByOrgIdAndTypeAndSubType1(orgId, type[0], type[1]);
            subType1 = type[1];
        } else {
            conf = commonConfigRepo.findOneByOrgIdAndType(orgId, type[0]);
        }
        Date now = new Date();
        if (null == conf || null == conf.getOrgId() || conf.getOrgId() == -1) {
            conf = new CommonConfigEntity();
            conf.setOrgId(orgId);
            conf.setType(type[0]);
            conf.setSubType1(subType1);
            conf.setSubType2(subType2);
            conf.setCreateTime(now);
        }
        conf.setUpdateTime(now);
        conf.setContent(content);
        commonConfigRepo.save(conf);
        return true;
    }

    public int getMaxReception(Integer orgId, String email) {
        String maxReception = read(orgId, String.format(RedisKey.USER_MAX_RECEPTION, orgId), email, CommonConfigEntity.TYPE_MAX_RECEPTION, email);
        if (maxReception == null) {
            maxReception = read(orgId, String.format(RedisKey.USER_DEFAULT_RECEPTION, orgId), null, CommonConfigEntity.TYPE_DEFAULT_RECEPTION);
        }
        return NumberUtils.toInt(maxReception, 10);
    }

}
