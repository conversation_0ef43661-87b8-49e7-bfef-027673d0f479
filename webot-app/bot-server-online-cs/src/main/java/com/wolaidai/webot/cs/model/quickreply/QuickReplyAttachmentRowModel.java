package com.wolaidai.webot.cs.model.quickreply;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.chat.QuickReplyEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ReplyCategoryEntity;

import java.util.Date;

public class QuickReplyAttachmentRowModel extends BaseModel {

    private Integer id;
    private String fileName;
    private String fileType;
    private String fileUrl;
    private String remark;
    private Integer categoryId;
    private String categoryName;
    private String creator;
    private Date createTime;
    private Date updateTime;

    public QuickReplyAttachmentRowModel(QuickReplyEntity replyEntity) {
        this.id = replyEntity.getId();
        this.fileName = replyEntity.getFileName();
        this.fileType = replyEntity.getFileType();
        this.fileUrl = replyEntity.getFileUrl();
        this.remark = replyEntity.getRemark();
        ReplyCategoryEntity category = replyEntity.getCategory();
        if (category != null) {
            this.categoryId = category.getId();
            this.categoryName = category.fullPath();
        }
        this.creator = replyEntity.getCreator();
        this.createTime = replyEntity.getCreateTime();
        this.updateTime = replyEntity.getUpdateTime();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
