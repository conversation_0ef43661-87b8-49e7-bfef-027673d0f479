package com.wolaidai.webot.cs.model.chat;

import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionTransferListEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TransHistoryModel extends BaseModel {

    private List<Manual> rowList = new ArrayList<>();

    public List<Manual> getRowList() {
        return rowList;
    }

    public void setRowList(List<Manual> rowList) {
        this.rowList = rowList;
    }

    public static class Manual {

        private TransHistoryRow manual;

        public TransHistoryRow getManual() {
            return manual;
        }

        public void setManual(TransHistoryRow manual) {
            this.manual = manual;
        }

        public Manual(TransHistoryRow transHistoryRow) {
            this.manual = transHistoryRow;
        }

        public Manual() {
        }
    }

    public static class TransHistoryRow {
        private Integer transId;
        private String sessionKey;
        private String businessName;
        private String customerName;
        private JSONObject customerDetail;
        private String from;
        private String fromName;
        private String to;
        private String toName;
        private String remark;
        private long remaining;

        public Integer getTransId() {
            return transId;
        }

        public void setTransId(Integer transId) {
            this.transId = transId;
        }

        public String getSessionKey() {
            return sessionKey;
        }

        public void setSessionKey(String sessionKey) {
            this.sessionKey = sessionKey;
        }

        public String getBusinessName() {
            return businessName;
        }

        public void setBusinessName(String businessName) {
            this.businessName = businessName;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public JSONObject getCustomerDetail() {
            return customerDetail;
        }

        public void setCustomerDetail(JSONObject customerDetail) {
            this.customerDetail = customerDetail;
        }

        public String getFrom() {
            return from;
        }

        public void setFrom(String from) {
            this.from = from;
        }

        public String getFromName() {
            return fromName;
        }

        public void setFromName(String fromName) {
            this.fromName = fromName;
        }

        public String getTo() {
            return to;
        }

        public void setTo(String to) {
            this.to = to;
        }

        public String getToName() {
            return toName;
        }

        public void setToName(String toName) {
            this.toName = toName;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public long getRemaining() {
            return remaining;
        }

        public void setRemaining(long remaining) {
            this.remaining = remaining;
        }

        public TransHistoryRow() {
        }

        public TransHistoryRow(Long remaining, SessionTransferListEntity entity, Map<String, UserInfo> infoMap) {

            this.transId = entity.getId();
            SessionListEntity session = entity.getSession();
            if (session != null) {
                this.sessionKey = session.getSessionKey();
                this.businessName = session.getBusinessName();
                this.customerName = session.getCustomerName();
                this.customerDetail = session.getCustomerDetail();
                this.from = entity.getFromServiceUser();
                this.to = entity.getToServiceUser();
                if (infoMap != null) {
                    UserInfo fromInfo = infoMap.get(entity.getFromServiceUser());
                    UserExtraField fromInfoUef = null != fromInfo ? fromInfo.getUserExtraField() : null;
                    if (fromInfoUef != null) {
                        this.fromName = fromInfoUef.getNickName();
                    }
                    UserInfo toInfo = infoMap.get(entity.getToServiceUser());
                    UserExtraField toInfoUef = null != toInfo ? toInfo.getUserExtraField() : null;
                    if (toInfoUef != null) {
                        this.toName = toInfoUef.getNickName();
                    }
                }
                this.remark = entity.getRemark();

            }
            this.remaining = remaining;
        }
    }

}
