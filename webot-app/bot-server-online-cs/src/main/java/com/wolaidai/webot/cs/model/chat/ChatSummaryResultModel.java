package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "聊天平台概要信息返回")
public class ChatSummaryResultModel extends BaseModel {
    @ApiModelProperty(value = "客服邮箱")
    private String email;
    @ApiModelProperty(value = "客服姓名")
    private String name;
    @ApiModelProperty(value = "最大接待数")
    private Integer maxReception;
    @ApiModelProperty(value = "当前队列数")
    private Integer currentQueueSize;
    @ApiModelProperty(value = "总队列数")
    private Integer totalQueueSize;
    @ApiModelProperty(value = "在线会话")
    private List<SessionInfoModel> onlineSessions = new ArrayList<>();
    @ApiModelProperty(value = "离线会话")
    private List<SessionInfoModel> offlineSessions = new ArrayList<>();
    @ApiModelProperty(value = "排队用户")
    private List<LineInfoModel> lines = new ArrayList<>();

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getMaxReception() {
        return maxReception;
    }

    public void setMaxReception(Integer maxReception) {
        this.maxReception = maxReception;
    }

    public Integer getCurrentQueueSize() {
        return currentQueueSize;
    }

    public void setCurrentQueueSize(Integer currentQueueSize) {
        this.currentQueueSize = currentQueueSize;
    }

    public Integer getTotalQueueSize() {
        return totalQueueSize;
    }

    public void setTotalQueueSize(Integer totalQueueSize) {
        this.totalQueueSize = totalQueueSize;
    }

    public List<SessionInfoModel> getOnlineSessions() {
        return onlineSessions;
    }

    public void setOnlineSessions(List<SessionInfoModel> onlineSessions) {
        this.onlineSessions = onlineSessions;
    }

    public List<SessionInfoModel> getOfflineSessions() {
        return offlineSessions;
    }

    public void setOfflineSessions(List<SessionInfoModel> offlineSessions) {
        this.offlineSessions = offlineSessions;
    }

    public List<LineInfoModel> getLines() {
        return lines;
    }

    public void setLines(List<LineInfoModel> lines) {
        this.lines = lines;
    }
}
