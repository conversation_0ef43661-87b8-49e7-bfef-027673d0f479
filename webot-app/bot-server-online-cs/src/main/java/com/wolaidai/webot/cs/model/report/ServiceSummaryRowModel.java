package com.wolaidai.webot.cs.model.report;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.elastic.entity.ServiceSummaryReportEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import com.wolaidai.webot.data.mysql.entity.config.BusinessUnitEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "服务小结统计详情")
public class ServiceSummaryRowModel extends BaseModel {

    @ApiModelProperty(value = "业务单元")
    private String unitName = "";
    @ApiModelProperty(value = "二级分类")
    private String typeName1 = "";
    @ApiModelProperty(value = "三级分类")
    private String typeName2 = "";
    @ApiModelProperty(value = "数量")
    private Integer itemCount;
    @ApiModelProperty(value = "小结数量")
    private Integer totalCount;
    @ApiModelProperty(value = "占比")
    private String percent;

    public ServiceSummaryRowModel(ServiceSummaryReportEntity ssr, BusinessUnitEntity u, BusinessTypeEntity t) {
        this.unitName = u.getName();
        if (null != t) {
            while (true) {
                if (t.getLevel() == 2) {
                    this.typeName2 = t.getTitle();
                } else if (t.getLevel() == 1) {
                    this.typeName1 = t.getTitle();
                    break;
                }
                t = t.getParentType();
            }
        }
        this.itemCount = ssr.getItemCount();
        this.totalCount = ssr.getTotalCount();
        this.percent = ssr.getPercent() + "%";
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getTypeName1() {
        return typeName1;
    }

    public void setTypeName1(String typeName1) {
        this.typeName1 = typeName1;
    }

    public String getTypeName2() {
        return typeName2;
    }

    public void setTypeName2(String typeName2) {
        this.typeName2 = typeName2;
    }

    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getPercent() {
        return percent;
    }

    public void setPercent(String percent) {
        this.percent = percent;
    }

}
