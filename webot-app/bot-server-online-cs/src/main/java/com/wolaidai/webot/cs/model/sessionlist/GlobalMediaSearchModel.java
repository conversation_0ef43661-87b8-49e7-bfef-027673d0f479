package com.wolaidai.webot.cs.model.sessionlist;

import com.wolaidai.webot.cs.model.PageableModel;
import com.wolaidai.webot.data.elastic.entity.ChatHistoryElasticEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.*;

@ApiModel(description = "公共媒体文件记录")
public class GlobalMediaSearchModel extends PageableModel {

    @ApiModelProperty(value = "会话建立时间")
    private Date startTime;
    @ApiModelProperty(value = "会话结束时间")
    private Date endTime;
    @ApiModelProperty(value = "文件类型,image,video")
    private String mediaType;
    @ApiModelProperty(value = "客户昵称")
    private String customerName;
    @ApiModelProperty(value = "客服email,逗号分隔")
    private List<String> staffEmails = new ArrayList<>();
    private List<MediaInfoRow> rowList = new ArrayList<>();

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getMediaType() {
        return mediaType;
    }

    public void setMediaType(String mediaType) {
        this.mediaType = mediaType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public List<String> getStaffEmails() {
        return staffEmails;
    }

    public void setStaffEmails(List<String> staffEmails) {
        this.staffEmails = staffEmails;
    }

    public List<MediaInfoRow> getRowList() {
        return rowList;
    }

    public void setRowList(List<MediaInfoRow> rowList) {
        this.rowList = rowList;
    }

    public static class MediaInfoRow {

        private String customerName;
        private String mediaType;
        private String mediaUrl;
        private Date date;
        private Set<String> staffNames = new LinkedHashSet<>();

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getMediaType() {
            return mediaType;
        }

        public void setMediaType(String mediaType) {
            this.mediaType = mediaType;
        }

        public String getMediaUrl() {
            return mediaUrl;
        }

        public void setMediaUrl(String mediaUrl) {
            this.mediaUrl = mediaUrl;
        }

        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }

        public Set<String> getStaffNames() {
            return staffNames;
        }

        public void setStaffNames(Set<String> staffNames) {
            this.staffNames = staffNames;
        }

        public MediaInfoRow(String customerName, String mediaUrl, ChatHistoryElasticEntity elasticEntity, Set<String> staffNames) {
            this.mediaUrl = mediaUrl;
            this.customerName = customerName;
            if (elasticEntity != null) {
                this.mediaType = elasticEntity.getType();
                this.date = elasticEntity.getDate();
            }
            this.staffNames = staffNames;
        }

        public MediaInfoRow() {
        }
    }
}
