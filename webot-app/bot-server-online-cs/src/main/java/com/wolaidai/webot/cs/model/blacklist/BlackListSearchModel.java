package com.wolaidai.webot.cs.model.blacklist;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.wolaidai.webot.cs.model.PageableModel;

public class BlackListSearchModel extends PageableModel {
    private Date beginDate;
    private Date endDate;
    private String name;
    private String phone;
    private List<Integer> status = new ArrayList<>();
    private List<String> creators = new ArrayList<>();
    private List<BlackListRowModel> list = new ArrayList<>();

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public List<Integer> getStatus() {
        return status;
    }

    public void setStatus(List<Integer> status) {
        this.status = status;
    }

    public List<String> getCreators() {
        return creators;
    }

    public void setCreators(List<String> creators) {
        this.creators = creators;
    }

    public List<BlackListRowModel> getList() {
        return list;
    }

    public void setList(List<BlackListRowModel> list) {
        this.list = list;
    }

}
