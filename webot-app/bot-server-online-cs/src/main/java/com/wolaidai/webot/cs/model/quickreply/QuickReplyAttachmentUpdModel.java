package com.wolaidai.webot.cs.model.quickreply;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "快捷回复附件")
public class QuickReplyAttachmentUpdModel {

    @ApiModelProperty(value = "附件")
    private MultipartFile file;
    @ApiModelProperty(value = "附件类型,图片-PICTURE,视频-VIDEO,其他-OTHER")
    private String attachmentType;
    @NotBlank(message = "{NotBlack.quickReply.content}")
    @ApiModelProperty(value = "描述")
    private String remark;

    public MultipartFile getFile() {
        return file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }

    public String getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(String attachmentType) {
        this.attachmentType = attachmentType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
