package com.wolaidai.webot.cs.service.impl;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.config.AppPropertyConfig;
import com.wolaidai.webot.cs.model.report.StateModel;
import com.wolaidai.webot.cs.model.report.StateRowExportModel;
import com.wolaidai.webot.cs.model.report.StateRowModel;
import com.wolaidai.webot.cs.service.StateReportService;
import com.wolaidai.webot.cs.util.CommonUtil;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.mysql.entity.chat.UserStateEntity;
import com.wolaidai.webot.data.mysql.entity.report.StateReportEntity;
import com.wolaidai.webot.data.mysql.entity.task.TaskEntity;
import com.wolaidai.webot.data.mysql.model.UserExtraField;
import com.wolaidai.webot.data.mysql.model.UserInfo;
import com.wolaidai.webot.data.mysql.model.UserStateDuration;
import com.wolaidai.webot.data.mysql.repo.StateReportRepo;
import com.wolaidai.webot.data.mysql.repo.UserStateRepo;

@Service
public class StateReportServiceImpl implements StateReportService {

    final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private StateReportRepo statisticsStateRepo;
    @Autowired
    private AppPropertyConfig config;
    @Autowired
    private UserStateRepo userStateRepo;
    @Autowired
    protected AppPropertyConfig appPropertyConfig;

    @Override
    public List<StateReportEntity> generateState(Integer _orgId, Set<String> emails, Date startTime, Date endTime) {
        ArrayList<StateReportEntity> sses = new ArrayList<>();
        HashMap<String, UserInfo> infoMap = new HashMap<>();
        for (Date[] arr : DateUtil.splitDateByDay(startTime, endTime)) {
            HashMap<Integer, HashMap<String, ArrayList<UserStateDuration>>> map = ReportUtil.getUserStateDuration(_orgId, emails, arr[0], arr[1]);
            for (Map.Entry<Integer, HashMap<String, ArrayList<UserStateDuration>>> entry : map.entrySet()) {
                Integer orgId = entry.getKey();
                HashMap<String, ArrayList<UserStateDuration>> m = entry.getValue();
                if (m.size() > 0) {
                    ArrayList<StateReportEntity> list = processOneDayState(orgId, m, infoMap, arr[0], arr[1]);
                    if (null != list && list.size() > 0) {
                        statisticsStateRepo.saveAll(list);
                        sses.addAll(list);
                    }
                }
            }
        }
        return sses;
    }

    private ArrayList<StateReportEntity> processOneDayState(Integer orgId, HashMap<String, ArrayList<UserStateDuration>> m, HashMap<String, UserInfo> infoMap, Date startTime, Date endTime) {
        ArrayList<StateReportEntity> sses = new ArrayList<>();
        ReportUtil.getUserInfoList(orgId, config.getProductId(), infoMap, m.keySet());
        HashMap<String, StateReportEntity> emptyInfoMap = new HashMap<>();
        for (Map.Entry<String, ArrayList<UserStateDuration>> entry : m.entrySet()) {
            String email = entry.getKey();
            StateReportEntity ss = new StateReportEntity();
            ss.setDataTime(startTime);
            ss.setOrgId(orgId);
            ss.setEmail(email);

            boolean skip = true;
            for (UserStateDuration usd : entry.getValue()) {
                Integer state = usd.getState();
                JSONObject data = new JSONObject();
                if (null != usd.getCreateTime()) {
                    data.put("begin", DateUtil.formatDateTime(usd.getCreateTime(), ""));
                } else {
                    data.put("begin", "");
                }
                if (null != usd.getEndTime()) {
                    data.put("end", DateUtil.formatDateTime(usd.getEndTime(), ""));
                } else {
                    data.put("end", "");
                }
                data.put("seconds", usd.getSeconds());
                if (Objects.equals(UserStateEntity.STATE_ONLINE, state)) {
                    ss.getOnlineData().add(data);
                    skip = false;
                } else if (Objects.equals(UserStateEntity.STATE_BUSY, state)) {
                    ss.getBusyData().add(data);
                    skip = false;
                } else if (Objects.equals(UserStateEntity.STATE_RESTING, state)) {
                    ss.getRestData().add(data);
                    skip = false;
                } else if (Objects.equals(UserStateEntity.STATE_LEAVE, state)) {
                    ss.getLeaveData().add(data);
                    skip = false;
                } else if (Objects.equals(UserStateEntity.STATE_STUDYING, state)) {
                    ss.getStudyData().add(data);
                    skip = false;
                } else if (Objects.equals(UserStateEntity.STATE_EATING, state)) {
                    ss.getEatData().add(data);
                    skip = false;
                }
            }
            if (!skip) {
                ss.setCreateTime(new Date());
                sses.add(ss);
                UserInfo info = infoMap.get(orgId + email);
                UserExtraField uef = null;
                if (null != info && null != (uef = info.getUserExtraField())) {
                    ss.setWorkNumber(uef.getWorkNumber());
                    ss.setNickName(uef.getNickName());
                } else {
                    emptyInfoMap.put(email, ss);
                }
            }
        }
        if (emptyInfoMap.size() > 0) {
            List<UserStateEntity> list = userStateRepo.findByOrgIdAndEmailInAndCreateTimeGreaterThanEqual(orgId, emptyInfoMap.keySet(), startTime);
            for (UserStateEntity s : list) {
                StateReportEntity ss = emptyInfoMap.get(s.getEmail());
                ss.setWorkNumber(s.getWorkNumber());
                ss.setNickName(s.getNickName());
            }
        }
        return sses;
    }

    @Override
    public void generateReportByDate(Integer orgId, Date startTime, Date endTime) {
        if (null != orgId) {
            statisticsStateRepo.deleteByOrgIdAndDataTime(orgId, startTime, endTime);
        } else {
            statisticsStateRepo.deleteByDataTime(startTime, endTime);
        }
        generateState(orgId, null, startTime, endTime);
    }

    @Override
    public String getReportName() {
        return "客服工作状态统计";
    }

    @Override
    public void loadData(Integer orgId, Object m) {
        StateModel model = (StateModel) m;
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);
        List<StateReportEntity> result = new ArrayList<>();
        if (today.getTime() >= model.getBeginDate().getTime() && today.before(model.getEndDate())) {
            result = generateState(orgId, model.getEmails(), today, new Date());
        }
        if (model.getBeginDate().before(today)) {
            Date endDate = model.getEndDate().after(today) ? DateUtils.addMilliseconds(today, -1) : model.getEndDate();
            List<StateReportEntity> list = null;
            if (model.getEmails().size() > 0) {
                list = statisticsStateRepo.findByOrgIdAndEmailInAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAscNickNameAsc(orgId, model.getEmails(), model.getBeginDate(), endDate);
            } else {
                list = statisticsStateRepo.findByOrgIdAndDataTimeGreaterThanEqualAndDataTimeLessThanEqualOrderByDataTimeAscNickNameAsc(orgId, model.getBeginDate(), endDate);
            }
            result.addAll(list);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (StateReportEntity s : result) {
            model.getList().add(new StateRowModel(s, sdf));
        }
    }

    @Override
    public boolean run(TaskEntity task) throws IOException {
        StateModel model = JSON.toJavaObject(task.getExtraParams(), StateModel.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = String.format("工作状态_%s_%s.xlsx", sdf.format(model.getBeginDate()), sdf.format(model.getEndDate()));
        task.setName(fileName);
        CommonUtil.saveTaskProgress(task, 0);
        loadData(task.getOrgId(), model);
        CommonUtil.saveTaskProgress(task, 75);
        ArrayList<StateRowExportModel> list = new ArrayList<>();
        for (StateRowModel s : model.getList()) {
            int rows = s.getMaxRows();
            if (rows == 0) {
                list.add(new StateRowExportModel(s, 0));
            } else {
                for (int i = 0; i < rows; i++) {
                    list.add(new StateRowExportModel(s, i));
                }
            }
        }
        Map<String, List<StateRowExportModel>> map = list.stream().collect(Collectors.groupingBy(i -> i.getDate()));
        Map<String, JSONArray> map2 = new TreeMap<>(Comparator.comparing(i -> i));
        HashMap<String, ArrayList<Integer>> indexes = new HashMap<>();
        for (Map.Entry<String, List<StateRowExportModel>> entry : map.entrySet()) {
            List<StateRowExportModel> l = entry.getValue();
            String lastEmail = null;
            ArrayList<Integer> index = new ArrayList<>();
            for (int i = 0; i < l.size(); i++) {
                StateRowExportModel s = l.get(i);
                if (!s.getEmail().equals(lastEmail)) {
                    lastEmail = s.getEmail();
                    index.add(i);
                }
            }
            index.add(l.size());
            indexes.put(entry.getKey(), index);
            l.add(0, l.get(0));
            map2.put(entry.getKey(), (JSONArray) JSON.toJSON(entry.getValue()));
        }
        LinkedHashMap<String, String> headerMap = new LinkedHashMap<>();
        headerMap.put("date", "日期");
        headerMap.put("workNumber", "工号");
        headerMap.put("nickName", "姓名");
        headerMap.put("onlineStart", "在线开始");
        headerMap.put("onlineEnd", "在线结束");
        headerMap.put("onlineDuration", "在线时长");
        headerMap.put("busyStart", "忙碌开始");
        headerMap.put("busyEnd", "忙碌结束");
        headerMap.put("busyDuration", "忙碌时长");
        headerMap.put("restStart", "小休开始");
        headerMap.put("restEnd", "小休结束");
        headerMap.put("restDuration", "小休时长");
        headerMap.put("leaveStart", "离开开始");
        headerMap.put("leaveEnd", "离开结束");
        headerMap.put("leaveDuration", "离开时长");
        headerMap.put("eatStart", "用餐开始");
        headerMap.put("eatEnd", "用餐结束");
        headerMap.put("eatDuration", "用餐时长");
        headerMap.put("studyStart", "培训开始");
        headerMap.put("studyEnd", "培训结束");
        headerMap.put("studyDuration", "培训时长");
        XSSFWorkbook sheets = ReportUtil.convertToExcel(map2, headerMap);
        int sheetCount = sheets.getNumberOfSheets();
        for (int i = 0; i < sheetCount; i++) {
            XSSFSheet sheet = sheets.getSheetAt(i);
            ArrayList<Integer> l = indexes.get(sheet.getSheetName());
            if (l.size() > 2) {
                for (int j = 0; j < l.size() - 1; j++) {
                    int startRow = l.get(j) + 2;
                    int endRow = l.get(j + 1) + 1;
                    if (endRow - startRow > 0) {
                        sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, 0, 0));
                        sheet.getRow(startRow).getCell(0).setCellValue(sheet.getSheetName());
                    }
                }
            }
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 3, 5));
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 6, 8));
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 9, 11));
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 14));
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 15, 17));
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 18, 20));
            XSSFRow h1 = sheet.getRow(0);
            h1.getCell(3).setCellValue("在线");
            h1.getCell(4).setCellValue("忙碌");
            h1.getCell(5).setCellValue("小休");
            h1.getCell(6).setCellValue("离开");
            h1.getCell(7).setCellValue("用餐");
            h1.getCell(8).setCellValue("培训");
            XSSFRow h2 = sheet.getRow(1);
            h2.getCell(0).setCellValue("");
            h2.getCell(1).setCellValue("");
            h2.getCell(2).setCellValue("");
            h2.getCell(3).setCellValue("开始时间");
            h2.getCell(4).setCellValue("结束时间");
            h2.getCell(5).setCellValue("持续时间");

            h2.getCell(6).setCellValue("开始时间");
            h2.getCell(7).setCellValue("结束时间");
            h2.getCell(8).setCellValue("持续时间");

            h2.getCell(9).setCellValue("开始时间");
            h2.getCell(10).setCellValue("结束时间");
            h2.getCell(11).setCellValue("持续时间");

            h2.getCell(12).setCellValue("开始时间");
            h2.getCell(13).setCellValue("结束时间");
            h2.getCell(14).setCellValue("持续时间");

            h2.getCell(15).setCellValue("开始时间");
            h2.getCell(16).setCellValue("结束时间");
            h2.getCell(17).setCellValue("持续时间");

            h2.getCell(18).setCellValue("开始时间");
            h2.getCell(19).setCellValue("结束时间");
            h2.getCell(20).setCellValue("持续时间");
        }
        CommonUtil.saveTaskProgress(task, 90);
        ReportUtil.saveToOss(sheets, appPropertyConfig.getOssBucketName(), task);
        return true;
    }
}
