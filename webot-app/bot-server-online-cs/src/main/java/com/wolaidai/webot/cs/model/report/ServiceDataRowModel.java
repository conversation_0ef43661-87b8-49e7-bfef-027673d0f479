package com.wolaidai.webot.cs.model.report;

import java.util.List;

import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.cs.util.ReportUtil;
import com.wolaidai.webot.data.mysql.entity.report.ServiceDataReportEntity;

import io.swagger.annotations.ApiModelProperty;

public class ServiceDataRowModel extends BaseModel {

    private String date = "";
    private String email;
    private String workNumber = "";
    private String nickName = "";
    @ApiModelProperty(value = "最大接待数")
    private Integer sessionCount = 0;
    private Long durationSecondsAvg = 0l;
    private Long totalBusy = 0l;
    private Long totalRest = 0l;
    private Long firstResponseAvg = 0l;
    private Long responseAvg = 0l;
    private String satisfactionPercent = "";
    private Long onlineAvg = 0l;
    private String onlineAvgStr = "";
    private int one = 0, two = 0, three = 0, four = 0, five = 0;
    private Float satisfactionPercentValue = 0f;

    public ServiceDataRowModel() {
    }

    int getInt(Number n) {
        if (null == n) {
            return 0;
        }
        return n.intValue();
    }

    public ServiceDataRowModel(List<ServiceDataReportEntity> list) {
        for (ServiceDataReportEntity s : list) {
            if (null == email) {
                email = s.getEmail();
                workNumber = s.getWorkNumber();
                nickName = s.getNickName();
            }
            sessionCount += s.getSessionCount();
            durationSecondsAvg += s.getDurationSecondsAvg();
            totalBusy += s.getTotalBusy();
            totalRest += s.getTotalRest();
            onlineAvg += s.getTotalOnline();
            firstResponseAvg += s.getFirstResponseAvg();
            responseAvg += s.getResponseAvg();
            one += getInt(s.getStarOne());
            two += getInt(s.getStarTwo());
            three += getInt(s.getStarThree());
            four += getInt(s.getStarFour());
            five += getInt(s.getStarFive());
        }
        int count = list.size();
        durationSecondsAvg = durationSecondsAvg / count;
        onlineAvg = onlineAvg / count;
        firstResponseAvg = firstResponseAvg / count;
        responseAvg = responseAvg / count;
        satisfactionPercentValue = ReportUtil.getPercent(four + five, one + two + three + four + five);
        satisfactionPercent = satisfactionPercentValue + "%";
        onlineAvgStr = DateUtil.formatSecondsTime(onlineAvg, null);
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public Long getDurationSecondsAvg() {
        return durationSecondsAvg;
    }

    public void setDurationSecondsAvg(Long durationSecondsAvg) {
        this.durationSecondsAvg = durationSecondsAvg;
    }

    public Long getTotalBusy() {
        return totalBusy;
    }

    public void setTotalBusy(Long totalBusy) {
        this.totalBusy = totalBusy;
    }

    public Long getTotalRest() {
        return totalRest;
    }

    public void setTotalRest(Long totalRest) {
        this.totalRest = totalRest;
    }

    public Long getFirstResponseAvg() {
        return firstResponseAvg;
    }

    public void setFirstResponseAvg(Long firstResponseAvg) {
        this.firstResponseAvg = firstResponseAvg;
    }

    public Long getResponseAvg() {
        return responseAvg;
    }

    public void setResponseAvg(Long responseAvg) {
        this.responseAvg = responseAvg;
    }

    public String getSatisfactionPercent() {
        return satisfactionPercent;
    }

    public void setSatisfactionPercent(String satisfactionPercent) {
        this.satisfactionPercent = satisfactionPercent;
    }

    public Long getOnlineAvg() {
        return onlineAvg;
    }

    public void setOnlineAvg(Long onlineAvg) {
        this.onlineAvg = onlineAvg;
    }

    public String getOnlineAvgStr() {
        return onlineAvgStr;
    }

    public void setOnlineAvgStr(String onlineAvgStr) {
        this.onlineAvgStr = onlineAvgStr;
    }

    public int getOne() {
        return one;
    }

    public void setOne(int one) {
        this.one = one;
    }

    public int getTwo() {
        return two;
    }

    public void setTwo(int two) {
        this.two = two;
    }

    public int getThree() {
        return three;
    }

    public void setThree(int three) {
        this.three = three;
    }

    public int getFour() {
        return four;
    }

    public void setFour(int four) {
        this.four = four;
    }

    public int getFive() {
        return five;
    }

    public void setFive(int five) {
        this.five = five;
    }

    public Float getSatisfactionPercentValue() {
        return satisfactionPercentValue;
    }

    public void setSatisfactionPercentValue(Float satisfactionPercentValue) {
        this.satisfactionPercentValue = satisfactionPercentValue;
    }

}
