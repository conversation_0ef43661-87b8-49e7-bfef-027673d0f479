package com.wolaidai.webot.cs.model.chat;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.common.util.DateUtil;
import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.chat.SatisfactionDataEntity;
import com.wolaidai.webot.data.mysql.entity.chat.ServiceSummaryEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionTransferListEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Objects;

@ApiModel(description = "会话详情")
public class SessionDetailModel extends BaseModel {
    @ApiModelProperty(value = "业务名")
    private String businessName;
    @ApiModelProperty(value = "客户姓名")
    private String customerName;
    @ApiModelProperty(value = "是否被标星，0：未标记，1：被标记")
    private Integer mark;
    private Boolean locked = false;
    @ApiModelProperty(value = "客户额外属性")
    private JSONObject extraProperties = new JSONObject();
    @ApiModelProperty(value = "访问次数")
    private Long accessTimes;
    @ApiModelProperty(value = "等待时间")
    private String waitTime;
    @ApiModelProperty(value = "创建时间")
    private Long createTimeMs;
    @ApiModelProperty(value = "离线时间")
    private Long offlineTimeMs;
    @ApiModelProperty(value = "服务小结")
    private ServiceSummaryDetailModel serviceSummary;
    @ApiModelProperty(value = "是否参评:1-已参评,0-未下发,-1-下发未参评")
    private Integer appraiseStatus = 0;
    @ApiModelProperty(value = "评价,1-5,null-未评价")
    private Integer appraiseLevel;
    @ApiModelProperty(value = "评价内容")
    private String satisfactionContent;
    @ApiModelProperty(value = "评价标签")
    private JSONArray satisfactionLabels;
    @ApiModelProperty(value = "咨询渠道")
    private Integer clientTypeId;
    @ApiModelProperty(value = "客户端ID")
    private String clientId;
    @ApiModelProperty(value = "客户在线状态:0-不在线,1-在线")
    private Integer clientStatus;
    @ApiModelProperty(value = "人脸核验状态:0-未核验,1-核验成功,2-核验失败")
    private int faceDetectionResult = 0;
    @ApiModelProperty(value = "人脸核验失败次数")
    private Integer failureTimes = 0;

    @ApiModelProperty(value = "高风险客户:0-否,1-是")
    private Boolean riskRegCustomer = false;

    public SessionDetailModel(SessionListEntity s, Long accessTimes) {
        this.businessName = s.getBusinessName();
        this.customerName = s.getCustomerName();
        this.mark = s.getMark();
        List<SessionTransferListEntity> l = s.getSessionTransferList();
        this.locked = (null != l && l.size() > 0 && l.get(0).getStatus() == SessionTransferListEntity.STATUS_START);
        this.extraProperties = s.getCustomerDetail();
        this.accessTimes = accessTimes;
        this.waitTime = DateUtil.formatSecondsTime(s.getWaitSecond(), null);
        this.createTimeMs = s.getCreateTime().getTime();
        if(s.getOfflineTime()!=null){
            this.offlineTimeMs = s.getOfflineTime().getTime();
        }
        ServiceSummaryEntity summary = s.getServiceSummary();
        if (null != summary) {
            serviceSummary = new ServiceSummaryDetailModel(summary);
        }
        if(s.getSatisfactionLevel()!=null) {
            if(Objects.equals(-1,s.getSatisfactionLevel())){
                appraiseStatus = -1;
            }else {
                appraiseStatus = 1;
                appraiseLevel = s.getSatisfactionLevel();
            }
            SatisfactionDataEntity sd = s.getSatisfactionData();
            if (null != sd) {
                satisfactionContent = sd.getContent();
                satisfactionLabels = sd.getLabels();
            }
        }
        clientId = s.getClientId();
        clientTypeId = s.getClientTypeId();
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getMark() {
        return mark;
    }

    public void setMark(Integer mark) {
        this.mark = mark;
    }

    public Boolean getLocked() {
        return locked;
    }

    public void setLocked(Boolean locked) {
        this.locked = locked;
    }

    public JSONObject getExtraProperties() {
        return extraProperties;
    }

    public void setExtraProperties(JSONObject extraProperties) {
        this.extraProperties = extraProperties;
    }

    public Long getAccessTimes() {
        return accessTimes;
    }

    public void setAccessTimes(Long accessTimes) {
        this.accessTimes = accessTimes;
    }

    public String getWaitTime() {
        return waitTime;
    }

    public void setWaitTime(String waitTime) {
        this.waitTime = waitTime;
    }

    public Long getCreateTimeMs() {
        return createTimeMs;
    }

    public void setCreateTimeMs(Long createTimeMs) {
        this.createTimeMs = createTimeMs;
    }

    public Long getOfflineTimeMs() {
        return offlineTimeMs;
    }

    public void setOfflineTimeMs(Long offlineTimeMs) {
        this.offlineTimeMs = offlineTimeMs;
    }

    public ServiceSummaryDetailModel getServiceSummary() {
        return serviceSummary;
    }

    public void setServiceSummary(ServiceSummaryDetailModel serviceSummary) {
        this.serviceSummary = serviceSummary;
    }

    public Integer getAppraiseStatus() {
        return appraiseStatus;
    }

    public void setAppraiseStatus(Integer appraiseStatus) {
        this.appraiseStatus = appraiseStatus;
    }

    public Integer getAppraiseLevel() {
        return appraiseLevel;
    }

    public void setAppraiseLevel(Integer appraiseLevel) {
        this.appraiseLevel = appraiseLevel;
    }

    public String getSatisfactionContent() {
        return satisfactionContent;
    }

    public void setSatisfactionContent(String satisfactionContent) {
        this.satisfactionContent = satisfactionContent;
    }

    public JSONArray getSatisfactionLabels() {
        return satisfactionLabels;
    }

    public void setSatisfactionLabels(JSONArray satisfactionLabels) {
        this.satisfactionLabels = satisfactionLabels;
    }

    public Integer getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(Integer clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getClientStatus() {
        return clientStatus;
    }

    public void setClientStatus(Integer clientStatus) {
        this.clientStatus = clientStatus;
    }

    public int getFaceDetectionResult() {
        return faceDetectionResult;
    }

    public void setFaceDetectionResult(int faceDetectionResult) {
        this.faceDetectionResult = faceDetectionResult;
    }

    public Integer getFailureTimes() {
        return failureTimes;
    }

    public void setFailureTimes(Integer failureTimes) {
        this.failureTimes = failureTimes;
    }

    public Boolean getRiskRegCustomer() {
        return riskRegCustomer;
    }

    public void setRiskRegCustomer(Boolean riskRegCustomer) {
        this.riskRegCustomer = riskRegCustomer;
    }
}
