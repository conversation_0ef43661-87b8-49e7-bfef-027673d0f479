package com.wolaidai.webot.cs.model.chat;

import com.wolaidai.webot.cs.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;

@ApiModel(description = "服务小结")
public class ServiceSummaryModel extends BaseModel {
    @ApiModelProperty(value = "unitId: u_1/typeId：t_1")
    private String summaryKey;
    @Size(max = 1000, message = "{Size.remark}")
    private String remark;
    @ApiModelProperty(value = "是否收藏")
    private boolean collect;

    public String getSummaryKey() {
        return summaryKey;
    }

    public void setSummaryKey(String summaryKey) {
        this.summaryKey = summaryKey;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public boolean isCollect() {
        return collect;
    }

    public void setCollect(boolean collect) {
        this.collect = collect;
    }
}
