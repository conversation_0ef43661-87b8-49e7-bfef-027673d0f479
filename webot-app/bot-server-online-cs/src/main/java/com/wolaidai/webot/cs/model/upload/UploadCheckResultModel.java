package com.wolaidai.webot.cs.model.upload;

import com.wolaidai.webot.cs.model.BaseModel;

import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

public class UploadCheckResultModel extends BaseModel {

    private String uploadId;
    private String fileName;
    private int totalCount;
    private Set<UploadErrorModel> errors = new LinkedHashSet<>();
    private Map<String, String> errorMap = new HashMap<>();

    public String getUploadId() {
        return uploadId;
    }

    public void setUploadId(String uploadId) {
        this.uploadId = uploadId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public Set<UploadErrorModel> getErrors() {
        return errors;
    }

    public void setErrors(Set<UploadErrorModel> errors) {
        this.errors = errors;
    }

    public void addError(UploadErrorModel errorModel) {
        errors.add(errorModel);
        errorMap.put(generateKey(errorModel.getType(), errorModel.getContent()), errorModel.getError());
    }

    public void cleanErrors() {
        errors.clear();
    }

    public String findError(int type, String content) {
        return errorMap.get(generateKey(type, content));
    }

    private String generateKey(int type, String content) {
        return type + "_" + content;
    }
}
