package com.wolaidai.webot.cs.model.servicesummary;

import com.wolaidai.webot.cs.model.BaseModel;
import com.wolaidai.webot.data.mysql.entity.config.BusinessTypeEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

public class BusinessTypeSearchModel extends BaseModel {

    @ApiModelProperty(value = "状态,1-启用/0-禁用")
    private Integer status;

    private List<BusinessTypeRowModel> list = new ArrayList<>();

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<BusinessTypeRowModel> getList() {
        return list;
    }

    public void setList(List<BusinessTypeRowModel> list) {
        this.list = list;
    }

    public static class BusinessTypeRowModel extends BaseModel {

        private String id;
        private String title;
        private Integer position;
        @ApiModelProperty(value = "状态,1-启用/0-禁用")
        private Integer status;
        private Integer level;
        private Set<BusinessTypeRowModel> children = new LinkedHashSet<>();

        public BusinessTypeRowModel() {
        }

        public BusinessTypeRowModel(Integer searchStatus, BusinessTypeEntity entity) {
            this.id = String.valueOf(entity.getId());
            this.title = entity.getTitle();
            this.position = entity.getPosition();
            this.status = entity.getStatus();
            this.level = entity.getLevel();
            if (CollectionUtils.isNotEmpty(entity.getChildTypes())) {
                for (BusinessTypeEntity childType : entity.getChildTypes()) {
                    if (searchStatus != null && !Objects.equals(searchStatus, childType.getStatus())) {
                        continue;
                    }
                    this.children.add(new BusinessTypeRowModel(searchStatus, childType));
                }
            }
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public Integer getPosition() {
            return position;
        }

        public void setPosition(Integer position) {
            this.position = position;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Integer getLevel() {
            return level;
        }

        public void setLevel(Integer level) {
            this.level = level;
        }

        public Set<BusinessTypeRowModel> getChildren() {
            return children;
        }

        public void setChildren(Set<BusinessTypeRowModel> children) {
            this.children = children;
        }
    }
}
