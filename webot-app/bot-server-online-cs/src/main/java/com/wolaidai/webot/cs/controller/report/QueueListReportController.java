package com.wolaidai.webot.cs.controller.report;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wolaidai.webot.cs.constant.WebStatusConstants;
import com.wolaidai.webot.cs.controller.BaseController;
import com.wolaidai.webot.cs.model.ResponseModel;
import com.wolaidai.webot.cs.model.report.QueueModel;
import com.wolaidai.webot.cs.service.CustomerInfoEncService;
import com.wolaidai.webot.data.mysql.entity.chat.QueueListEntity;
import com.wolaidai.webot.data.mysql.repo.ComplexQueueListRepo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;

@Api(tags = "排队报表接口")
@RestController
@RequestMapping("/report")
public class QueueListReportController extends BaseController {

    @Autowired
    private ComplexQueueListRepo complexQueueListRepo;
    @Autowired
    private CustomerInfoEncService customerInfoEncService;

    @GetMapping(value = "/queue")
    @ApiOperation(value = "取消排队报表")
    public ResponseModel queueReport(QueueModel queueModel) {
        if (queueModel.getStartTime() == null || queueModel.getEndTime() == null) {
            return new ResponseModel(WebStatusConstants.RESPONSE_CODE_FAILURE, "起止时间不能为空");
        }
        Page<QueueListEntity> queueListEntities = complexQueueListRepo.findByOrgIdAndStatusAndTime(
                getUser().getOrganizationId(), QueueListEntity.DEQUEUE_STATUS, queueModel.getStartTime(), queueModel.getEndTime(), queueModel.getUuid(), queueModel.getUserId(), 
                queueModel.getPageNumber() == -1 ? Pageable.unpaged() : PageRequest.of(queueModel.getPageNumber(), queueModel.getPageSize()));
        queueModel.setTotal(queueListEntities.getTotalElements());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (QueueListEntity entity : queueListEntities.getContent()) {
            JSONObject detail = entity.getCustomerDetail();
            JSONArray customers = null != detail ? detail.getJSONArray("customers") : null;
            customerInfoEncService.decryptCustomerInfo(customers);
            queueModel.getInfoRowList().add(new QueueModel.QueueInfoRow(sdf, entity));
        }
        return new ResponseModel(WebStatusConstants.RESPONSE_CODE_SUCCESS, "查询成功", queueModel);
    }
}
