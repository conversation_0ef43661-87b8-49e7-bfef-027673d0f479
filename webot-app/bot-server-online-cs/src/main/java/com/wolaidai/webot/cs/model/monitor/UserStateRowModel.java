package com.wolaidai.webot.cs.model.monitor;

import com.wolaidai.webot.cs.model.BaseModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "在线监控坐席")
public class UserStateRowModel extends BaseModel {
    
    @ApiModelProperty(value = "客服邮箱")
    private String email;
    @ApiModelProperty(value = "工号")
    private String workNumber;
    @ApiModelProperty(value = "昵称")
    private String nickName;
    @ApiModelProperty(value = "状态")
    private String state;
    @ApiModelProperty(value = "状态时长")
    private String duration;
    @ApiModelProperty(value = "实时接待人数，当前会话中的数量")
    private Integer sessionCount;
    @ApiModelProperty(value = "接待上限")
    private Integer maxReception;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public Integer getMaxReception() {
        return maxReception;
    }

    public void setMaxReception(Integer maxReception) {
        this.maxReception = maxReception;
    }

}
