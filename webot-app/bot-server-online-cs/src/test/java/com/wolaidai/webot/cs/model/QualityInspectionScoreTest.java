package com.wolaidai.webot.cs.model;

import com.wolaidai.webot.cs.model.sessionlist.GlobalSessionRowModel;
import com.wolaidai.webot.data.elastic.entity.SessionListElasticEntity;
import com.wolaidai.webot.data.mysql.entity.chat.SessionListEntity;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 qualityInspectionScore 字段的类型和功能
 */
public class QualityInspectionScoreTest {

    @Test
    public void testSessionListElasticEntity_QualityInspectionScore() {
        // Given
        SessionListElasticEntity entity = new SessionListElasticEntity();
        Integer expectedScore = 85;

        // When
        entity.setQualityInspectionScore(expectedScore);

        // Then
        assertEquals(expectedScore, entity.getQualityInspectionScore());
        assertTrue(entity.getQualityInspectionScore() instanceof Integer);
    }

    @Test
    public void testSessionListEntity_QualityInspectionScore() {
        // Given
        SessionListEntity entity = new SessionListEntity();
        Integer expectedScore = 92;

        // When
        entity.setQualityInspectionScore(expectedScore);

        // Then
        assertEquals(expectedScore, entity.getQualityInspectionScore());
        assertTrue(entity.getQualityInspectionScore() instanceof Integer);
    }

    @Test
    public void testGlobalSessionRowModel_QualityInspectionScore() {
        // Given
        GlobalSessionRowModel model = new GlobalSessionRowModel();
        Integer expectedScore = 78;

        // When
        model.setQualityInspectionScore(expectedScore);

        // Then
        assertEquals(expectedScore, model.getQualityInspectionScore());
        assertTrue(model.getQualityInspectionScore() instanceof Integer);
    }

    @Test
    public void testQualityInspectionScore_NullValues() {
        // Given
        SessionListElasticEntity elasticEntity = new SessionListElasticEntity();
        SessionListEntity mysqlEntity = new SessionListEntity();
        GlobalSessionRowModel rowModel = new GlobalSessionRowModel();

        // When - 设置为 null
        elasticEntity.setQualityInspectionScore(null);
        mysqlEntity.setQualityInspectionScore(null);
        rowModel.setQualityInspectionScore(null);

        // Then - 应该能正确处理 null 值
        assertNull(elasticEntity.getQualityInspectionScore());
        assertNull(mysqlEntity.getQualityInspectionScore());
        assertNull(rowModel.getQualityInspectionScore());
    }

    @Test
    public void testQualityInspectionScore_BoundaryValues() {
        // Given
        SessionListElasticEntity entity = new SessionListElasticEntity();

        // When & Then - 测试边界值
        entity.setQualityInspectionScore(0);
        assertEquals(Integer.valueOf(0), entity.getQualityInspectionScore());

        entity.setQualityInspectionScore(100);
        assertEquals(Integer.valueOf(100), entity.getQualityInspectionScore());

        entity.setQualityInspectionScore(-1);
        assertEquals(Integer.valueOf(-1), entity.getQualityInspectionScore());
    }

    @Test
    public void testDataMapping_ElasticToRowModel() {
        // Given
        SessionListElasticEntity elasticEntity = new SessionListElasticEntity();
        GlobalSessionRowModel rowModel = new GlobalSessionRowModel();
        Integer expectedScore = 88;

        // When
        elasticEntity.setQualityInspectionScore(expectedScore);
        rowModel.setQualityInspectionScore(elasticEntity.getQualityInspectionScore());

        // Then
        assertEquals(expectedScore, rowModel.getQualityInspectionScore());
        assertEquals(elasticEntity.getQualityInspectionScore(), rowModel.getQualityInspectionScore());
    }
}
