2025-05-20 14:04:25.358 [INFO] 22888 [kground-preinit] o.h.validator.internal.util.Version     [Version.java:21] : HV000001: Hibernate Validator 6.2.3.Final
2025-05-20 14:04:25.390 [INFO] 22888 [           main] c.wolaidai.webot.cs.WebotWebApplication [StartupInfoLogger.java:55] : Starting WebotWebApplication using Java 1.8.0_301 on QH20071510L with PID 22888 (D:\code\bot\bot-server-online-cs\webot-app\bot-server-online-cs\target\classes started by leon.li in D:\code\bot\bot-server-online-cs)
2025-05-20 14:04:25.392 [INFO] 22888 [           main] c.wolaidai.webot.cs.WebotWebApplication [SpringApplication.java:640] : No active profile set, falling back to 1 default profile: "default"
2025-05-20 14:04:26.380 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:262] : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-20 14:04:26.381 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:132] : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-20 14:04:26.647 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:201] : Finished Spring Data repository scanning in 257 ms. Found 31 JPA repository interfaces.
2025-05-20 14:04:26.651 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:262] : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-20 14:04:26.651 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:132] : Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-05-20 14:04:26.656 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:201] : Finished Spring Data repository scanning in 3 ms. Found 0 Elasticsearch repository interfaces.
2025-05-20 14:04:26.876 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:262] : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-20 14:04:26.876 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:132] : Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-05-20 14:04:26.893 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:201] : Finished Spring Data repository scanning in 16 ms. Found 0 Elasticsearch repository interfaces.
2025-05-20 14:04:26.898 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:262] : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-20 14:04:26.899 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:132] : Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-05-20 14:04:26.908 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:201] : Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-05-20 14:04:26.926 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:262] : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-20 14:04:26.928 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:132] : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-20 14:04:26.944 [INFO] 22888 [           main] .s.d.r.c.RepositoryConfigurationDelegate[RepositoryConfigurationDelegate.java:201] : Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-05-20 14:04:27.292 [INFO] 22888 [           main] c.w.p.i.j.DataSourceBeanPostProcessor   [DataSourceBeanPostProcessor.java:37] : 开启加密datasource增强
2025-05-20 14:04:27.307 [INFO] 22888 [           main] com.welab.privacy.config.PrivacyConfigs [PrivacyConfigs.java:34] : Init welab privacy config info.
2025-05-20 14:04:27.840 [INFO] 22888 [           main] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-online-cs&secret=%2Fgsaz1hBUZ32wbgQSJ7t6aEdi7T2c9tz2s6MR7HTn7U%3D'.
2025-05-20 14:04:28.327 [INFO] 22888 [           main] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:251] : Welab privacy config cache info key set : [cs_bot:black_list, cs_bot:face_detection, cs_bot:session_detail_report, auth:admin_user, auth:organization, auth:user, qa_bot:user, qa_bot:organization, qa_bot:admin_user, cs_bot:queue_list, cs_bot:session_list, cs_bot:message_event, webot-v2:conversations, webot-v2:histories, webot-v2:feedbacks, cs_bot:call_detail, qa_bot:call_detail, cs_bot:black_list_test, cs_bot:user_verification_history]
2025-05-20 14:04:28.328 [INFO] 22888 [           main] trationDelegate$BeanPostProcessorChecker[PostProcessorRegistrationDelegate.java:376] : Bean 'privacyConfigs' of type [com.welab.privacy.config.PrivacyConfigs] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-20 14:04:28.756 [INFO] 22888 [           main] org.eclipse.jetty.util.log              [Log.java:170] : Logging initialized @11435ms to org.eclipse.jetty.util.log.Slf4jLog
2025-05-20 14:04:28.980 [INFO] 22888 [           main] o.s.b.w.e.j.JettyServletWebServerFactory[JettyServletWebServerFactory.java:166] : Server initialized with port: 9393
2025-05-20 14:04:28.985 [INFO] 22888 [           main] org.eclipse.jetty.server.Server         [Server.java:375] : jetty-9.4.45.v20220203; built: 2022-02-03T09:14:34.105Z; git: 4a0c91c0be53805e3fcffdcdcc9587d5301863db; jvm 1.8.0_301-b09
2025-05-20 14:04:29.026 [INFO] 22888 [           main] o.e.j.s.h.ContextHandler.application    [ContextHandler.java:2368] : Initializing Spring embedded WebApplicationContext
2025-05-20 14:04:29.026 [INFO] 22888 [           main] w.s.c.ServletWebServerApplicationContext[ServletWebServerApplicationContext.java:290] : Root WebApplicationContext: initialization completed in 3589 ms
2025-05-20 14:04:30.241 [INFO] 22888 [           main] c.w.p.i.j.DataSourceBeanPostProcessor   [DataSourceBeanPostProcessor.java:54] : enhance HikariDataSource (null)
2025-05-20 14:04:30.301 [INFO] 22888 [           main] com.zaxxer.hikari.HikariDataSource      [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-05-20 14:04:30.692 [INFO] 22888 [           main] com.zaxxer.hikari.HikariDataSource      [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-05-20 14:04:30.828 [INFO] 22888 [           main] o.hibernate.jpa.internal.util.LogHelper [LogHelper.java:31] : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-20 14:04:30.949 [INFO] 22888 [           main] org.hibernate.Version                   [Version.java:44] : HHH000412: Hibernate ORM core version 5.6.7.Final
2025-05-20 14:04:31.296 [INFO] 22888 [           main] o.hibernate.annotations.common.Version  [JavaReflectionManager.java:56] : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-20 14:04:31.711 [INFO] 22888 [           main] org.hibernate.dialect.Dialect           [Dialect.java:175] : HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-05-20 14:04:32.517 [INFO] 22888 [           main] Hibernate Types                         [Configuration.java:304] : This framework is proudly powered by:

>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
 _    _                           _     _
| |  | |                         (_)   | |
| |__| |_   _ _ __   ___ _ __ ___ _ ___| |_ ___ _ __   ___ ___
|  __  | | | | '_ \ / _ \ '__/ __| / __| __/ _ \ '_ \ / __/ _ \
| |  | | |_| | |_) |  __/ |  \__ \ \__ \ ||  __/ | | | (_|  __/
|_|  |_|\__, | .__/ \___|_|  |___/_|___/\__\___|_| |_|\___\___|
         __/ | |
        |___/|_|

At Hypersistence, we only build amazing tools, like Hibernate Types, Flexy Pool, or Hypersistence Optimizer.

What if there were a tool that could automatically detect JPA and Hibernate performance issues?

Hypersistence Optimizer is that tool! For more details, go to: 

https://vladmihalcea.com/hypersistence-optimizer/
<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

2025-05-20 14:04:33.904 [INFO] 22888 [           main] o.h.e.t.j.p.i.JtaPlatformInitiator      [JtaPlatformInitiator.java:52] : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-20 14:04:33.931 [INFO] 22888 [           main] j.LocalContainerEntityManagerFactoryBean[AbstractEntityManagerFactoryBean.java:437] : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-20 14:04:38.867 [INFO] 22888 [           main] org.redisson.Version                    [Version.java:41] : Redisson 3.16.8
2025-05-20 14:04:40.281 [INFO] 22888 [sson-netty-4-23] o.r.c.pool.MasterPubSubConnectionPool   [ConnectionPool.java:161] : 1 connections initialized for 10.90.0.11/10.90.0.11:6379
2025-05-20 14:04:40.434 [INFO] 22888 [sson-netty-4-19] o.r.c.pool.MasterConnectionPool         [ConnectionPool.java:161] : 24 connections initialized for 10.90.0.11/10.90.0.11:6379
2025-05-20 14:04:42.862 [INFO] 22888 [           main] o.elasticsearch.plugins.PluginsService  [PluginsService.java:192] : no modules loaded
2025-05-20 14:04:42.866 [INFO] 22888 [           main] o.elasticsearch.plugins.PluginsService  [PluginsService.java:195] : loaded plugin [org.elasticsearch.index.reindex.ReindexPlugin]
2025-05-20 14:04:42.867 [INFO] 22888 [           main] o.elasticsearch.plugins.PluginsService  [PluginsService.java:195] : loaded plugin [org.elasticsearch.join.ParentJoinPlugin]
2025-05-20 14:04:42.867 [INFO] 22888 [           main] o.elasticsearch.plugins.PluginsService  [PluginsService.java:195] : loaded plugin [org.elasticsearch.percolator.PercolatorPlugin]
2025-05-20 14:04:42.867 [INFO] 22888 [           main] o.elasticsearch.plugins.PluginsService  [PluginsService.java:195] : loaded plugin [org.elasticsearch.script.mustache.MustachePlugin]
2025-05-20 14:04:42.868 [INFO] 22888 [           main] o.elasticsearch.plugins.PluginsService  [PluginsService.java:195] : loaded plugin [org.elasticsearch.transport.Netty4Plugin]
2025-05-20 14:04:42.868 [INFO] 22888 [           main] o.elasticsearch.plugins.PluginsService  [PluginsService.java:195] : loaded plugin [org.elasticsearch.xpack.core.XPackClientPlugin]
2025-05-20 14:04:47.606 [INFO] 22888 [           main] o.e.transport.NettyAllocator            [NettyAllocator.java:110] : creating NettyAllocator with the following configs: [name=elasticsearch_configured, chunk_size=1mb, suggested_max_allocation_size=1mb, factors={es.unsafe.use_netty_default_chunk_and_page_size=false, g1gc_enabled=false, g1gc_region_size=0b}]
2025-05-20 14:04:50.257 [INFO] 22888 [           main] org.eclipse.jetty.server.session        [DefaultSessionIdManager.java:334] : DefaultSessionIdManager workerName=node0
2025-05-20 14:04:50.257 [INFO] 22888 [           main] org.eclipse.jetty.server.session        [DefaultSessionIdManager.java:339] : No SessionScavenger set, using defaults
2025-05-20 14:04:50.260 [INFO] 22888 [           main] org.eclipse.jetty.server.session        [HouseKeeper.java:132] : node0 Scavenging every 600000ms
2025-05-20 14:04:50.316 [INFO] 22888 [           main] o.e.jetty.server.handler.ContextHandler [ContextHandler.java:921] : Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@30b0de1d{application,/api,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9393.8328876176916960266/, jar:file:/D:/software/maven/apache-maven-3.8.6-bin/apache-maven-3.8.6/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar!/META-INF/resources],AVAILABLE}
2025-05-20 14:04:50.316 [INFO] 22888 [           main] org.eclipse.jetty.server.Server         [Server.java:415] : Started @32995ms
2025-05-20 14:04:57.209 [WARN] 22888 [           main] o.s.b.a.f.FreeMarkerAutoConfiguration   [FreeMarkerAutoConfiguration.java:65] : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-05-20 14:04:57.364 [WARN] 22888 [           main] .s.s.UserDetailsServiceAutoConfiguration[UserDetailsServiceAutoConfiguration.java:87] : 

Using generated security password: 3895adf9-c7e9-4a4d-b2b8-6d1a41681771

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-05-20 14:04:59.192 [INFO] 22888 [           main] o.s.s.web.DefaultSecurityFilterChain    [DefaultSecurityFilterChain.java:55] : Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6c5a2034, org.springframework.security.web.context.SecurityContextPersistenceFilter@6cc45cf2, org.springframework.security.web.header.HeaderWriterFilter@19f926cf, org.springframework.security.web.authentication.logout.LogoutFilter@231a5622, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@223a5dad, com.wolaidai.webot.security.filter.CorsFilter@3d1cb317, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2b05257a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1f396ed8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5346038, org.springframework.security.web.session.SessionManagementFilter@65c5c319, org.springframework.security.web.access.ExceptionTranslationFilter@1849c0af, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@50769bc1]
2025-05-20 14:04:59.456 [INFO] 22888 [           main] o.e.j.s.h.ContextHandler.application    [ContextHandler.java:2368] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-20 14:04:59.456 [INFO] 22888 [           main] o.s.web.servlet.DispatcherServlet       [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-05-20 14:04:59.458 [INFO] 22888 [           main] o.s.web.servlet.DispatcherServlet       [FrameworkServlet.java:547] : Completed initialization in 2 ms
2025-05-20 14:04:59.497 [INFO] 22888 [           main] o.e.jetty.server.AbstractConnector      [AbstractConnector.java:333] : Started ServerConnector@7884f722{HTTP/1.1, (http/1.1)}{0.0.0.0:9393}
2025-05-20 14:04:59.498 [INFO] 22888 [           main] o.s.b.web.embedded.jetty.JettyWebServer [JettyWebServer.java:172] : Jetty started on port(s) 9393 (http/1.1) with context path '/api'
2025-05-20 14:05:04.188 [INFO] 22888 [           main] s.a.ScheduledAnnotationBeanPostProcessor[ScheduledAnnotationBeanPostProcessor.java:315] : No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-05-20 14:05:04.234 [INFO] 22888 [           main] c.wolaidai.webot.cs.WebotWebApplication [StartupInfoLogger.java:61] : Started WebotWebApplication in 39.441 seconds (JVM running for 46.914)
2025-05-20 14:05:04.630 [INFO] 22888 [   threadPool-1] c.w.w.c.s.impl.ReportHistoryServiceImpl [ReportHistoryServiceImpl.java:113] : start generateReport...
2025-05-20 14:05:07.900 [INFO] 22888 [   threadPool-1] c.w.w.c.s.impl.ReportHistoryServiceImpl [ReportHistoryServiceImpl.java:222] : end generateReport,cost 3269 ms
2025-05-20 14:05:10.395 [INFO] 22888 [   threadPool-2] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:05:11.359 [INFO] 22888 [   threadPool-2] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 963 ms
2025-05-20 14:05:28.340 [INFO] 22888 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:277] : Refresh welab privacy config info local cache.
2025-05-20 14:05:28.340 [INFO] 22888 [ConfigScheduler] c.welab.privacy.manager.PrivacyManager  [PrivacyManager.java:504] : Clear privacy element manager local cache. 
2025-05-20 14:05:28.342 [INFO] 22888 [ConfigScheduler] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-online-cs&secret=%2Fgsaz1hBUZ32wbgQSJ7t6aEdi7T2c9tz2s6MR7HTn7U%3D'.
2025-05-20 14:05:28.471 [INFO] 22888 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:251] : Welab privacy config cache info key set : [cs_bot:black_list, cs_bot:face_detection, cs_bot:session_detail_report, auth:admin_user, auth:organization, auth:user, qa_bot:user, qa_bot:organization, qa_bot:admin_user, cs_bot:queue_list, cs_bot:session_list, cs_bot:message_event, webot-v2:conversations, webot-v2:histories, webot-v2:feedbacks, cs_bot:call_detail, qa_bot:call_detail, cs_bot:black_list_test, cs_bot:user_verification_history]
2025-05-20 14:05:30.059 [INFO] 22888 [   threadPool-3] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:05:30.079 [INFO] 22888 [   threadPool-3] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 19 ms
2025-05-20 14:05:50.060 [INFO] 22888 [   threadPool-4] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:05:50.086 [INFO] 22888 [   threadPool-4] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 24 ms
2025-05-20 14:06:10.065 [INFO] 22888 [   threadPool-5] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:06:10.090 [INFO] 22888 [   threadPool-5] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 23 ms
2025-05-20 14:06:28.479 [INFO] 22888 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:277] : Refresh welab privacy config info local cache.
2025-05-20 14:06:28.480 [INFO] 22888 [ConfigScheduler] c.welab.privacy.manager.PrivacyManager  [PrivacyManager.java:504] : Clear privacy element manager local cache. 
2025-05-20 14:06:28.480 [INFO] 22888 [ConfigScheduler] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-online-cs&secret=%2Fgsaz1hBUZ32wbgQSJ7t6aEdi7T2c9tz2s6MR7HTn7U%3D'.
2025-05-20 14:06:28.602 [INFO] 22888 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:251] : Welab privacy config cache info key set : [cs_bot:black_list, cs_bot:face_detection, cs_bot:session_detail_report, auth:admin_user, auth:organization, auth:user, qa_bot:user, qa_bot:organization, qa_bot:admin_user, cs_bot:queue_list, cs_bot:session_list, cs_bot:message_event, webot-v2:conversations, webot-v2:histories, webot-v2:feedbacks, cs_bot:call_detail, qa_bot:call_detail, cs_bot:black_list_test, cs_bot:user_verification_history]
2025-05-20 14:06:30.051 [INFO] 22888 [   threadPool-6] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:06:30.074 [INFO] 22888 [   threadPool-6] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 22 ms
2025-05-20 14:06:50.052 [INFO] 22888 [   threadPool-7] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:06:50.072 [INFO] 22888 [   threadPool-7] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 19 ms
2025-05-20 14:07:10.052 [INFO] 22888 [   threadPool-8] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:07:10.072 [INFO] 22888 [   threadPool-8] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 19 ms
2025-05-20 14:07:28.614 [INFO] 22888 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:277] : Refresh welab privacy config info local cache.
2025-05-20 14:07:28.615 [INFO] 22888 [ConfigScheduler] c.welab.privacy.manager.PrivacyManager  [PrivacyManager.java:504] : Clear privacy element manager local cache. 
2025-05-20 14:07:28.616 [INFO] 22888 [ConfigScheduler] com.welab.privacy.util.http.HttpClients [HttpClients.java:199] : Http client get Request url is 'https://japi-fat.wolaidai.com/privacy/api/v2/config-info?appId=bot-server-online-cs&secret=%2Fgsaz1hBUZ32wbgQSJ7t6aEdi7T2c9tz2s6MR7HTn7U%3D'.
2025-05-20 14:07:28.775 [INFO] 22888 [ConfigScheduler] c.w.privacy.config.PrivacyConfigContext [PrivacyConfigContext.java:251] : Welab privacy config cache info key set : [cs_bot:black_list, cs_bot:face_detection, cs_bot:session_detail_report, auth:admin_user, auth:organization, auth:user, qa_bot:user, qa_bot:organization, qa_bot:admin_user, cs_bot:queue_list, cs_bot:session_list, cs_bot:message_event, webot-v2:conversations, webot-v2:histories, webot-v2:feedbacks, cs_bot:call_detail, qa_bot:call_detail, cs_bot:black_list_test, cs_bot:user_verification_history]
2025-05-20 14:07:30.049 [INFO] 22888 [   threadPool-1] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:07:30.075 [INFO] 22888 [   threadPool-1] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 25 ms
2025-05-20 14:07:50.053 [INFO] 22888 [   threadPool-2] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:07:50.072 [INFO] 22888 [   threadPool-2] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 19 ms
2025-05-20 14:08:10.052 [INFO] 22888 [   threadPool-3] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:81] : start checkQueueList...
2025-05-20 14:08:10.071 [INFO] 22888 [   threadPool-3] c.w.w.c.s.impl.QueueListServiceImpl     [QueueListServiceImpl.java:113] : end checkQueueList,cost 18 ms
2025-05-20 14:08:16.108 [INFO] 22888 [ionShutdownHook] o.e.jetty.server.AbstractConnector      [AbstractConnector.java:383] : Stopped ServerConnector@7884f722{HTTP/1.1, (http/1.1)}{0.0.0.0:9393}
2025-05-20 14:08:16.109 [INFO] 22888 [ionShutdownHook] org.eclipse.jetty.server.session        [HouseKeeper.java:149] : node0 Stopped scavenging
2025-05-20 14:08:16.111 [INFO] 22888 [ionShutdownHook] o.e.j.s.h.ContextHandler.application    [ContextHandler.java:2368] : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-20 14:08:16.115 [INFO] 22888 [ionShutdownHook] o.e.jetty.server.handler.ContextHandler [ContextHandler.java:1159] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@30b0de1d{application,/api,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9393.8328876176916960266/, jar:file:/D:/software/maven/apache-maven-3.8.6-bin/apache-maven-3.8.6/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar!/META-INF/resources],STOPPED}
2025-05-20 14:08:17.322 [INFO] 22888 [ionShutdownHook] j.LocalContainerEntityManagerFactoryBean[AbstractEntityManagerFactoryBean.java:651] : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-20 14:08:17.328 [INFO] 22888 [ionShutdownHook] com.zaxxer.hikari.HikariDataSource      [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-05-20 14:08:17.336 [INFO] 22888 [ionShutdownHook] com.zaxxer.hikari.HikariDataSource      [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
